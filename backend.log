Using Database Storage
Setting up session middleware with PostgreSQL store
Session middleware configured, cookie settings: {
  secure: false,
  httpOnly: true,
  sameSite: 'lax',
  path: '/',
  maxAge: '30 days'
}
[5:51:29 PM] Server is running on http://localhost:3000
GET /api/user session ID: RX8Pqjfjvzm9O9HoftCM9eOWdGAPgDMd
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:51:29.468Z,
    originalMaxAge: 2592000000,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  }
}
Session user: undefined
isAuthenticated check: false, sessionID: RX8Pqjfjvzm9O9HoftCM9eOWdGAPgDMd
Request isAuthenticated(): false
Request user: undefined
isAuthenticated check: false, sessionID: RX8Pqjfjvzm9O9HoftCM9eOWdGAPgDMd
User is not authenticated
[5:51:29 PM] GET /api/user 401 in 2ms :: {"message":"Not authenticated"}
[5:51:29 PM] GET /api/exams/active 200 in 39ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","…
[5:51:29 PM] GET /api/exams/active 200 in 49ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","…
