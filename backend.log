Using Database Storage
Setting up session middleware with PostgreSQL store
Session middleware configured, cookie settings: {
  secure: false,
  httpOnly: true,
  sameSite: 'lax',
  path: '/',
  maxAge: '30 days'
}
Server startup error: TypeError: OAuth2Strategy requires a clientID option
    at Strategy.OAuth2Strategy (/Users/<USER>/Downloads/EPSSimulator2/node_modules/passport-oauth2/lib/strategy.js:87:34)
    at new Strategy (/Users/<USER>/Downloads/EPSSimulator2/node_modules/passport-google-oauth20/lib/strategy.js:52:18)
    at <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes/auth.ts:14:14)
    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)
    at async registerRoutes (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:672:25)
    at async <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/index.ts:72:20)
