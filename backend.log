Using Database Storage
Setting up session middleware with PostgreSQL store
Session middleware configured, cookie settings: {
  secure: false,
  httpOnly: true,
  sameSite: 'lax',
  path: '/',
  maxAge: '30 days'
}
Setting up Google OAuth strategy...
[9:21:45 AM] Server is running on http://localhost:3000
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.vtm_s6I7MPjyNri_54OqSKGUwjhIWahPHHnBbuPUyLw
Session ID: LigVbfNNiUszKlVJnSYzFWxad1VcGTou
Session user: undefined
Found JWT token, verifying...
JWT verification failed: invalid signature
Trying session-based authentication...
isAuthenticated check: false, sessionID: LigVbfNNiUszKlVJnSYzFWxad1VcGTou
User is not authenticated
[9:21:51 AM] GET /api/user 401 in 10ms :: {"message":"Not authenticated"}
Refresh token request received
Refresh token error: JsonWebTokenError: invalid signature
    at /Users/<USER>/Downloads/EPSSimulator2/node_modules/jsonwebtoken/verify.js:171:19
    at getSecret (/Users/<USER>/Downloads/EPSSimulator2/node_modules/jsonwebtoken/verify.js:97:14)
    at module.exports [as verify] (/Users/<USER>/Downloads/EPSSimulator2/node_modules/jsonwebtoken/verify.js:101:10)
    at verifyRefreshToken (/Users/<USER>/Downloads/EPSSimulator2/server/utils/jwt.ts:26:14)
    at <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:696:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[9:21:51 AM] POST /api/refresh-token 401 in 61ms :: {"message":"Invalid refresh token"}
GET /api/user - checking authentication...
Authorization header: undefined
Session ID: Q_2seCE5Y3GIXwxIAuVVj1pDwy1mdCYl
Session user: undefined
Trying session-based authentication...
isAuthenticated check: false, sessionID: Q_2seCE5Y3GIXwxIAuVVj1pDwy1mdCYl
User is not authenticated
[9:21:51 AM] GET /api/user 401 in 0ms :: {"message":"Not authenticated"}
Login attempt for username: admin
Session ID at login start: NIfHW0VN9qu5L84EC8FWZ3v7pKnu2eCE
User found: admin, ID: 15, Role: admin
Password format check: {
  hasPassword: true,
  passwordLength: 161,
  includesDot: true,
  startsWithBcrypt: false
}
Custom hash password comparison failed
Login failed: Invalid password
[9:22:17 AM] POST /api/login 401 in 119ms :: {"message":"Invalid username or password"}
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.vtm_s6I7MPjyNri_54OqSKGUwjhIWahPHHnBbuPUyLw
Session ID: SLfLP96Zyb_xbUeWX-iw5JglgpvV6iTr
Session user: undefined
Found JWT token, verifying...
JWT verification failed: invalid signature
Trying session-based authentication...
isAuthenticated check: false, sessionID: SLfLP96Zyb_xbUeWX-iw5JglgpvV6iTr
User is not authenticated
[9:27:55 AM] GET /api/user 401 in 11ms :: {"message":"Not authenticated"}
Refresh token request received
Refresh token error: JsonWebTokenError: invalid signature
    at /Users/<USER>/Downloads/EPSSimulator2/node_modules/jsonwebtoken/verify.js:171:19
    at getSecret (/Users/<USER>/Downloads/EPSSimulator2/node_modules/jsonwebtoken/verify.js:97:14)
    at module.exports [as verify] (/Users/<USER>/Downloads/EPSSimulator2/node_modules/jsonwebtoken/verify.js:101:10)
    at verifyRefreshToken (/Users/<USER>/Downloads/EPSSimulator2/server/utils/jwt.ts:26:14)
    at <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:696:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[9:27:55 AM] POST /api/refresh-token 401 in 17ms :: {"message":"Invalid refresh token"}
GET /api/user - checking authentication...
Authorization header: undefined
Session ID: x_QdixEaq9nZwYpHuppOHyL_wgFlnEdI
Session user: undefined
Trying session-based authentication...
isAuthenticated check: false, sessionID: x_QdixEaq9nZwYpHuppOHyL_wgFlnEdI
User is not authenticated
[9:27:55 AM] GET /api/user 401 in 3ms :: {"message":"Not authenticated"}
Login attempt for username: admin
Session ID at login start: KdrEcGs8gid3Y4K3VmRfaVsLLo3jf6g5
User found: admin, ID: 15, Role: admin
Password format check: {
  hasPassword: true,
  passwordLength: 161,
  includesDot: true,
  startsWithBcrypt: false
}
Password matched using custom hash.salt format
JWT tokens generated successfully
User saved to regenerated session: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: 2025-05-27T14:52:45.877Z,
  emailVerified: false,
  createdAt: 2025-05-07T21:26:08.048Z,
  updatedAt: 2025-05-07T21:26:08.048Z
}
Session saved successfully, new session ID: yTSjnDT0CAk4XkkvIDqdewXO9H0CxWXR
Login successful, returning user data with tokens
[9:28:03 AM] POST /api/login 200 in 137ms :: {"id":15,"username":"admin","email":"admin@exam…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.5Vqo2mRAwtz7ejk582P6MDbl1xDUaz5Nv0MhClkk9OE
Session ID: yTSjnDT0CAk4XkkvIDqdewXO9H0CxWXR
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-27T14:52:45.877Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:28:03 AM] GET /api/user 200 in 5ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.5Vqo2mRAwtz7ejk582P6MDbl1xDUaz5Nv0MhClkk9OE
Session ID: yTSjnDT0CAk4XkkvIDqdewXO9H0CxWXR
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-27T14:52:45.877Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:28:03 AM] GET /api/user 304 in 5ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: yTSjnDT0CAk4XkkvIDqdewXO9H0CxWXR
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: yTSjnDT0CAk4XkkvIDqdewXO9H0CxWXR
[9:28:03 AM] GET /api/dashboard/stats 304 in 102ms :: {"usersCount":3,"activeUsersCount":3,"…
[9:28:03 AM] GET /api/dashboard/stats 304 in 18ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
Logout attempt, session ID: yTSjnDT0CAk4XkkvIDqdewXO9H0CxWXR
Logging out user: admin, ID: 15
Session destroyed successfully, user logged out
[9:28:06 AM] POST /api/logout 200 in 2ms :: {"message":"Logged out successfully"}
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.5Vqo2mRAwtz7ejk582P6MDbl1xDUaz5Nv0MhClkk9OE
Session ID: jOrn64kQL91XfJthAoQBBFIY4CyMeBWU
Session user: undefined
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:28:06 AM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
[9:28:08 AM] GET /api/auth/google 302 in 3ms
Login attempt for username: admin
Session ID at login start: 6wIHYS0aP5mdkllasJbqgAgjpy1jAdju
User found: admin, ID: 15, Role: admin
Password format check: {
  hasPassword: true,
  passwordLength: 161,
  includesDot: true,
  startsWithBcrypt: false
}
Password matched using custom hash.salt format
JWT tokens generated successfully
User saved to regenerated session: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: 2025-06-01T02:28:02.958Z,
  emailVerified: false,
  createdAt: 2025-05-07T21:26:08.048Z,
  updatedAt: 2025-05-07T21:26:08.048Z
}
Session saved successfully, new session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Login successful, returning user data with tokens
[9:28:40 AM] POST /api/login 200 in 107ms :: {"id":15,"username":"admin","email":"admin@exam…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:28:40 AM] GET /api/user 200 in 7ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:28:41 AM] GET /api/user 304 in 4ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:28:41 AM] GET /api/dashboard/stats 304 in 29ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:28:41 AM] GET /api/dashboard/stats 304 in 23ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:28:42 AM] GET /api/exams 304 in 11ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
[9:28:42 AM] GET /api/exams 304 in 14ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:28:45 AM] GET /api/exams/10 304 in 5ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[9:28:45 AM] GET /api/exams/10 304 in 4ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[9:28:49 AM] GET /api/direct-url/gdrive 200 in 4122ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[9:28:57 AM] GET /api/proxy/gdrive 200 in 7437ms
User attached to request: admin, role: admin
User attached to request: admin, role: admin
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
[9:29:35 AM] GET /api/exams/10 304 in 21ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descr…
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
[9:29:35 AM] GET /api/exams/10 304 in 8ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
JWT user found in database: admin
[9:29:35 AM] GET /api/user 304 in 9ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:30:37 AM] GET /api/user 304 in 12ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:31:20 AM] GET /api/dashboard/stats 304 in 32ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:31:20 AM] GET /api/dashboard/stats 304 in 25ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
[9:31:24 AM] GET /api/exams 304 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
[9:31:24 AM] GET /api/exams 304 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:36:52 AM] GET /api/user 304 in 11ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:36:53 AM] GET /api/user 304 in 4ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:36:53 AM] GET /api/dashboard/stats 304 in 21ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:36:53 AM] GET /api/dashboard/stats 304 in 14ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:39:13 AM] GET /api/exams 304 in 11ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
[9:39:13 AM] GET /api/exams 304 in 13ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:39:15 AM] GET /api/exams/10 304 in 9ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[9:39:15 AM] GET /api/exams/10 304 in 7ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[9:39:18 AM] GET /api/direct-url/gdrive 304 in 3393ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:39:22 AM] GET /api/user 304 in 7ms :: {"id":15,"username":"admin","email":"admin@example.…
[9:39:25 AM] GET /api/proxy/gdrive 200 in 6705ms
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:39:44 AM] GET /api/dashboard/stats 304 in 33ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:39:44 AM] GET /api/dashboard/stats 304 in 31ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:39:52 AM] GET /api/user 304 in 5ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:39:52 AM] GET /api/dashboard/stats 304 in 9ms :: {"usersCount":3,"activeUsersCount":3,"ex…
[9:39:52 AM] GET /api/dashboard/stats 304 in 8ms :: {"usersCount":3,"activeUsersCount":3,"ex…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:39:58 AM] GET /api/user 304 in 7ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:41:20 AM] GET /api/exams 304 in 10ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
[9:41:20 AM] GET /api/exams 304 in 16ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:41:24 AM] GET /api/exams 304 in 8ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[9:41:24 AM] GET /api/exams 304 in 8ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:41:29 AM] GET /api/exams/10 304 in 3ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[9:41:29 AM] GET /api/exams/10 304 in 3ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[9:41:32 AM] GET /api/direct-url/gdrive 304 in 3431ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[9:41:39 AM] GET /api/proxy/gdrive 200 in 6358ms
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:41:43 AM] GET /api/dashboard/stats 304 in 13ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:41:43 AM] GET /api/dashboard/stats 304 in 34ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:41:44 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[9:41:44 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:41:46 AM] GET /api/exams/10 304 in 4ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[9:41:46 AM] GET /api/exams/10 304 in 4ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:42:29 AM] GET /api/exams/10 304 in 11ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descr…
[9:42:29 AM] GET /api/exams/10 304 in 9ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:42:31 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[9:42:31 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:42:33 AM] GET /api/user 304 in 14ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:42:33 AM] GET /api/dashboard/stats 304 in 28ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:42:33 AM] GET /api/dashboard/stats 304 in 32ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:42:35 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[9:42:35 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:42:36 AM] GET /api/exams/10 304 in 10ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descr…
[9:42:36 AM] GET /api/exams/10 304 in 7ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[9:42:40 AM] GET /api/direct-url/gdrive 304 in 3432ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[9:42:47 AM] GET /api/proxy/gdrive 200 in 6339ms
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:43:06 AM] GET /api/dashboard/stats 304 in 25ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:43:06 AM] GET /api/dashboard/stats 304 in 21ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:43:07 AM] GET /api/exams 304 in 7ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[9:43:07 AM] GET /api/exams 304 in 7ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:43:10 AM] GET /api/exams/11 304 in 5ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[9:43:10 AM] GET /api/exams/11 304 in 5ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[9:43:55 AM] GET /api/user 304 in 14ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:50:09 AM] GET /api/exams/11 304 in 5ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[9:50:09 AM] GET /api/exams/11 304 in 3ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[9:50:12 AM] GET /api/dashboard/stats 304 in 23ms :: {"usersCount":3,"activeUsersCount":3,"e…
[9:50:12 AM] GET /api/dashboard/stats 304 in 28ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:50:14 AM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[9:50:14 AM] GET /api/exams 304 in 7ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[9:50:15 AM] GET /api/exams/10 304 in 4ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[9:50:15 AM] GET /api/exams/10 304 in 3ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[10:05:17 AM] GET /api/user 304 in 24ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT token verified: {
  userId: 15,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin',
  iat: **********,
  exp: **********
}
JWT user found in database: admin
[10:05:19 AM] GET /api/user 304 in 7ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[10:05:19 AM] GET /api/exams/10 304 in 72ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descr…
[10:05:19 AM] GET /api/exams/10 304 in 66ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descr…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[10:05:22 AM] GET /api/direct-url/gdrive 304 in 3640ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
[10:05:29 AM] GET /api/dashboard/stats 304 in 29ms :: {"usersCount":3,"activeUsersCount":3,"e…
[10:05:29 AM] GET /api/dashboard/stats 304 in 26ms :: {"usersCount":3,"activeUsersCount":3,"e…
[10:05:30 AM] GET /api/proxy/gdrive 200 in 6695ms
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[10:40:28 AM] GET /api/exams 304 in 17ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
[10:40:28 AM] GET /api/exams 304 in 20ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT verification failed: jwt expired
Trying session-based authentication...
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user is authenticated, returning user data
[10:40:29 AM] GET /api/user 200 in 19ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[10:40:33 AM] GET /api/exams/10 304 in 6ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[10:40:33 AM] GET /api/exams/10 304 in 7ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT verification failed: jwt expired
Trying session-based authentication...
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user is authenticated, returning user data
[10:47:22 AM] GET /api/user 304 in 17ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT verification failed: jwt expired
Trying session-based authentication...
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user is authenticated, returning user data
[10:47:24 AM] GET /api/user 304 in 7ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[10:47:24 AM] GET /api/exams/10 304 in 9ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[10:47:24 AM] GET /api/exams/10 304 in 3ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[10:47:27 AM] GET /api/direct-url/gdrive 304 in 3343ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[10:47:35 AM] GET /api/proxy/gdrive 200 in 7221ms
User attached to request: admin, role: admin
[10:49:55 AM] GET /api/exams/10 304 in 13ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descr…
User attached to request: admin, role: admin
[10:51:30 AM] GET /api/exams/10 304 in 5ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT verification failed: jwt expired
Trying session-based authentication...
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user is authenticated, returning user data
[10:53:35 AM] GET /api/user 304 in 12ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
[10:53:35 AM] GET /api/exams/10 304 in 7ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
[10:53:35 AM] GET /api/exams/10 304 in 3ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[10:53:39 AM] GET /api/direct-url/gdrive 304 in 4034ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[10:53:46 AM] GET /api/proxy/gdrive 200 in 6507ms
User attached to request: admin, role: admin
GET /api/user - checking authentication...
Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.-IM3sI4tO5nP-vEHlk5Cg_5o5qut-b6-s8fOjM7ITm0
Session ID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-06-01T02:28:02.958Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
Found JWT token, verifying...
JWT verification failed: jwt expired
Trying session-based authentication...
isAuthenticated check: true, sessionID: voVBsxpQu__gmpNTnkI4M1Ujxs9gAF6S
Session user is authenticated, returning user data
[10:54:45 AM] GET /api/user 200 in 10ms :: {"id":15,"username":"admin","email":"admin@example…
User attached to request: admin, role: admin
[10:54:45 AM] GET /api/exams/10 200 in 8ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
[10:54:45 AM] GET /api/exams/10 200 in 6ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[10:54:49 AM] GET /api/direct-url/gdrive 200 in 4039ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[10:54:57 AM] GET /api/proxy/gdrive 200 in 7313ms
