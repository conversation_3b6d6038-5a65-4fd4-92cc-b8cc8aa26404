Using Database Storage
Setting up session middleware with PostgreSQL store
Session middleware configured, cookie settings: {
  secure: false,
  httpOnly: true,
  sameSite: 'lax',
  path: '/',
  maxAge: '30 days'
}
[9:04:45 PM] Server is running on http://localhost:3000
GET /api/user session ID: OIbrdRGtxNa1dofl9rsayB0z0U-5a4no
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-26T14:04:49.190Z,
    originalMaxAge: 2592000000,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  }
}
Session user: undefined
isAuthenticated check: false, sessionID: OIbrdRGtxNa1dofl9rsayB0z0U-5a4no
Request isAuthenticated(): false
Request user: undefined
isAuthenticated check: false, sessionID: OIbrdRGtxNa1dofl9rsayB0z0U-5a4no
User is not authenticated
[9:04:49 PM] GET /api/user 401 in 3ms :: {"message":"Not authenticated"}
