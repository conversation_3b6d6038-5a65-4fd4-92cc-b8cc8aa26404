Using Database Storage
Setting up session middleware with PostgreSQL store
Session middleware configured, cookie settings: {
  secure: false,
  httpOnly: true,
  sameSite: 'lax',
  path: '/',
  maxAge: '30 days'
}
Re-optimizing dependencies because vite config has changed
[4:52:36 PM] Server is running on http://localhost:3000
GET /api/user session ID: qhxCGexdO0MqclD2R_2N_Z7H0VH3m6gR
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:52:48.593Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  }
}
Session user: undefined
isAuthenticated check: false, sessionID: qhxCGexdO0MqclD2R_2N_Z7H0VH3m6gR
Request isAuthenticated(): false
Request user: undefined
isAuthenticated check: false, sessionID: qhxCGexdO0MqclD2R_2N_Z7H0VH3m6gR
User is not authenticated
[4:52:48 PM] GET /api/user 401 in 7ms :: {"message":"Not authenticated"}
[4:52:48 PM] GET /api/exams/active 304 in 59ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","…
[4:52:48 PM] GET /api/exams/active 304 in 71ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","…
Login attempt for username: admin
Session ID at login start: y67jdKpvDuRhB1KKlnjYGY_XY5_TjFq0
User found: admin, ID: 15, Role: admin
Password matched using secure comparison
User saved to regenerated session: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: 2025-05-23T14:21:13.387Z,
  emailVerified: false,
  createdAt: 2025-05-07T21:26:08.048Z,
  updatedAt: 2025-05-07T21:26:08.048Z
}
Session saved successfully, new session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Login successful, returning user data
[4:53:05 PM] POST /api/login 200 in 115ms :: {"id":15,"username":"admin","email":"admin@exam…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:53:05.634Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:53:05 PM] GET /api/user 200 in 6ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[4:53:05 PM] GET /api/exam-attempts 304 in 1ms :: []
User attached to request: admin, role: admin
[4:53:05 PM] GET /api/exams/active 304 in 3ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","d…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:53:05.665Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:53:05 PM] GET /api/user 304 in 1ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[4:53:05 PM] GET /api/dashboard/stats 304 in 113ms :: {"usersCount":3,"activeUsersCount":3,"…
[4:53:05 PM] GET /api/dashboard/stats 304 in 117ms :: {"usersCount":3,"activeUsersCount":3,"…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:53:08 PM] GET /api/exams 304 in 9ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[4:53:08 PM] GET /api/exams 304 in 9ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:53:18 PM] GET /api/exams/10 304 in 4ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[4:53:18 PM] GET /api/exams/10 304 in 4ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[4:53:32 PM] GET /api/direct-url/gdrive 304 in 13496ms :: {"url":"https://docs.google.com/uc…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:53:32.045Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:54:16 PM] GET /api/user 304 in 1ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[4:54:16 PM] GET /api/dashboard/stats 304 in 20ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[4:54:16 PM] GET /api/dashboard/stats 304 in 5ms :: {"usersCount":3,"activeUsersCount":3,"ex…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:54:16.774Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:54:16 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:54:18 PM] GET /api/exams 304 in 6ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[4:54:18 PM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:54:19 PM] GET /api/exams/10 304 in 6ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
[4:54:19 PM] GET /api/exams/10 304 in 5ms :: {"id":10,"title":"EPS-TOPIK 기출 모의고사 1회","descri…
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:54:22 PM] GET /api/exams 304 in 13ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
[4:54:22 PM] GET /api/exams 304 in 13ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descrip…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:54:24 PM] GET /api/exams/11 304 in 3ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[4:54:24 PM] GET /api/exams/11 304 in 6ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Error verifying file access: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at async <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:854:26) {
  [cause]: Error: read ETIMEDOUT
      at TLSWrap.onStreamRead (node:internal/stream_base_commons:216:20) {
    errno: -60,
    code: 'ETIMEDOUT',
    syscall: 'read'
  }
}
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[4:54:42 PM] GET /api/direct-url/gdrive 304 in 84321ms :: {"url":"https://docs.google.com/uc…
Error verifying file access: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at async <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:854:26) {
  [cause]: Error: read ETIMEDOUT
      at TLSWrap.onStreamRead (node:internal/stream_base_commons:216:20) {
    errno: -60,
    code: 'ETIMEDOUT',
    syscall: 'read'
  }
}
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[4:55:43 PM] GET /api/direct-url/gdrive 304 in 70984ms :: {"url":"https://docs.google.com/uc…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:55:43.645Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:57:12 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:57:12.824Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:57:27 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:57:27.490Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:57:29 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[4:57:29 PM] GET /api/dashboard/stats 304 in 25ms :: {"usersCount":3,"activeUsersCount":3,"e…
[4:57:29 PM] GET /api/dashboard/stats 304 in 15ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:57:30 PM] GET /api/exams 304 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[4:57:30 PM] GET /api/exams 304 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:57:32 PM] GET /api/exams/11 304 in 4ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[4:57:32 PM] GET /api/exams/11 304 in 6ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[4:57:48 PM] GET /api/direct-url/gdrive 304 in 11904ms :: {"url":"https://docs.google.com/uc…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:57:48.622Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:57:51 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:57:51.957Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:58:03 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:58:03.986Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:58:22 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:58:22.300Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:58:33 PM] GET /api/user 304 in 5ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:58:33.539Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:58:37 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[4:58:37 PM] GET /api/dashboard/stats 304 in 28ms :: {"usersCount":3,"activeUsersCount":3,"e…
[4:58:37 PM] GET /api/dashboard/stats 304 in 17ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:58:38 PM] GET /api/exams 304 in 7ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[4:58:38 PM] GET /api/exams 304 in 9ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[4:58:41 PM] GET /api/exams/11 304 in 5ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[4:58:41 PM] GET /api/exams/11 304 in 5ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[4:58:48 PM] GET /api/direct-url/gdrive 304 in 5373ms :: {"url":"https://docs.google.com/uc?…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:58:48.787Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:58:50 PM] GET /api/user 304 in 1ms :: {"id":15,"username":"admin","email":"admin@example.…
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:58:50.311Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:59:13 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:59:13.443Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[4:59:59 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:59:59.801Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:00:06 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[5:00:06 PM] GET /api/dashboard/stats 304 in 26ms :: {"usersCount":3,"activeUsersCount":3,"e…
[5:00:06 PM] GET /api/dashboard/stats 304 in 13ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:00:06.111Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:00:06 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[5:00:07 PM] GET /api/exams 304 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[5:00:07 PM] GET /api/exams 304 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[5:00:09 PM] GET /api/exams/11 304 in 4ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[5:00:09 PM] GET /api/exams/11 304 in 4ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Error verifying file access: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at process.processImmediate (node:internal/timers:473:9)
    at async <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:854:26) {
  [cause]: ConnectTimeoutError: Connect Timeout Error (attempted address: docs.google.com:443, timeout: 10000ms)
      at onConnectTimeout (node:internal/deps/undici/undici:2602:28)
      at Immediate._onImmediate (node:internal/deps/undici/undici:2583:11)
      at process.processImmediate (node:internal/timers:505:21) {
    code: 'UND_ERR_CONNECT_TIMEOUT'
  }
}
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Error verifying file access: TypeError: fetch failed
    at node:internal/deps/undici/undici:13510:13
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/Users/<USER>/Downloads/EPSSimulator2/server/routes.ts:854:26) {
  [cause]: ConnectTimeoutError: Connect Timeout Error (attempted address: docs.google.com:443, timeout: 10000ms)
      at onConnectTimeout (node:internal/deps/undici/undici:2602:28)
      at Immediate._onImmediate (node:internal/deps/undici/undici:2583:11)
      at process.processImmediate (node:internal/timers:505:21) {
    code: 'UND_ERR_CONNECT_TIMEOUT'
  }
}
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[5:00:20 PM] GET /api/direct-url/gdrive 304 in 10165ms :: {"url":"https://docs.google.com/uc…
[5:00:20 PM] GET /api/direct-url/gdrive 304 in 10168ms :: {"url":"https://docs.google.com/uc…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:00:20.072Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:00:29 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
Streaming file with content type: audio/mpeg, size: 20720880
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:00:29.837Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:00:38 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:00:38.364Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:00:59 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User attached to request: admin, role: admin
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
[5:00:59 PM] GET /api/dashboard/stats 304 in 21ms :: {"usersCount":3,"activeUsersCount":3,"e…
[5:00:59 PM] GET /api/dashboard/stats 304 in 14ms :: {"usersCount":3,"activeUsersCount":3,"e…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:00:59.683Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:00:59 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[5:01:01 PM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
[5:01:01 PM] GET /api/exams 304 in 5ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[5:01:03 PM] GET /api/exams/11 304 in 9ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[5:01:03 PM] GET /api/exams/11 304 in 9ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
Verifying file access for ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:01:03.199Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:01:12 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
Streaming file with content type: audio/mpeg, size: 20720880
Streaming file with content type: audio/mpeg, size: 20720880
File verified. Content-Type: audio/mpeg
Generated direct URL for file ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[5:01:16 PM] GET /api/direct-url/gdrive 304 in 12074ms :: {"url":"https://docs.google.com/uc…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[5:01:23 PM] HEAD /api/proxy/gdrive 200 in 6523ms
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
[5:01:29 PM] GET /api/proxy/gdrive 200 in 5508ms
Streaming file with content type: audio/mpeg, size: 20720880
[5:01:32 PM] GET /api/proxy/gdrive 200 in 5961ms
[5:01:37 PM] HEAD /api/proxy/gdrive 200 in 352591ms
[5:01:45 PM] HEAD /api/proxy/gdrive 200 in 222870ms
[5:01:51 PM] HEAD /api/proxy/gdrive 200 in 181741ms
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T09:58:53.405Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:02:38 PM] GET /api/user 304 in 3ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
Received exam update request: {
  id: 11,
  readingTime: 1500,
  listeningTime: 1295,
  body: {
    title: 'EPS-TOPIK 실전 모의고사 2회',
    description: '한국 산업 현장에서 자주 사용되는 용어와 상황 중심의 실전 모의고사입니다.',
    readingTime: 1500,
    listeningTime: 1295,
    duration: 47,
    isActive: true,
    listeningAudioUrl: 'https://drive.google.com/file/d/1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6/view?usp=share_link',
    listeningAudioDuration: 1295,
    questions: [
      [Object], [Object], [Object],
      [Object], [Object], [Object],
      [Object], [Object], [Object],
      [Object], [Object], [Object],
      [Object], [Object], [Object],
      [Object], [Object], [Object],
      [Object], [Object]
    ]
  }
}
After conversion: { readingTime: 1500, listeningTime: 1295 }
Calling storage.updateExam with: {
  id: 11,
  body: `{"title":"EPS-TOPIK 실전 모의고사 2회","description":"한국 산업 현장에서 자주 사용되는 용어와 상황 중심의 실전 모의고사입니다.","readingTime":1500,"listeningTime":1295,"duration":47,"isActive":true,"listeningAudioUrl":"https://drive.google.com/file/d/1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6/view?usp=share_link","listeningAudioDuration":1295,"questions":[{"id":1,"display_index":1,"type":"reading","text":"다음 표지판의 의미로 맞는 것을 고르십시오.","subText":"","correctAnswer":1,"start_at":0,"options":[{"id":1,"text":"위험! 고압 전기"},{"id":2,"text":"여기서 휴식하세요"},{"id":3,"text":"전기 사용 가능"},{"id":4,"text":"조명 스위치"}]},{"id":2,"display_index":2,"type":"reading","text":"다음 중 작업장에서 '금지'되는 행동을 고르십시오.","subText":"","hasImageOptions":true,"correctAnswer":2,"start_at":0,"options":[{"id":1,"imageUrl":"https://i.ibb.co/0MZzMKd/wearing-mask.png"},{"id":2,"imageUrl":"https://i.ibb.co/47kxnxZ/using-phone.png"},{"id":3,"imageUrl":"https://i.ibb.co/FhVVnMg/washing-hands.png"},{"id":4,"imageUrl":"https://i.ibb.co/LPdYnCZ/wearing-helmet.png"}]},{"id":3,"display_index":3,"type":"reading","text":"다음 안내문의 내용과 일치하는 것을 고르십시오.","subText":"점심 시간은 12시부터 1시까지입니다. 식사 후에는 반드시，식당 뒷정리를 해 주세요.","correctAnswer":1,"start_at":0,"options":[{"id":1,"text":"점심 시간은 1시간입니다."},{"id":2,"text":"점심은 11시에 시작합니다."},{"id":3,"text":"식사 후 설거지는 다른 사람이 합니다."},{"id":4,"text":"식당은 오후 2시에 문을 닫습니다."}]},{"id":4,"display_index":4,"type":"reading","text":"다음 두 사람의 대화 내용으로 보아 남자가 해야 할 일이 아닌 것은 무엇입니까?","subText":"여자: 철수 씨, 제품이 완성되면 검사하고 포장해 주세요.\\n남자: 네, 알겠습니다. 그리고 납품 일정도 확인해야 하나요?\\n여자: 아니요, 납품 일정은 제가 확인할게요. 그냥 검사와 포장만 해 주세요.","correctAnswer":4,"start_at":0,"options":[{"id":1,"text":"제품 완성하기"},{"id":2,"text":"제품 검사하기"},{"id":3,"text":"제품 포장하기"},{"id":4,"text":"납품 일정 확인하기"}]},{"id":5,"display_index":5,"type":"reading","text":"다음 글에서 알 수 있는 내용으로 알맞은 것을 고르십시오.","subText":"저는 한국에서 3년 동안 제조업체에서 일했습니다. 처음에는 한국어를 잘 몰라서 어려웠지만 지금은 많이 익숙해졌습니다. 한국에서 돈을 모아 고향에 집을 짓고 싶습니다.","correctAnswer":3,"start_at":0,"options":[{"id":1,"text":"현재 한국어를 공부하고 있습니다."},{"id":2,"text":"한국에서 1년 동안 일했습니다."},{"id":3,"text":"고향에 집을 짓고 싶어합니다."},{"id":4,"text":"공장에서 일하는 것이 쉽습니다."}]},{"id":6,"display_index":6,"type":"reading","text":"다음 중 기계를 작동하기 전에 확인해야 할 사항을 고르십시오.","subText":"","correctAnswer":4,"start_at":0,"options":[{"id":1,"text":"안전장비 착용 여부"},{"id":2,"text":"기계 상태 점검"},{"id":3,"text":"작업 환경 확인"},{"id":4,"text":"모든 위 사항"}]},{"id":7,"display_index":7,"type":"reading","text":"다음 중 기계 작동 중 주의해야 할 사항을 고르십시오.","subText":"","correctAnswer":4,"start_at":0,"options":[{"id":1,"text":"기계 소음에 주의하기"},{"id":2,"text":"기계 상태 모니터링하기"},{"id":3,"text":"갑작스러운 정지에 대비하기"},{"id":4,"text":"모든 위 사항"}]},{"id":8,"display_index":8,"type":"reading","text":"다음 중 화재 발생 시 해야 할 일을 고르십시오.","subText":"","correctAnswer":2,"start_at":0,"options":[{"id":1,"text":"소화기로 불을 끄고 대피하기"},{"id":2,"text":"바로 대피하기"},{"id":3,"text":"담당자에게 보고하고 대피하기"},{"id":4,"text":"불이 작으면 계속 작업하기"}]},{"id":9,"display_index":9,"type":"reading","text":"다음 중 작업장에서 발생할 수 있는 사고의 원인을 고르십시오.","subText":"","correctAnswer":4,"start_at":0,"options":[{"id":1,"text":"안전장비 미착용"},{"id":2,"text":"기계 조작 미숙"},{"id":3,"text":"작업 환경 불량"},{"id":4,"text":"모든 위 사항"}]},{"id":10,"display_index":10,"type":"reading","text":"다음 중 작업장에서 지켜야 할 안전 수칙을 고르십시오.","subText":"","correctAnswer":4,"start_at":0,"options":[{"id":1,"text":"안전장비 착용하기"},{"id":2,"text":"작업 지시 따르기"},{"id":3,"text":"비상구 확인하기"},{"id":4,"text":"모든 위 사항"}]},{"id":11,"display_index":11,"type":"listening","text":"들은 내용과 일치하는 그림을 고르십시오.","subText":"오디오: '건물 2층에서 불이 났습니다. 모든 직원은 즉시 대피하십시오.'","hasImageOptions":true,"correctAnswer":2,"start_at":0,"options":[{"id":1,"imageUrl":"https://i.ibb.co/FXk0mbF/emergency-exit.png"},{"id":2,"imageUrl":"https://i.ibb.co/wB6RFqM/fire-alarm.png"},{"id":3,"imageUrl":"https://i.ibb.co/9rxkT3D/building-construction.png"},{"id":4,"imageUrl":"https://i.ibb.co/CJshpX7/elevator.png"}]},{"id":12,"display_index":12,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":20,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":13,"display_index":13,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":59,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":14,"display_index":14,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":103,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":15,"display_index":15,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":137,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":16,"display_index":16,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":278,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":17,"display_index":17,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":431,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":18,"display_index":18,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":605,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":19,"display_index":19,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":811,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]},{"id":20,"display_index":20,"type":"listening","text":"Câu mới","subText":"","correctAnswer":1,"start_at":1202,"options":[{"id":1,"text":"Câu mới"},{"id":2,"text":"Câu mới"},{"id":3,"text":"Câu mới"},{"id":4,"text":"Câu mới"}]}]}`
}
Updated exam: {
  id: 11,
  readingTime: 1500,
  listeningTime: 1295,
  listeningAudioDuration: 1295
}
[5:03:20 PM] PATCH /api/exams/11 200 in 24ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","des…
User attached to request: admin, role: admin
[5:03:20 PM] GET /api/exams 200 in 4ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
[5:03:20 PM] GET /api/exams 200 in 7ms :: [{"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descript…
User attached to request: admin, role: admin
User attached to request: admin, role: admin
[5:03:28 PM] GET /api/exams/11 200 in 6ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
[5:03:28 PM] GET /api/exams/11 200 in 5ms :: {"id":11,"title":"EPS-TOPIK 실전 모의고사 2회","descri…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
Streaming file with content type: audio/mpeg, size: 20720880
[5:03:40 PM] GET /api/proxy/gdrive 200 in 12642ms
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:03:30.855Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:03:47 PM] GET /api/user 304 in 2ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
[5:04:59 PM] GET /api/proxy/gdrive 200 in 49065ms
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:04:18.012Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:07:39 PM] GET /api/user 304 in 4ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:07:39.385Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:07:58 PM] GET /api/user 304 in 4ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
GET /api/user session ID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Session data: Session {
  cookie: {
    path: '/',
    _expires: 2025-06-24T10:07:58.017Z,
    originalMaxAge: **********,
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  },
  user: {
    id: 15,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'admin',
    status: 'active',
    authProvider: 'local',
    googleId: null,
    profilePicture: null,
    lastLogin: '2025-05-23T14:21:13.387Z',
    emailVerified: false,
    createdAt: '2025-05-07T21:26:08.048Z',
    updatedAt: '2025-05-07T21:26:08.048Z'
  }
}
Session user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
Request isAuthenticated(): true
Request user: {
  id: 15,
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  authProvider: 'local',
  googleId: null,
  profilePicture: null,
  lastLogin: '2025-05-23T14:21:13.387Z',
  emailVerified: false,
  createdAt: '2025-05-07T21:26:08.048Z',
  updatedAt: '2025-05-07T21:26:08.048Z'
}
isAuthenticated check: true, sessionID: UnZ8wN37aFXEQPMHJwf07iMNhUlH2Cbx
User is authenticated, returning user data
[5:09:04 PM] GET /api/user 304 in 5ms :: {"id":15,"username":"admin","email":"admin@example.…
User attached to request: admin, role: admin
Proxying file with ID: 1-0yyH5DeZ0t4w_9QseEGc-cGs_R24oP6
Streaming file with content type: audio/mpeg, size: 20720880
