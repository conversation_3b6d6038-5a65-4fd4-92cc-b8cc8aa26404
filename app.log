
> rest-express@1.0.0 dev
> NODE_ENV=development tsx server/index.ts

Using Database Storage
Setting up session middleware with PostgreSQL store
Session middleware configured, cookie settings: {
  secure: false,
  httpOnly: true,
  sameSite: 'lax',
  path: '/',
  maxAge: '30 days'
}
node:events:485
      throw er; // Unhandled 'error' event
      ^

Error: listen ENOTSUP: operation not supported on socket 0.0.0.0:5000
    at Server.setupListenHandle [as _listen2] (node:net:1917:21)
    at listenInCluster (node:net:1996:12)
    at node:net:2205:7
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'ENOTSUP',
  errno: -45,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 5000
}

Node.js v23.11.0
