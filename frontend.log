
  VITE v5.4.14  ready in 192 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
8:55:21 PM [vite] Internal server error: Failed to resolve import "./contexts/AuthContext" from "client/src/App.tsx". Does the file exist?
  Plugin: vite:import-analysis
  File: /Users/<USER>/Downloads/EPSSimulator2/client/src/App.tsx:30:24
  35 |  import RegisterPage from "./components/RegisterPage";
  36 |  import { ProtectedRoute } from "./components/ProtectedRoute";
  37 |  import { useAuth } from "./contexts/AuthContext";
     |                           ^
  38 |  export const ExamResultScreen = ({ exam, answers, score, onReturn }) => {
  39 |    const answeredCount = Object.keys(answers).length;
      at TransformPluginContext._formatError (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49257:41)
      at TransformPluginContext.error (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49252:16)
      at normalizeUrl (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64199:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64331:39
      at async Promise.all (index 22)
      at async TransformPluginContext.transform (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64258:7)
      at async PluginContainer.transform (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49098:18)
      at async loadAndTransform (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:51931:27)
8:55:21 PM [vite] Pre-transform error: Failed to resolve import "./contexts/AuthContext" from "client/src/App.tsx". Does the file exist?
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
8:55:27 PM [vite] Internal server error: Failed to resolve import "./contexts/AuthContext" from "client/src/App.tsx". Does the file exist?
  Plugin: vite:import-analysis
  File: /Users/<USER>/Downloads/EPSSimulator2/client/src/App.tsx:30:24
  35 |  import RegisterPage from "./components/RegisterPage";
  36 |  import { ProtectedRoute } from "./components/ProtectedRoute";
  37 |  import { useAuth } from "./contexts/AuthContext";
     |                           ^
  38 |  export const ExamResultScreen = ({ exam, answers, score, onReturn }) => {
  39 |    const answeredCount = Object.keys(answers).length;
      at TransformPluginContext._formatError (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49257:41)
      at TransformPluginContext.error (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49252:16)
      at normalizeUrl (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64199:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64331:39
      at async Promise.all (index 22)
      at async TransformPluginContext.transform (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64258:7)
      at async PluginContainer.transform (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49098:18)
      at async loadAndTransform (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:51931:27)
      at async viteTransformMiddleware (file:///Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:62031:24)
