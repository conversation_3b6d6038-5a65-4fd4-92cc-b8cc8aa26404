
  VITE v5.4.14  ready in 111 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
Error:   Failed to scan for dependencies from entries:
  /Users/<USER>/Downloads/EPSSimulator2/client/index.html

  [31m✘ [41;31m[[41;97mERROR[41;31m][0m [1mNo matching export in "client/src/hooks/use-toast.ts" for import "ToastProvider"[0m

    client/src/AuthWrapper.tsx:3:9:
[37m      3 │ import { [32mToastProvider[37m } from './hooks/use-toast';
        ╵          [32m~~~~~~~~~~~~~[0m


    at failureErrorWithLog (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:1472:15)
    at /Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:945:25
    at runOnEndCallbacks (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:1315:45)
    at buildResponseToResult (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:943:7)
    at /Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:955:9
    at new Promise (<anonymous>)
    at requestCallbacks.on-end (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:954:54)
    at handleRequest (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:647:17)
    at handleIncomingPacket (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:672:7)
    at Socket.readFromStdout (/Users/<USER>/Downloads/EPSSimulator2/node_modules/vite/node_modules/esbuild/lib/main.js:600:7)
Browserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
