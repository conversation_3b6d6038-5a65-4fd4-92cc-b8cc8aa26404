
  VITE v5.4.14  ready in 219 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
Browserslist: browsers data (caniuse-lite) is 8 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
9:29:35 AM [vite] hmr update /src/hooks/use-auth.tsx
9:29:35 AM [vite] hmr invalidate /src/hooks/use-auth.tsx Could not Fast Refresh ("useAuth" export is incompatible). Learn more at https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react#consistent-components-exports
9:29:35 AM [vite] hmr update /src/AuthWrapper.tsx, /src/App.tsx, /src/components/ProtectedRoute.tsx, /src/components/LoginPage.tsx, /src/components/admin/AdminPage.tsx, /src/components/RegisterPage.tsx, /src/components/user/UserDashboard.tsx
9:29:51 AM [vite] hmr update /src/components/LoginPage.tsx
9:50:06 AM [vite] hmr update /src/components/TimerSection.tsx
9:50:06 AM [vite] hmr update /src/components/QuestionArea.tsx
9:50:06 AM [vite] hmr update /src/components/SubmitButton.tsx
9:50:06 AM [vite] hmr update /src/components/QuestionNavigation.tsx
9:50:09 AM [vite] hmr update /src/App.tsx
10:49:55 AM [vite] hmr update /src/App.tsx
10:50:49 AM [vite] hmr update /src/App.tsx
10:50:49 AM [vite] Internal server error: /Users/<USER>/Downloads/EPSSimulator2/client/src/App.tsx: Unexpected token (728:8)

  726 |           )}
  727 |           {showTestEndScreen && <TestEndScreen onReturn={() => setShowTestEndScreen(false)} />}
> 728 |         </>
      |         ^
  729 |       )}
  730 |     </div>
  731 |   );
  Plugin: vite:react-babel
  File: /Users/<USER>/Downloads/EPSSimulator2/client/src/App.tsx:728:8
  726|            )}
  727|            {showTestEndScreen && <TestEndScreen onReturn={() => setShowTestEndScreen(false)} />}
  728|          </>
     |          ^
  729|        )}
  730|      </div>
      at constructor (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:360:19)
      at TypeScriptParserMixin.raise (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:3327:19)
      at TypeScriptParserMixin.unexpected (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:3347:16)
      at TypeScriptParserMixin.checkExpressionErrors (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:3734:12)
      at TypeScriptParserMixin.parseMaybeAssign (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10551:12)
      at TypeScriptParserMixin.parseMaybeAssign (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9621:20)
      at TypeScriptParserMixin.parseExpressionBase (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10475:23)
      at /Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10471:39
      at TypeScriptParserMixin.allowInAnd (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12100:12)
      at TypeScriptParserMixin.parseExpression (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10471:17)
      at TypeScriptParserMixin.jsxParseExpressionContainer (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:6805:31)
      at TypeScriptParserMixin.jsxParseElementAt (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:6884:36)
      at TypeScriptParserMixin.jsxParseElement (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:6922:17)
      at TypeScriptParserMixin.parseExprAtom (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:6932:19)
      at TypeScriptParserMixin.parseExprSubscripts (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10758:23)
      at TypeScriptParserMixin.parseUpdate (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10743:21)
      at TypeScriptParserMixin.parseMaybeUnary (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10723:23)
      at TypeScriptParserMixin.parseMaybeUnary (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9672:18)
      at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10576:61)
      at TypeScriptParserMixin.parseExprOps (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10581:23)
      at TypeScriptParserMixin.parseMaybeConditional (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10558:23)
      at TypeScriptParserMixin.parseMaybeAssign (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10521:21)
      at /Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9610:39
      at TypeScriptParserMixin.tryParse (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:3665:20)
      at TypeScriptParserMixin.parseMaybeAssign (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9610:18)
      at /Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10491:39
      at TypeScriptParserMixin.allowInAnd (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12100:12)
      at TypeScriptParserMixin.parseMaybeAssignAllowIn (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10491:17)
      at TypeScriptParserMixin.parseParenAndDistinguishExpression (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:11359:28)
      at TypeScriptParserMixin.parseExprAtom (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:11006:23)
      at TypeScriptParserMixin.parseExprAtom (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:6937:20)
      at TypeScriptParserMixin.parseExprSubscripts (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10758:23)
      at TypeScriptParserMixin.parseUpdate (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10743:21)
      at TypeScriptParserMixin.parseMaybeUnary (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10723:23)
      at TypeScriptParserMixin.parseMaybeUnary (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9672:18)
      at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10576:61)
      at TypeScriptParserMixin.parseExprOps (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10581:23)
      at TypeScriptParserMixin.parseMaybeConditional (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10558:23)
      at TypeScriptParserMixin.parseMaybeAssign (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10521:21)
      at TypeScriptParserMixin.parseMaybeAssign (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9621:20)
      at TypeScriptParserMixin.parseExpressionBase (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10475:23)
      at /Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10471:39
      at TypeScriptParserMixin.allowInAnd (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12095:16)
      at TypeScriptParserMixin.parseExpression (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:10471:17)
      at TypeScriptParserMixin.parseReturnStatement (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12786:28)
      at TypeScriptParserMixin.parseStatementContent (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12437:21)
      at TypeScriptParserMixin.parseStatementContent (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:9344:18)
      at TypeScriptParserMixin.parseStatementLike (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12406:17)
      at TypeScriptParserMixin.parseStatementListItem (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12386:17)
      at TypeScriptParserMixin.parseBlockOrModuleBlockBody (/Users/<USER>/Downloads/EPSSimulator2/node_modules/@babel/parser/lib/index.js:12960:61)
10:51:29 AM [vite] hmr update /src/App.tsx
10:54:44 AM [vite] hmr update /src/App.tsx
