
  VITE v5.4.14  ready in 219 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
Browserslist: browsers data (caniuse-lite) is 8 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
9:29:35 AM [vite] hmr update /src/hooks/use-auth.tsx
9:29:35 AM [vite] hmr invalidate /src/hooks/use-auth.tsx Could not Fast Refresh ("useAuth" export is incompatible). Learn more at https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react#consistent-components-exports
9:29:35 AM [vite] hmr update /src/AuthWrapper.tsx, /src/App.tsx, /src/components/ProtectedRoute.tsx, /src/components/LoginPage.tsx, /src/components/admin/AdminPage.tsx, /src/components/RegisterPage.tsx, /src/components/user/UserDashboard.tsx
9:29:51 AM [vite] hmr update /src/components/LoginPage.tsx
9:50:06 AM [vite] hmr update /src/components/TimerSection.tsx
9:50:06 AM [vite] hmr update /src/components/QuestionArea.tsx
9:50:06 AM [vite] hmr update /src/components/SubmitButton.tsx
9:50:06 AM [vite] hmr update /src/components/QuestionNavigation.tsx
9:50:09 AM [vite] hmr update /src/App.tsx
