Here’s a detailed, technical description of how the EPS-KLPT mock test system should function, structured for easy implementation on Replit or any other platform:

1. Core System Flow

Initialization
Fetch questions from a predefined dataset (JSON/DB).
Start a 45-minute countdown timer (format: MM:SS).
Restore incomplete progress from localStorage (if any).
Test Execution
Display one question per screen with 4 multiple-choice answers.
Track selected answers in real-time.
Submission
On timeout or manual submission → Calculate score → Show results.
2. Component-Level Specifications

A. Header Section

Timer:
Starts at 45:00, updates every second.
Visual alerts:
Turns orange at 05:00.
Blinks red at 01:00.
On timeout: Auto-submits answers.
Title: Static text: "EPS-KLPT Korean Proficiency Test".
B. Question Panel (Left)

Question Display:
Shows:
Question number (e.g., "Question 1").
Bilingual text (Korean + optional Vietnamese translation).
Underlined keywords (<u> tag).
Answer Selection:
Radio buttons for single-choice answers.
Immediate visual feedback:
Selected answer highlights in light blue.
Corresponding number in the control panel turns blue.
C. Control Panel (Right)

Question Grid (5x10):
Color coding:
White: Unvisited.
Blue: Answered.
Red border: Current question.
Functionality: Click any number to jump to that question.
Navigation Buttons:
"Previous": Disabled on Question 1.
"Next":
Shows warning if no answer selected ("Please choose an answer!").
Allows force-skipping.
"Submit":
Confirmation popup ("Are you sure you want to submit?").
If confirmed → Calculate and display results.
D. Result Screen

Score Summary:
Raw score (e.g., *"82/100"*).
Time taken.
Detailed Breakdown:
Table per question: User’s answer vs correct answer.
Accuracy by category (Listening/Reading/Vocabulary).
3. Key Technical Behaviors

State Management:
Track:
currentQuestionIndex (0-49).
userAnswers[] (array of selected options).
timeRemaining (in seconds).
Persistence:
Auto-save to localStorage every 30 seconds.
On page reload: Restore from localStorage.
Anti-Cheat:
Disable right-click/text selection.
Fullscreen mode recommended.
4. Data Structures (Example)

javascript
// Sample question format
questions = [
  {
    id: 1,
    text: "다음 밑줄 친 부분과 바꾸어 쓸 수 있는 것을 고르십시오...",
    options: ["되요", "없어요", "생겼어요", "모르겠어요"],
    correctAnswer: 2, // Index of correct option
    category: "Vocabulary" 
  }
  // ...49 more questions
]
5. Edge Cases to Handle

Network issues → Fallback to offline mode.
Tab switching → Show warning ("Do not leave this page!").
Incomplete submissions → Prompt to resume.
This description provides all necessary logic for implementation without prescribing a specific tech stack. Let me know if you need elaboration on any component!
New chat
