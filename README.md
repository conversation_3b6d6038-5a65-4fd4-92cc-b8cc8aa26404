# EPS Simulator 2

A web application for practicing EPS-TOPIK (Employment Permit System - Test of Proficiency in Korean) exams.

## Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn package manager

## Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd EPSSimulator2
```

2. Install dependencies:
```bash
npm install
```

3. Set up the database:
- Create a PostgreSQL database named `eps_simulator`
- Update the database connection string in your environment variables:
```bash
export DATABASE_URL="postgres://<username>@localhost:5432/eps_simulator"
```

4. Seed the database with initial data:
```bash
DATABASE_URL="postgres://<username>@localhost:5432/eps_simulator" NODE_ENV=development npx tsx scripts/seed_data.ts
```

## Running the Application

The application consists of two parts: the backend server and the frontend development server.

### Option 1: Running in Foreground (Development)

#### Start the Backend Server

In one terminal window:
```bash
DATABASE_URL="postgres://<username>@localhost:5432/eps_simulator" NODE_ENV=development npx tsx server/index.ts
```
The backend server will run on http://localhost:3000

#### Start the Frontend Development Server

In another terminal window:
```bash
NODE_ENV=development npx vite
```
The frontend development server will run on http://localhost:5173

### Option 2: Running in Background (Production/Development)

#### Using nohup (Simple Background Process)

1. Start the backend server in background:
```bash
nohup DATABASE_URL="postgres://<username>@localhost:5432/eps_simulator" NODE_ENV=development npx tsx server/index.ts > backend.log 2>&1 &
```

2. Start the frontend server in background:
```bash
nohup NODE_ENV=development npx vite > frontend.log 2>&1 &
```

To check the logs:
```bash
tail -f backend.log  # For backend logs
tail -f frontend.log # For frontend logs
```

To stop the processes:
```bash
pkill -f "npx tsx server/index.ts"  # Stop backend
pkill -f "npx vite"                 # Stop frontend
```

#### Using screen (Recommended for Development)

1. Create a new screen session:
```bash
screen -S eps-simulator
```

2. Start the backend server:
```bash
DATABASE_URL="postgres://<username>@localhost:5432/eps_simulator" NODE_ENV=development npx tsx server/index.ts
```

3. Press `Ctrl+A` then `D` to detach from the screen session

4. Create another screen session for frontend:
```bash
screen -S eps-frontend
```

5. Start the frontend server:
```bash
NODE_ENV=development npx vite
```

6. Press `Ctrl+A` then `D` to detach from the screen session

To reattach to the sessions:
```bash
screen -r eps-simulator  # For backend
screen -r eps-frontend   # For frontend
```

To stop the processes:
```bash
screen -X -S eps-simulator quit  # Stop backend
screen -X -S eps-frontend quit   # Stop frontend
```

## Application Structure

- `client/` - Frontend React application
- `server/` - Backend Express server
- `shared/` - Shared TypeScript types and utilities
- `scripts/` - Database seeding and utility scripts

## Features

- User authentication (login/register)
- Admin dashboard for managing exams
- User dashboard for taking practice exams
- Exam results tracking
- Reading and listening sections
- Real-time exam timer

## Default Admin Account

After seeding the database, you can log in with these credentials:
- Username: admin
- Password: adminpass

## Troubleshooting

1. If you see port conflicts:
   - Make sure no other processes are using ports 3000 or 5173
   - Kill any existing processes using these ports:
   ```bash
   lsof -i :3000
   lsof -i :5173
   ```

2. If you see database connection errors:
   - Verify your PostgreSQL server is running
   - Check your database credentials in the DATABASE_URL
   - Ensure the database `eps_simulator` exists

3. If you see dependency resolution errors:
   - Try clearing the node_modules and reinstalling:
   ```bash
   rm -rf node_modules
   npm install
   ```

4. If background processes are not working:
   - Check if the processes are running:
   ```bash
   ps aux | grep "npx tsx server/index.ts"
   ps aux | grep "npx vite"
   ```
   - Check the log files for errors:
   ```bash
   cat backend.log
   cat frontend.log
   ```

## Development

- The application uses TypeScript for both frontend and backend
- Vite is used for frontend development and building
- Express.js powers the backend server
- PostgreSQL with Drizzle ORM for database operations 