import { pgTable, text, serial, integer, boolean, timestamp, jsonb, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// Enums
export const roleEnum = pgEnum('role', ['user', 'admin']);
export const statusEnum = pgEnum('status', ['active', 'inactive', 'pending']);

// User model for authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password"),
  email: text("email").notNull().unique(),
  fullName: text("full_name"),
  role: roleEnum("role").notNull().default("user"),
  status: statusEnum("status").notNull().default("active"),
  authProvider: text("auth_provider", { enum: ['local', 'google'] }).notNull().default("local"),
  googleId: text("google_id").unique(),
  profilePicture: text("profile_picture"),
  lastLogin: timestamp("last_login"),
  emailVerified: boolean("email_verified").default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});

// Refresh tokens table
export const refreshTokens = pgTable("refresh_tokens", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  token: text("token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  revoked: boolean("revoked").notNull().default(false),
  deviceInfo: text("device_info")
});

// Exam model to store exam structure
export const exams = pgTable("exams", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  duration: integer("duration").notNull(), // in minutes
  readingTime: integer("reading_time").notNull(), // in seconds
  listeningTime: integer("listening_time").notNull(), // in seconds
  questions: jsonb("questions").notNull(), // an array of questions
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  createdBy: integer("created_by").references(() => users.id),
  listeningAudioUrl: text("listeningAudioUrl"),
  listeningAudioDuration: integer("listeningAudioDuration")
});

// Exam Attempt model to store user's exam attempts
export const examAttempts = pgTable("exam_attempts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  examId: integer("exam_id").notNull().references(() => exams.id),
  answers: jsonb("answers"), // user's answers
  score: integer("score"),
  completed: boolean("completed").notNull().default(false),
  startedAt: timestamp("started_at").notNull().defaultNow(),
  completedAt: timestamp("completed_at"),
  result_json: jsonb("result_json") // full test result details
});

// Exam Result Details model to store detailed exam results
export const examResultDetails = pgTable("exam_result_details", {
  id: serial("id").primaryKey(),
  attemptId: integer("attempt_id").notNull().references(() => examAttempts.id, { onDelete: "cascade" }),
  questionId: integer("question_id").notNull(),
  questionText: text("question_text").notNull(),
  questionType: text("question_type").notNull(),
  userAnswer: integer("user_answer"),
  correctAnswer: integer("correct_answer").notNull(),
  isCorrect: boolean("is_correct").notNull(),
  options: jsonb("options").notNull(), // Store all options with their texts
  createdAt: timestamp("created_at").notNull().defaultNow()
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  examAttempts: many(examAttempts),
  refreshTokens: many(refreshTokens)
}));

export const examsRelations = relations(exams, ({ many }) => ({
  attempts: many(examAttempts)
}));

export const examAttemptsRelations = relations(examAttempts, ({ one }) => ({
  user: one(users, {
    fields: [examAttempts.userId],
    references: [users.id],
  }),
  exam: one(exams, {
    fields: [examAttempts.examId],
    references: [exams.id],
  }),
}));

// Question type for JSON structure
export const questionSchema = z.object({
  id: z.number(),
  display_index: z.number().min(1),
  type: z.enum(["reading", "listening"]),
  text: z.string(),
  subText: z.string().optional(),
  options: z.array(z.object({
    id: z.number(),
    text: z.string().optional(),
    imageUrl: z.string().optional()
  })),
  hasImageOptions: z.boolean().optional(),
  imageQuestion: z.boolean().optional(),
  imageUrl: z.string().optional(),
  start_at: z.number().optional(), // Thời điểm bắt đầu của câu hỏi (giây)
  correctAnswer: z.number().optional()
});

export type Question = z.infer<typeof questionSchema>;

// Create insert schemas
export const insertUserSchema = createInsertSchema(users);
export const selectUserSchema = createSelectSchema(users);

export const insertRefreshTokenSchema = createInsertSchema(refreshTokens);
export const selectRefreshTokenSchema = createSelectSchema(refreshTokens);

// Create base insert schema
const baseInsertExamSchema = createInsertSchema(exams);

// Extend the schema to ensure readingTime, listeningTime, and listeningAudioDuration are numbers
export const insertExamSchema = baseInsertExamSchema.extend({
  readingTime: z.coerce.number(),
  listeningTime: z.coerce.number(),
  listeningAudioDuration: z.coerce.number().optional()
});
// Create base select schema
const baseSelectExamSchema = createSelectSchema(exams);

// Extend the schema to ensure readingTime, listeningTime, and listeningAudioDuration are numbers
export const selectExamSchema = baseSelectExamSchema.extend({
  readingTime: z.coerce.number(),
  listeningTime: z.coerce.number(),
  listeningAudioDuration: z.coerce.number().optional()
});

export const insertExamAttemptSchema = createInsertSchema(examAttempts);
export const selectExamAttemptSchema = createSelectSchema(examAttempts);

export const insertExamResultDetailsSchema = createInsertSchema(examResultDetails);

// Define types
export type User = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;
export type RefreshToken = typeof refreshTokens.$inferSelect;
export type InsertRefreshToken = typeof refreshTokens.$inferInsert;
export type Exam = typeof exams.$inferSelect;
export type InsertExam = typeof exams.$inferInsert;
export type ExamAttempt = typeof examAttempts.$inferSelect;
export type InsertExamAttempt = typeof examAttempts.$inferInsert;
export type ExamResultDetails = typeof examResultDetails.$inferSelect;
export type InsertExamResultDetails = typeof examResultDetails.$inferInsert;
