import { NextFunction, Request, Response } from 'express';
import session from 'express-session';
import connectPg from 'connect-pg-simple';
import { pool } from './db';
import { hashPassword, comparePassword } from './auth-utils';
import crypto from 'crypto';

// Generate a strong random secret for session security
const SESSION_SECRET = process.env.SESSION_SECRET || crypto.randomBytes(32).toString('hex');

// Create PostgreSQL session store
const PostgresStore = connectPg(session);

declare global {
  namespace Express {
    interface Request {
      user?: any;
      isAuthenticated: () => boolean;
    }
  }
}

// Declare session data type
declare module 'express-session' {
  interface SessionData {
    user?: any;
  }
}

// Thiết lập middleware xác thực
export function setupAuth(app: any) {
  // Thiết lập session
  // Get environment mode
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Setup session middleware with more debugging
  console.log('Setting up session middleware with PostgreSQL store');
  
  // Create session store with error handling
  const sessionStore = new PostgresStore({
    pool,
    tableName: 'user_sessions',
    createTableIfMissing: true,
    pruneSessionInterval: 60 // Cleanup every 60 seconds
  });
  
  // Handle store errors
  sessionStore.on('error', function(error) {
    console.error('Session store error:', error);
  });

  app.use(
    session({
      store: sessionStore,
      secret: SESSION_SECRET,
      resave: true, // Always save session even if unmodified
      saveUninitialized: false, // Don't create session until something stored
      rolling: true, // Reset expiration on every response
      cookie: {
        secure: false, // Never use secure in Replit environment
        httpOnly: true,
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        sameSite: 'lax',
        path: '/',
      },
      name: 'eps_session' // Custom name to avoid conflicts
    })
  );
  
  // Log session setup 
  console.log('Session middleware configured, cookie settings:', {
    secure: false,
    httpOnly: true,
    sameSite: 'lax',
    path: '/',
    maxAge: '30 days'
  });

  // Add authentication helpers to request
  app.use((req: Request, _res: Response, next: NextFunction) => {
    // Add authentication check method
    req.isAuthenticated = function() {
      const isAuth = !!req.session.user;
      console.log(`isAuthenticated check: ${isAuth}, sessionID: ${req.sessionID}`);
      return isAuth;
    };
    
    // Attach user data to request if logged in
    if (req.session.user) {
      req.user = req.session.user;
      console.log(`User attached to request: ${req.user.username}, role: ${req.user.role}`);
    }
    
    next();
  });
  
  // Add a test endpoint to check session functionality
  app.get('/api/session-test', (req: Request, res: Response) => {
    const sessionData = {
      id: req.sessionID,
      cookie: req.session.cookie,
      user: req.session.user,
      isAuthenticated: req.isAuthenticated()
    };
    
    res.json(sessionData);
  });
}

// Middleware yêu cầu đăng nhập
export const requireAuth = (req: Request, res: Response, next: NextFunction) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "Not authenticated" });
  }
  next();
};

// Middleware yêu cầu quyền admin
export const requireAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "Not authenticated" });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: "Admin access required" });
  }
  
  next();
};