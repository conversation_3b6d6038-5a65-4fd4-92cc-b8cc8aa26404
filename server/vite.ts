import { createServer as createViteServer } from "vite";
import { type Express } from "express";
import { resolve } from "path";
import express from "express";

export const log = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`[${timestamp}] ${message}`);
};

export const setupVite = async (app: Express) => {
  const vite = await createViteServer({
    server: { middlewareMode: true },
    appType: "custom",
    root: resolve(process.cwd(), "client"),
  });

  app.use(vite.middlewares);

  // Serve index.html
  app.use('*', async (req, res, next) => {
    const url = req.originalUrl;

    try {
      let template = await vite.transformIndexHtml(url, '');
      res.status(200).set({ 'Content-Type': 'text/html' }).end(template);
    } catch (e) {
      vite.ssrFixStacktrace(e as Error);
      next(e);
    }
  });
};

export const serveStatic = (app: Express) => {
  app.use(express.static(resolve(process.cwd(), "dist/client")));
  
  // Serve index.html for all routes
  app.get('*', (req, res) => {
    res.sendFile(resolve(process.cwd(), "dist/client/index.html"));
  });
};
