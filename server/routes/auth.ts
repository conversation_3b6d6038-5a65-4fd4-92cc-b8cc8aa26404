import { Router } from 'express';
import bcrypt from 'bcryptjs';
import { db } from '../db';
import { generateAccessToken, generateRefreshToken } from '../utils/jwt';
import { authenticateJWT } from '../middleware/auth';
import { users, refreshTokens } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';

const router = Router();

// Configure Google OAuth
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback',
  scope: ['profile', 'email']
}, async (accessToken, refreshToken, profile, done) => {
  try {
    if (!profile.emails?.[0]?.value) {
      return done(new Error('No email provided by Google'));
    }

    // Check if user exists with this Google ID
    let user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.googleId, profile.id)
    });

    if (!user) {
      // Check if user exists with this email
      user = await db.query.users.findFirst({
        where: (users, { eq }) => eq(users.email, profile.emails[0].value)
      });

      if (user) {
        // Update existing user with Google ID
        const [updatedUser] = await db.update(users)
          .set({ 
            googleId: profile.id,
            authProvider: 'google',
            emailVerified: true,
            profilePicture: profile.photos?.[0]?.value || null
          })
          .where(eq(users.id, user.id))
          .returning();
        
        if (!updatedUser) {
          return done(new Error('Failed to update user'));
        }
        user = updatedUser;
      } else {
        // Create new user
        const [newUser] = await db.insert(users).values({
          username: profile.emails[0].value.split('@')[0] || profile.id,
          email: profile.emails[0].value,
          fullName: profile.displayName,
          googleId: profile.id,
          authProvider: 'google',
          role: 'user',
          status: 'active',
          emailVerified: true,
          profilePicture: profile.photos?.[0]?.value || null
        }).returning();
        
        if (!newUser) {
          return done(new Error('Failed to create user'));
        }
        user = newUser;
      }
    }

    // Generate JWT tokens
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };

    const jwtAccessToken = generateAccessToken(tokenPayload);
    const jwtRefreshToken = generateRefreshToken(tokenPayload);

    // Store refresh token
    await db.insert(refreshTokens).values({
      userId: user.id,
      token: jwtRefreshToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return done(null, { ...user, accessToken: jwtAccessToken, refreshToken: jwtRefreshToken });
  } catch (error) {
    return done(error as Error);
  }
}));

// Google OAuth routes
router.get('/google', passport.authenticate('google'));

router.get('/google/callback', 
  passport.authenticate('google', { failureRedirect: '/login' }),
  (req, res) => {
    // Successful authentication, redirect to frontend with tokens
    const user = req.user as any;
    res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/callback?accessToken=${user.accessToken}&refreshToken=${user.refreshToken}`);
  }
);

// Local Login with JWT
router.post('/login', async (req, res) => {
  const { username, password } = req.body;

  try {
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.username, username)
    });

    if (!user || !user.password) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };

    const accessToken = generateAccessToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // Store refresh token
    await db.insert(refreshTokens).values({
      userId: user.id,
      token: refreshToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    res.json({
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        fullName: user.fullName,
        profilePicture: user.profilePicture
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create/Update Admin User
router.post('/admin/create', async (req, res) => {
  const { username, password } = req.body;

  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Try to find existing user
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.username, username)
    });

    if (existingUser) {
      // Update existing user
      await db.update(users)
        .set({
          password: hashedPassword,
          role: 'admin',
          status: 'active',
          emailVerified: true
        })
        .where(eq(users.username, username));
    } else {
      // Create new admin user
      await db.insert(users).values({
        username,
        password: hashedPassword,
        email: `${username}@example.com`,
        role: 'admin',
        status: 'active',
        authProvider: 'local',
        emailVerified: true
      });
    }

    res.json({ message: 'Admin user created/updated successfully' });
  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Refresh Token
router.post('/refresh-token', async (req, res) => {
  const { refreshToken } = req.body;

  try {
    const token = await db.query.refreshTokens.findFirst({
      where: (tokens, { eq }) => eq(tokens.token, refreshToken)
    });

    if (!token || token.revoked || new Date() > token.expiresAt) {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, token.userId)
    });

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };

    const newAccessToken = generateAccessToken(tokenPayload);
    const newRefreshToken = generateRefreshToken(tokenPayload);

    // Revoke old refresh token and create new one
    await db.update(refreshTokens)
      .set({ revoked: true })
      .where(eq(refreshTokens.token, refreshToken));

    await db.insert(refreshTokens).values({
      userId: user.id,
      token: newRefreshToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    });

    res.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Logout
router.post('/logout', authenticateJWT, async (req, res) => {
  const { refreshToken } = req.body;

  try {
    if (refreshToken) {
      await db.update(refreshTokens)
        .set({ revoked: true })
        .where(eq(refreshTokens.token, refreshToken));
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router; 