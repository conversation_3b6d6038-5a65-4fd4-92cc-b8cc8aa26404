import { Router } from 'express';
import { authenticateJWT, requireRole } from '../middleware/auth';
import { db } from '../db';
import { exams, examAttempts } from '@shared/schema';
import { eq } from 'drizzle-orm';

const router = Router();

// Get all exams
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const examList = await db.query.exams.findMany({
      where: (exams, { eq }) => eq(exams.isActive, true)
    });
    res.json(examList);
  } catch (error) {
    console.error('Error fetching exams:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get exam by ID
router.get('/:id', authenticateJWT, async (req, res) => {
  try {
    const exam = await db.query.exams.findFirst({
      where: (exams, { eq }) => eq(exams.id, parseInt(req.params.id))
    });

    if (!exam) {
      return res.status(404).json({ message: 'Exam not found' });
    }

    res.json(exam);
  } catch (error) {
    console.error('Error fetching exam:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create exam (admin only)
router.post('/', authenticateJWT, requireRole('admin'), async (req, res) => {
  try {
    const exam = await db.insert(exams).values({
      ...req.body,
      createdBy: req.user?.userId
    }).returning();

    res.status(201).json(exam[0]);
  } catch (error) {
    console.error('Error creating exam:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update exam (admin only)
router.put('/:id', authenticateJWT, requireRole('admin'), async (req, res) => {
  try {
    const exam = await db.update(exams)
      .set(req.body)
      .where(eq(exams.id, parseInt(req.params.id)))
      .returning();

    if (!exam.length) {
      return res.status(404).json({ message: 'Exam not found' });
    }

    res.json(exam[0]);
  } catch (error) {
    console.error('Error updating exam:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete exam (admin only)
router.delete('/:id', authenticateJWT, requireRole('admin'), async (req, res) => {
  try {
    const exam = await db.delete(exams)
      .where(eq(exams.id, parseInt(req.params.id)))
      .returning();

    if (!exam.length) {
      return res.status(404).json({ message: 'Exam not found' });
    }

    res.json({ message: 'Exam deleted successfully' });
  } catch (error) {
    console.error('Error deleting exam:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Start exam attempt
router.post('/:id/start', authenticateJWT, async (req, res) => {
  try {
    const exam = await db.query.exams.findFirst({
      where: (exams, { eq }) => eq(exams.id, parseInt(req.params.id))
    });

    if (!exam) {
      return res.status(404).json({ message: 'Exam not found' });
    }

    const attempt = await db.insert(examAttempts).values({
      userId: req.user?.userId!,
      examId: exam.id,
      startedAt: new Date()
    }).returning();

    res.status(201).json(attempt[0]);
  } catch (error) {
    console.error('Error starting exam attempt:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Submit exam attempt
router.post('/:id/submit', authenticateJWT, async (req, res) => {
  try {
    const { answers } = req.body;
    const attempt = await db.update(examAttempts)
      .set({
        answers,
        completed: true,
        completedAt: new Date()
      })
      .where(eq(examAttempts.id, parseInt(req.params.id)))
      .returning();

    if (!attempt.length) {
      return res.status(404).json({ message: 'Exam attempt not found' });
    }

    res.json(attempt[0]);
  } catch (error) {
    console.error('Error submitting exam attempt:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router; 