import { Router } from 'express';
import authRoutes from './auth';
import examRoutes from './exams';
import { db } from '../db';

export const registerRoutes = () => {
  const router = Router();

  // Health check
  router.get('/health', (req, res) => {
    res.json({ status: 'ok' });
  });

  // Auth routes
  router.use('/auth', authRoutes);

  // Exam routes
  router.use('/exams', examRoutes);

  return router;
}; 