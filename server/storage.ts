import { 
  users, exams, examAttempts, examResultDetails,
  type User, type InsertUser,
  type Exam, type InsertExam,
  type ExamAttempt, type InsertExamAttempt,
  type Question,
  type ExamResultDetails, type InsertExamResultDetails
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, sql, count, avg, inArray } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  getUsers(): Promise<User[]>;
  getActiveUsers(): Promise<User[]>;
  getUsersCount(): Promise<number>;
  getActiveUsersCount(): Promise<number>;
  updateUser(id: number, updates: Partial<Omit<User, 'id'>>): Promise<User | undefined>;
  
  // Exam operations
  getExam(id: number): Promise<Exam | undefined>;
  getExams(): Promise<Exam[]>;
  getActiveExams(): Promise<Exam[]>;
  getExamsCount(): Promise<number>;
  createExam(exam: InsertExam): Promise<Exam>;
  updateExam(id: number, updates: Partial<Omit<Exam, 'id'>>): Promise<Exam | undefined>;
  deleteExam(id: number): Promise<boolean>;
  
  // Exam Attempt operations
  getExamAttempt(id: number): Promise<ExamAttempt | undefined>;
  getExamAttemptsByUser(userId: number): Promise<ExamAttempt[]>;
  getExamAttemptsByExam(examId: number): Promise<ExamAttempt[]>;
  getCompletedExamAttemptsCount(): Promise<number>;
  getExamAttemptsCount(): Promise<number>;
  getTopExams(limit: number): Promise<{ examId: number, title: string, attemptsCount: number }[]>;
  getExamAverageScores(): Promise<{ examId: number, title: string, averageScore: number }[]>;
  createExamAttempt(attempt: InsertExamAttempt): Promise<ExamAttempt>;
  updateExamAttempt(id: number, updates: Partial<Omit<ExamAttempt, 'id'>>): Promise<ExamAttempt | undefined>;

  // Exam Result Details operations
  createExamResultDetails(details: InsertExamResultDetails): Promise<ExamResultDetails>;
  getExamResultDetails(attemptId: number): Promise<ExamResultDetails[]>;
  getExamResultDetailsByQuestion(attemptId: number, questionId: number): Promise<ExamResultDetails | undefined>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async getUsers(): Promise<User[]> {
    return db.select().from(users).orderBy(desc(users.createdAt));
  }

  async getActiveUsers(): Promise<User[]> {
    return db.select().from(users).where(eq(users.status, 'active')).orderBy(desc(users.createdAt));
  }

  async getUsersCount(): Promise<number> {
    const result = await db.select({ count: count() }).from(users);
    return result[0].count;
  }

  async getActiveUsersCount(): Promise<number> {
    const result = await db.select({ count: count() }).from(users).where(eq(users.status, 'active'));
    return result[0].count;
  }

  async updateUser(id: number, updates: Partial<Omit<User, 'id'>>): Promise<User | undefined> {
    const [user] = await db.update(users)
      .set(updates)
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  async getExam(id: number): Promise<Exam | undefined> {
    const [exam] = await db.select().from(exams).where(eq(exams.id, id));
    return exam;
  }

  async getExams(): Promise<Exam[]> {
    return db.select().from(exams).orderBy(desc(exams.createdAt));
  }

  async getActiveExams(): Promise<Exam[]> {
    return db.select().from(exams).where(eq(exams.isActive, true)).orderBy(desc(exams.createdAt));
  }

  async getExamsCount(): Promise<number> {
    const result = await db.select({ count: count() }).from(exams);
    return result[0].count;
  }

  async createExam(exam: InsertExam): Promise<Exam> {
    const [created] = await db.insert(exams).values(exam).returning();
    return created;
  }

  async updateExam(id: number, updates: Partial<Omit<Exam, 'id'>>): Promise<Exam | undefined> {
    const [exam] = await db.update(exams)
      .set(updates)
      .where(eq(exams.id, id))
      .returning();
    return exam;
  }

  async deleteExam(id: number): Promise<boolean> {
    // First check if there are any exam attempts for this exam
    const existingAttempts = await db.select({ count: count() })
      .from(examAttempts)
      .where(eq(examAttempts.examId, id));
    
    if (existingAttempts[0].count > 0) {
      // Don't physically delete, just deactivate
      await db.update(exams)
        .set({ isActive: false })
        .where(eq(exams.id, id));
      return true;
    } else {
      // No attempts, can physically delete
      await db.delete(exams).where(eq(exams.id, id));
      return true;
    }
  }

  async getExamAttempt(id: number): Promise<ExamAttempt | undefined> {
    const result = await db.select().from(examAttempts).where(eq(examAttempts.id, id)).limit(1);
    return result[0];
  }

  async getExamAttemptsByUser(userId: number): Promise<ExamAttempt[]> {
    return db.select()
      .from(examAttempts)
      .where(eq(examAttempts.userId, userId))
      .orderBy(desc(examAttempts.startedAt));
  }

  async getExamAttemptsByExam(examId: number): Promise<ExamAttempt[]> {
    return db.select()
      .from(examAttempts)
      .where(eq(examAttempts.examId, examId))
      .orderBy(desc(examAttempts.startedAt));
  }

  async getCompletedExamAttemptsCount(): Promise<number> {
    const result = await db.select({ count: count() })
      .from(examAttempts)
      .where(eq(examAttempts.completed, true));
    return result[0].count;
  }

  async getExamAttemptsCount(): Promise<number> {
    const result = await db.select({ count: count() }).from(examAttempts);
    return result[0].count;
  }

  async getTopExams(limit: number): Promise<{ examId: number, title: string, attemptsCount: number }[]> {
    const result = await db.select({
      examId: examAttempts.examId,
      attemptsCount: count(),
    })
    .from(examAttempts)
    .groupBy(examAttempts.examId)
    .orderBy(desc(sql`count(*)`))
    .limit(limit);

    // Get exam titles
    const examIds = result.map(r => r.examId);
    
    if (examIds.length === 0) {
      return [];
    }
    
    const examData = await db.select({
      id: exams.id,
      title: exams.title,
    })
    .from(exams)
    .where(inArray(exams.id, examIds));

    // Join data
    return result.map(r => {
      const exam = examData.find(e => e.id === r.examId);
      return {
        examId: r.examId,
        title: exam?.title || `Exam #${r.examId}`,
        attemptsCount: r.attemptsCount,
      };
    });
  }

  async getExamAverageScores(): Promise<{ examId: number, title: string, averageScore: number }[]> {
    const result = await db.select({
      examId: examAttempts.examId,
      averageScore: avg(examAttempts.score),
    })
    .from(examAttempts)
    .where(eq(examAttempts.completed, true))
    .groupBy(examAttempts.examId);

    // Get exam titles
    const examIds = result.map(r => r.examId);
    
    if (examIds.length === 0) {
      return [];
    }
    
    const examData = await db.select({
      id: exams.id,
      title: exams.title,
    })
    .from(exams)
    .where(inArray(exams.id, examIds));

    // Join data
    return result.map(r => {
      const exam = examData.find(e => e.id === r.examId);
      return {
        examId: r.examId,
        title: exam?.title || `Exam #${r.examId}`,
        averageScore: Number(r.averageScore) || 0,
      };
    });
  }

  async createExamAttempt(attempt: InsertExamAttempt): Promise<ExamAttempt> {
    // Convert date strings to Date objects if they exist
    const processedAttempt = {
      ...attempt,
      startedAt: attempt.startedAt ? new Date(attempt.startedAt) : undefined,
      completedAt: attempt.completedAt ? new Date(attempt.completedAt) : undefined
    };
    
    const [createdAttempt] = await db.insert(examAttempts).values(processedAttempt).returning();
    return createdAttempt;
  }

  async updateExamAttempt(id: number, updates: Partial<Omit<ExamAttempt, 'id'>>): Promise<ExamAttempt | undefined> {
    const [attempt] = await db.update(examAttempts)
      .set(updates)
      .where(eq(examAttempts.id, id))
      .returning();
    return attempt;
  }

  async createExamResultDetails(details: InsertExamResultDetails): Promise<ExamResultDetails> {
    const [result] = await db.insert(examResultDetails).values(details).returning();
    return result;
  }

  async getExamResultDetails(attemptId: number): Promise<ExamResultDetails[]> {
    return db.select().from(examResultDetails)
      .where(eq(examResultDetails.attemptId, attemptId))
      .orderBy(examResultDetails.questionId);
  }

  async getExamResultDetailsByQuestion(attemptId: number, questionId: number): Promise<ExamResultDetails | undefined> {
    const [result] = await db.select().from(examResultDetails)
      .where(and(
        eq(examResultDetails.attemptId, attemptId),
        eq(examResultDetails.questionId, questionId)
      ));
    return result;
  }
}

// Fallback to memory storage for development if database fails
class MemStorage implements IStorage {
  private usersMap: Map<number, User>;
  private examsMap: Map<number, Exam>;
  private examAttemptsMap: Map<number, ExamAttempt>;
  private examResultDetailsMap: Map<number, ExamResultDetails[]>;
  private userIdCounter: number;
  private examIdCounter: number;
  private attemptIdCounter: number;
  private resultDetailsIdCounter: number;

  constructor() {
    this.usersMap = new Map();
    this.examsMap = new Map();
    this.examAttemptsMap = new Map();
    this.examResultDetailsMap = new Map();
    this.userIdCounter = 1;
    this.examIdCounter = 1;
    this.attemptIdCounter = 1;
    this.resultDetailsIdCounter = 1;
    
    // Add an admin user
    this.createUser({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin', 
      role: 'admin'
    });
    
    // Add a demo exam
    this.createExam({
      title: 'EPS-TOPIK Demo Exam',
      description: 'Test of proficiency in Korean',
      duration: 60,
      readingTime: 40,
      listeningTime: 20,
      questions: this.generateSampleQuestions(),
      createdBy: 1
    });
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.usersMap.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.usersMap.values()).find(
      (user) => user.username === username
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const createdAt = new Date();
    const user: User = { 
      ...insertUser, 
      id, 
      createdAt,
      password: insertUser.password || null,
      fullName: insertUser.fullName || null,
      role: insertUser.role || 'user',
      status: insertUser.status || 'active',
      authProvider: insertUser.authProvider || 'local',
      googleId: insertUser.googleId || null,
      emailVerified: insertUser.emailVerified || false,
      profilePicture: insertUser.profilePicture || null,
      lastLogin: insertUser.lastLogin || null,
      updatedAt: new Date()
    };
    this.usersMap.set(id, user);
    return user;
  }
  
  async getUsers(): Promise<User[]> {
    return Array.from(this.usersMap.values());
  }

  async getActiveUsers(): Promise<User[]> {
    return Array.from(this.usersMap.values()).filter(user => user.status === 'active');
  }

  async getUsersCount(): Promise<number> {
    return this.usersMap.size;
  }

  async getActiveUsersCount(): Promise<number> {
    return Array.from(this.usersMap.values()).filter(user => user.status === 'active').length;
  }

  async updateUser(id: number, updates: Partial<Omit<User, 'id'>>): Promise<User | undefined> {
    const user = this.usersMap.get(id);
    if (!user) return undefined;
    
    const updatedUser = { ...user, ...updates };
    this.usersMap.set(id, updatedUser);
    return updatedUser;
  }

  // Exam operations
  async getExam(id: number): Promise<Exam | undefined> {
    return this.examsMap.get(id);
  }

  async getExams(): Promise<Exam[]> {
    return Array.from(this.examsMap.values());
  }
  
  async getActiveExams(): Promise<Exam[]> {
    return Array.from(this.examsMap.values()).filter(exam => exam.isActive);
  }

  async getExamsCount(): Promise<number> {
    return this.examsMap.size;
  }

  async createExam(insertExam: InsertExam): Promise<Exam> {
    const id = this.examIdCounter++;
    const createdAt = new Date();
    const exam: Exam = { 
      ...insertExam, 
      id, 
      createdAt,
      description: insertExam.description || null,
      isActive: insertExam.isActive ?? true,
      createdBy: insertExam.createdBy || null,
      listeningAudioUrl: insertExam.listeningAudioUrl || null
    };
    this.examsMap.set(id, exam);
    return exam;
  }
  
  async updateExam(id: number, updates: Partial<Omit<Exam, 'id'>>): Promise<Exam | undefined> {
    const exam = this.examsMap.get(id);
    if (!exam) return undefined;
    
    const updatedExam = { ...exam, ...updates };
    this.examsMap.set(id, updatedExam);
    return updatedExam;
  }
  
  async deleteExam(id: number): Promise<boolean> {
    const hasAttempts = Array.from(this.examAttemptsMap.values()).some(attempt => attempt.examId === id);
    
    if (hasAttempts) {
      const exam = this.examsMap.get(id);
      if (exam) {
        this.examsMap.set(id, { ...exam, isActive: false });
      }
      return true;
    }
    
    this.examsMap.delete(id);
    return true;
  }

  // Exam Attempt operations
  async getExamAttempt(id: number): Promise<ExamAttempt | undefined> {
    return this.examAttemptsMap.get(id);
  }

  async getExamAttemptsByUser(userId: number): Promise<ExamAttempt[]> {
    return Array.from(this.examAttemptsMap.values()).filter(
      (attempt) => attempt.userId === userId
    );
  }
  
  async getExamAttemptsByExam(examId: number): Promise<ExamAttempt[]> {
    return Array.from(this.examAttemptsMap.values()).filter(
      (attempt) => attempt.examId === examId
    );
  }
  
  async getCompletedExamAttemptsCount(): Promise<number> {
    return Array.from(this.examAttemptsMap.values()).filter(attempt => attempt.completed).length;
  }
  
  async getExamAttemptsCount(): Promise<number> {
    return this.examAttemptsMap.size;
  }
  
  async getTopExams(limit: number): Promise<{ examId: number, title: string, attemptsCount: number }[]> {
    const attemptsByExam = new Map<number, number>();
    
    // Count attempts by exam
    Array.from(this.examAttemptsMap.values()).forEach(attempt => {
      const count = attemptsByExam.get(attempt.examId) || 0;
      attemptsByExam.set(attempt.examId, count + 1);
    });
    
    // Convert to array and sort
    const sorted = Array.from(attemptsByExam.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([examId, attemptsCount]) => {
        const exam = this.examsMap.get(examId);
        return {
          examId,
          title: exam?.title || `Exam #${examId}`,
          attemptsCount,
        };
      });
      
    return sorted;
  }
  
  async getExamAverageScores(): Promise<{ examId: number, title: string, averageScore: number }[]> {
    const scoresByExam = new Map<number, number[]>();
    
    // Group scores by exam
    Array.from(this.examAttemptsMap.values())
      .filter(attempt => attempt.completed && attempt.score !== undefined && attempt.score !== null)
      .forEach(attempt => {
        const scores = scoresByExam.get(attempt.examId) || [];
        scores.push(attempt.score!);
        scoresByExam.set(attempt.examId, scores);
      });
    
    // Calculate averages
    return Array.from(scoresByExam.entries()).map(([examId, scores]) => {
      const avg = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      const exam = this.examsMap.get(examId);
      return {
        examId,
        title: exam?.title || `Exam #${examId}`,
        averageScore: avg || 0,
      };
    });
  }

  async createExamAttempt(insertAttempt: InsertExamAttempt): Promise<ExamAttempt> {
    const id = this.attemptIdCounter++;
    const startedAt = new Date();
    const attempt: ExamAttempt = { 
      ...insertAttempt, 
      id, 
      startedAt,
      completedAt: null,
      answers: insertAttempt.answers || {},
      score: insertAttempt.score || null,
      completed: insertAttempt.completed || false,
      result_json: insertAttempt.result_json || {}
    };
    this.examAttemptsMap.set(id, attempt);
    return attempt;
  }

  async updateExamAttempt(id: number, updates: Partial<Omit<ExamAttempt, 'id'>>): Promise<ExamAttempt | undefined> {
    const attempt = this.examAttemptsMap.get(id);
    if (!attempt) return undefined;
    
    const updatedAttempt = { ...attempt, ...updates };
    this.examAttemptsMap.set(id, updatedAttempt);
    return updatedAttempt;
  }
  
  // Helper method to generate sample questions
  private generateSampleQuestions(): Question[] {
    const questions: Question[] = [];
    
    // Reading questions (more realistic Korean content)
    questions.push({
      id: 1,
      type: "reading",
      text: "어디의 직업은 무엇입니까?",
      subText: "",
      options: [
        { id: 1, text: "가수" },
        { id: 2, text: "교사" },
        { id: 3, text: "학생" },
        { id: 4, text: "회사원" }
      ],
      correctAnswer: 2,
      display_index: 1
    });
    
    questions.push({
      id: 2,
      type: "reading",
      text: "이것은 무엇입니까?",
      subText: "",
      options: [
        { id: 1, text: "책" },
        { id: 2, text: "공책" },
        { id: 3, text: "펜" },
        { id: 4, text: "가방" }
      ],
      correctAnswer: 1,
      display_index: 2
    });
    
    // Sample listening question with image options
    questions.push({
      id: 11,
      type: "listening",
      text: "듣고 맞는 그림을 고르세요.",
      subText: "",
      options: [
        { id: 1, imageUrl: "https://via.placeholder.com/150?text=Image+1" },
        { id: 2, imageUrl: "https://via.placeholder.com/150?text=Image+2" },
        { id: 3, imageUrl: "https://via.placeholder.com/150?text=Image+3" },
        { id: 4, imageUrl: "https://via.placeholder.com/150?text=Image+4" }
      ],
      hasImageOptions: true,
      correctAnswer: 2,
      display_index: 3
    });
    
    return questions;
  }

  async createExamResultDetails(details: InsertExamResultDetails): Promise<ExamResultDetails> {
    const id = this.resultDetailsIdCounter++;
    const result: ExamResultDetails = {
      ...details,
      id,
      createdAt: new Date(),
      userAnswer: details.userAnswer || null
    };
    
    const existingDetails = this.examResultDetailsMap.get(details.attemptId) || [];
    existingDetails.push(result);
    this.examResultDetailsMap.set(details.attemptId, existingDetails);
    
    return result;
  }

  async getExamResultDetails(attemptId: number): Promise<ExamResultDetails[]> {
    return this.examResultDetailsMap.get(attemptId) || [];
  }

  async getExamResultDetailsByQuestion(attemptId: number, questionId: number): Promise<ExamResultDetails | undefined> {
    const details = this.examResultDetailsMap.get(attemptId);
    return details?.find(d => d.questionId === questionId);
  }
}

// Check if we can connect to the database, if not fall back to MemStorage
let storage: IStorage;

try {
  storage = new DatabaseStorage();
  console.log("Using Database Storage");
} catch (error) {
  console.warn("Failed to initialize database, falling back to memory storage", error);
  storage = new MemStorage();
}

export { storage };