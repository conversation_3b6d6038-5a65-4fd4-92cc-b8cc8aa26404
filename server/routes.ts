import express, { Express, Request, Response, NextFunction } from 'express';
import { Server, createServer } from 'http';
import { storage } from './storage';
import {
  insertUserSchema,
  insertExamSchema,
  insertExamAttemptSchema,
  insertExamResultDetailsSchema
} from '@shared/schema';
import { z } from 'zod';
import { requireAuth, requireAdmin } from './auth';
import { Readable } from 'stream';

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware is already set up in index.ts

  // Current user endpoint with JWT and session support
  app.get("/api/user", async (req: Request, res: Response) => {
    console.log('GET /api/user - checking authentication...');
    console.log('Authorization header:', req.headers.authorization);
    console.log('Session ID:', req.sessionID);
    console.log('Session user:', req.session.user);

    // First try JWT authentication
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];
      console.log('Found JWT token, verifying...');

      try {
        const { verifyAccessToken } = await import('./utils/jwt');
        const tokenPayload = verifyAccessToken(token);
        console.log('JWT token verified:', tokenPayload);

        // Get full user data from database
        const user = await storage.getUser(tokenPayload.userId);
        if (user) {
          console.log('JWT user found in database:', user.username);
          const { password, ...userWithoutPassword } = user;
          return res.json(userWithoutPassword);
        } else {
          console.log('JWT user not found in database');
          return res.status(401).json({ message: "User not found" });
        }
      } catch (jwtError) {
        console.log('JWT verification failed:', jwtError.message);
        // Continue to session-based auth
      }
    }

    // Fallback to session-based authentication
    console.log('Trying session-based authentication...');
    if (req.isAuthenticated()) {
      console.log('Session user is authenticated, returning user data');
      res.json(req.user);
    } else {
      console.log('User is not authenticated');
      res.status(401).json({ message: "Not authenticated" });
    }
  });
  // API Routes
  // ================ USER ROUTES ================
  app.get("/api/users", requireAdmin, async (req: Request, res: Response) => {
    try {
      const users = await storage.getUsers();
      // Don't send passwords back to the client
      const usersWithoutPasswords = users.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });
      res.json(usersWithoutPasswords);
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.get("/api/users/active", requireAdmin, async (req: Request, res: Response) => {
    try {
      const users = await storage.getActiveUsers();
      // Don't send passwords back to the client
      const usersWithoutPasswords = users.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });
      res.json(usersWithoutPasswords);
    } catch (error) {
      console.error('Error fetching active users:', error);
      res.status(500).json({ message: "Failed to fetch active users" });
    }
  });

  app.get("/api/users/count", requireAdmin, async (req: Request, res: Response) => {
    try {
      const count = await storage.getUsersCount();
      res.json({ count });
    } catch (error) {
      console.error('Error fetching users count:', error);
      res.status(500).json({ message: "Failed to fetch users count" });
    }
  });

  app.get("/api/users/active/count", requireAdmin, async (req: Request, res: Response) => {
    try {
      const count = await storage.getActiveUsersCount();
      res.json({ count });
    } catch (error) {
      console.error('Error fetching active users count:', error);
      res.status(500).json({ message: "Failed to fetch active users count" });
    }
  });

  app.get("/api/users/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Don't send the password back to the client
      const { password, ...userWithoutPassword } = user;
      res.json(userWithoutPassword);
    } catch (error) {
      console.error('Error fetching user:', error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  app.patch("/api/users/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid user ID" });
      }

      // Validate that the user exists
      const existingUser = await storage.getUser(id);
      if (!existingUser) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check if the logged-in user is allowed to edit this profile
      // Only allow users to edit their own profile unless they're an admin
      if (req.user.id !== id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to edit this user" });
      }

      // Remove password from updates if it's not provided
      const updates = { ...req.body };
      if (!updates.password) {
        delete updates.password;
      }

      // Only admins can change roles
      if (updates.role && req.user.role !== 'admin') {
        delete updates.role;
      }

      const updatedUser = await storage.updateUser(id, updates);

      // Don't send the password back to the client
      if (updatedUser) {
        const { password, ...userWithoutPassword } = updatedUser;
        res.json(userWithoutPassword);
      } else {
        res.status(500).json({ message: "Failed to update user" });
      }
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  // ================ EXAM ROUTES ================
  app.get("/api/exams", async (req: Request, res: Response) => {
    try {
      const exams = await storage.getExams();
      res.json(exams);
    } catch (error) {
      console.error('Error fetching exams:', error);
      res.status(500).json({ message: "Failed to fetch exams" });
    }
  });

  app.get("/api/exams/active", async (req: Request, res: Response) => {
    try {
      const exams = await storage.getActiveExams();
      res.json(exams);
    } catch (error) {
      console.error('Error fetching active exams:', error);
      res.status(500).json({ message: "Failed to fetch active exams" });
    }
  });

  app.get("/api/exams/count", async (req: Request, res: Response) => {
    try {
      const count = await storage.getExamsCount();
      res.json({ count });
    } catch (error) {
      console.error('Error fetching exams count:', error);
      res.status(500).json({ message: "Failed to fetch exams count" });
    }
  });

  app.get("/api/exams/top", async (req: Request, res: Response) => {
    try {
      const limit = parseInt(req.query.limit as string) || 5;
      const topExams = await storage.getTopExams(limit);
      res.json(topExams);
    } catch (error) {
      console.error('Error fetching top exams:', error);
      res.status(500).json({ message: "Failed to fetch top exams" });
    }
  });

  app.get("/api/exams/scores", async (req: Request, res: Response) => {
    try {
      const averageScores = await storage.getExamAverageScores();
      res.json(averageScores);
    } catch (error) {
      console.error('Error fetching exam average scores:', error);
      res.status(500).json({ message: "Failed to fetch exam average scores" });
    }
  });

  app.get("/api/exams/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid exam ID" });
      }

      const exam = await storage.getExam(id);
      if (!exam) {
        return res.status(404).json({ message: "Exam not found" });
      }

      res.json(exam);
    } catch (error) {
      console.error('Error fetching exam:', error);
      res.status(500).json({ message: "Failed to fetch exam" });
    }
  });

  app.post("/api/exams", async (req: Request, res: Response) => {
    try {
      // Debug log to see what we're receiving from the client
      console.log('Received exam creation request:', {
        readingTime: req.body.readingTime,
        listeningTime: req.body.listeningTime,
        body: req.body
      });

      // Ensure readingTime and listeningTime are numbers
      if (req.body.readingTime) {
        req.body.readingTime = Number(req.body.readingTime);
      }
      if (req.body.listeningTime) {
        req.body.listeningTime = Number(req.body.listeningTime);
      }

      console.log('After conversion:', {
        readingTime: req.body.readingTime,
        listeningTime: req.body.listeningTime
      });

      const examData = insertExamSchema.parse(req.body);
      const exam = await storage.createExam(examData);
      console.log('Created exam:', {
        id: exam?.id,
        readingTime: exam?.readingTime,
        listeningTime: exam?.listeningTime
      });
      res.status(201).json(exam);
    } catch (error) {
      console.error('Error creating exam:', error);
      // Provide more detailed error information
      if (error instanceof z.ZodError) {
        console.error('Zod validation error:', error.errors);
        return res.status(400).json({ message: "Validation error", errors: error.errors });
      } else if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
        return res.status(500).json({ message: `Failed to create exam: ${error.message}` });
      }
      res.status(500).json({ message: "Failed to create exam" });
    }
  });

  app.patch("/api/exams/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid exam ID" });
      }

      // Debug log to see what we're receiving from the client
      console.log('Received exam update request:', {
        id,
        readingTime: req.body.readingTime,
        listeningTime: req.body.listeningTime,
        body: req.body
      });

      // Validate that the exam exists
      const existingExam = await storage.getExam(id);
      if (!existingExam) {
        return res.status(404).json({ message: "Exam not found" });
      }

      // Ensure readingTime and listeningTime are numbers
      if (req.body.readingTime) {
        req.body.readingTime = Number(req.body.readingTime);
      }
      if (req.body.listeningTime) {
        req.body.listeningTime = Number(req.body.listeningTime);
      }

      console.log('After conversion:', {
        readingTime: req.body.readingTime,
        listeningTime: req.body.listeningTime
      });

      try {
        console.log('Calling storage.updateExam with:', {
          id,
          body: JSON.stringify(req.body)
        });
        const updatedExam = await storage.updateExam(id, req.body);
        console.log('Updated exam:', {
          id: updatedExam?.id,
          readingTime: updatedExam?.readingTime,
          listeningTime: updatedExam?.listeningTime,
          listeningAudioDuration: updatedExam?.listeningAudioDuration
        });
        res.json(updatedExam);
      } catch (storageError) {
        console.error('Error in storage.updateExam:', storageError);
        throw storageError;
      }
    } catch (error) {
      console.error('Error updating exam:', error);
      // Provide more detailed error information
      if (error instanceof z.ZodError) {
        console.error('Zod validation error:', error.errors);
        return res.status(400).json({ message: "Validation error", errors: error.errors });
      } else if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
        return res.status(500).json({ message: `Failed to update exam: ${error.message}` });
      }
      res.status(500).json({ message: "Failed to update exam" });
    }
  });

  app.delete("/api/exams/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid exam ID" });
      }

      // Validate that the exam exists
      const existingExam = await storage.getExam(id);
      if (!existingExam) {
        return res.status(404).json({ message: "Exam not found" });
      }

      const success = await storage.deleteExam(id);
      if (success) {
        res.status(204).end();
      } else {
        res.status(500).json({ message: "Failed to delete exam" });
      }
    } catch (error) {
      console.error('Error deleting exam:', error);
      res.status(500).json({ message: "Failed to delete exam" });
    }
  });

  // ================ EXAM ATTEMPT ROUTES ================
  app.get("/api/exam-attempts", requireAuth, async (req: Request, res: Response) => {
    try {
      const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
      const examId = req.query.examId ? parseInt(req.query.examId as string) : undefined;

      // Check if the user is requesting their own attempts or if they're an admin
      if (userId && userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to view these attempts" });
      }

      // If no userId is specified, use the current user's ID (unless they're an admin)
      const effectiveUserId = userId || (req.user.role !== 'admin' ? req.user.id : undefined);

      if (effectiveUserId) {
        const attempts = await storage.getExamAttemptsByUser(effectiveUserId);
        return res.json(attempts);
      } else if (examId) {
        // Only admins can view attempts by exam ID
        if (req.user.role !== 'admin') {
          return res.status(403).json({ message: "You don't have permission to view these attempts" });
        }
        const attempts = await storage.getExamAttemptsByExam(examId);
        return res.json(attempts);
      } else if (req.user.role === 'admin') {
        // For now, admins without filters get an empty array
        // TODO: Add getExamAttempts() to storage interface for admins to view all attempts
        return res.json([]);
      } else {
        // Regular users get their own attempts if no filter is provided
        const attempts = await storage.getExamAttemptsByUser(req.user.id);
        return res.json(attempts);
      }
    } catch (error) {
      console.error('Error fetching exam attempts:', error);
      res.status(500).json({ message: "Failed to fetch exam attempts" });
    }
  });

  app.get("/api/exam-attempts/count", async (req: Request, res: Response) => {
    try {
      const count = await storage.getExamAttemptsCount();
      res.json({ count });
    } catch (error) {
      console.error('Error fetching exam attempts count:', error);
      res.status(500).json({ message: "Failed to fetch exam attempts count" });
    }
  });

  app.get("/api/exam-attempts/completed/count", async (req: Request, res: Response) => {
    try {
      const count = await storage.getCompletedExamAttemptsCount();
      res.json({ count });
    } catch (error) {
      console.error('Error fetching completed exam attempts count:', error);
      res.status(500).json({ message: "Failed to fetch completed exam attempts count" });
    }
  });

  app.get("/api/exam-attempts/:id", /*requireAuth,*/ async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid attempt ID" });
      }

      const attempt = await storage.getExamAttempt(id);
      if (!attempt) {
        return res.status(404).json({ message: "Exam attempt not found" });
      }

      // Không kiểm tra quyền user nữa, public access
      // console.log('[DEBUG] Returning exam attempt with result_json:', attempt.result_json);
      res.json(attempt);
    } catch (error) {
      console.error('Error fetching exam attempt:', error);
      res.status(500).json({ message: "Failed to fetch exam attempt" });
    }
  });

  // New endpoint for full exam attempt data
  app.get("/api/exam-attempts/full", requireAuth, async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      const examId = req.query.examId ? parseInt(req.query.examId as string) : undefined;

      if (!userId) {
        return res.status(401).json({ message: "User not authenticated" });
      }

      // Get the exam attempt
      const attempts = await storage.getExamAttemptsByUser(userId);
      const attempt = examId
        ? attempts.find(a => a.examId === examId)
        : attempts[attempts.length - 1]; // Get the most recent attempt if no examId specified

      if (!attempt) {
        return res.status(404).json({ message: "Exam attempt not found" });
      }

      // Get the exam details
      const exam = await storage.getExam(attempt.examId);
      if (!exam) {
        return res.status(404).json({ message: "Exam not found" });
      }

      // Combine the data
      const fullData = {
        ...attempt,
        exam: {
          id: exam.id,
          title: exam.title,
          description: exam.description,
          duration: exam.duration,
          questions: exam.questions
        }
      };

      res.json(fullData);
    } catch (error) {
      console.error('Error fetching full exam attempt data:', error);
      res.status(500).json({ message: "Failed to fetch full exam attempt data" });
    }
  });

  app.post("/api/exam-attempts/full", requireAuth, async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      const { examId, score, result_json, startedAt, completedAt } = req.body;

      if (!userId) {
        return res.status(401).json({ message: "User not authenticated" });
      }

      if (!examId) {
        return res.status(400).json({ message: "Exam ID is required" });
      }

      // Create the exam attempt
      const attemptData = {
        userId,
        examId,
        score,
        result_json,
        startedAt,
        completedAt,
        completed: true
      };

      const attempt = await storage.createExamAttempt(attemptData);
      res.status(201).json(attempt);
    } catch (error) {
      console.error('Error creating full exam attempt:', error);
      res.status(500).json({ message: "Failed to create full exam attempt" });
    }
  });

  app.post("/api/exam-attempts", requireAuth, async (req: Request, res: Response) => {
    try {
      const attemptData = insertExamAttemptSchema.parse(req.body);

      // Users can only create attempts for themselves unless they're admins
      if (attemptData.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to create an attempt for another user" });
      }

      // Check if exam exists
      const exam = await storage.getExam(attemptData.examId);
      if (!exam) {
        return res.status(404).json({ message: "Exam not found" });
      }

      // Check if the exam is active
      if (exam.isActive === false) {
        return res.status(403).json({ message: "This exam is not currently active" });
      }

      // Check if user exists
      const user = await storage.getUser(attemptData.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // startedAt is auto-set in the database (defaultNow)
      const attempt = await storage.createExamAttempt(attemptData);
      res.status(201).json(attempt);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors });
      }
      console.error('Error creating exam attempt:', error);
      res.status(500).json({ message: "Failed to create exam attempt" });
    }
  });

  app.patch("/api/exam-attempts/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid attempt ID" });
      }

      // Validate that the attempt exists
      const existingAttempt = await storage.getExamAttempt(id);
      if (!existingAttempt) {
        return res.status(404).json({ message: "Exam attempt not found" });
      }

      // Check if the user is allowed to update this attempt
      // Only allow users to update their own attempts unless they're an admin
      if (existingAttempt.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to update this attempt" });
      }

      const updates = req.body;

      // Prevent changing the userId (attempt ownership)
      if (updates.userId && updates.userId !== existingAttempt.userId) {
        return res.status(400).json({ message: "Cannot change the user associated with an attempt" });
      }

      // If completing the exam, calculate score and set completedAt if not provided
      if (updates.completed && !updates.completedAt) {
        updates.completedAt = new Date();
      }

      const updatedAttempt = await storage.updateExamAttempt(id, updates);
      res.json(updatedAttempt);
    } catch (error) {
      console.error('Error updating exam attempt:', error);
      res.status(500).json({ message: "Failed to update exam attempt" });
    }
  });

  // ================ EXAM RESULT DETAILS ROUTES ================
  app.post("/api/exam-result-details", requireAuth, async (req: Request, res: Response) => {
    try {
      const details = insertExamResultDetailsSchema.parse(req.body);

      // Check if the attempt exists and belongs to the user
      const attempt = await storage.getExamAttempt(details.attemptId);
      if (!attempt) {
        return res.status(404).json({ message: "Exam attempt not found" });
      }

      if (attempt.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to add details to this attempt" });
      }

      const result = await storage.createExamResultDetails(details);
      res.status(201).json(result);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors });
      }
      console.error('Error creating exam result details:', error);
      res.status(500).json({ message: "Failed to create exam result details" });
    }
  });

  app.get("/api/exam-result-details/:attemptId", requireAuth, async (req: Request, res: Response) => {
    try {
      const attemptId = parseInt(req.params.attemptId);
      if (isNaN(attemptId)) {
        return res.status(400).json({ message: "Invalid attempt ID" });
      }

      // Check if the attempt exists and belongs to the user
      const attempt = await storage.getExamAttempt(attemptId);
      if (!attempt) {
        return res.status(404).json({ message: "Exam attempt not found" });
      }

      if (attempt.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to view these details" });
      }

      const details = await storage.getExamResultDetails(attemptId);
      res.json(details);
    } catch (error) {
      console.error('Error fetching exam result details:', error);
      res.status(500).json({ message: "Failed to fetch exam result details" });
    }
  });

  // ================ AUTH ROUTES ================

  // Add auth routes from auth.ts
  app.use('/api/auth', (await import('./routes/auth')).default);

  // Refresh token endpoint
  app.post("/api/refresh-token", async (req: Request, res: Response) => {
    const { refreshToken } = req.body;
    console.log('Refresh token request received');

    try {
      const { verifyRefreshToken, generateAccessToken, generateRefreshToken } = await import('./utils/jwt');
      const { db } = await import('./db');
      const { refreshTokens, users } = await import('@shared/schema');
      const { eq } = await import('drizzle-orm');

      // Verify refresh token exists and is valid
      const tokenRecord = await db.query.refreshTokens.findFirst({
        where: eq(refreshTokens.token, refreshToken)
      });

      if (!tokenRecord || tokenRecord.revoked || new Date() > tokenRecord.expiresAt) {
        console.log('Invalid or expired refresh token');
        return res.status(401).json({ message: 'Invalid refresh token' });
      }

      // Verify JWT signature
      const tokenPayload = verifyRefreshToken(refreshToken);

      // Get user data
      const user = await db.query.users.findFirst({
        where: eq(users.id, tokenPayload.userId)
      });

      if (!user) {
        console.log('User not found for refresh token');
        return res.status(401).json({ message: 'User not found' });
      }

      // Generate new tokens
      const newTokenPayload = {
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      };

      const newAccessToken = generateAccessToken(newTokenPayload);
      const newRefreshToken = generateRefreshToken(newTokenPayload);

      // Revoke old refresh token and create new one
      await db.update(refreshTokens)
        .set({ revoked: true })
        .where(eq(refreshTokens.token, refreshToken));

      await db.insert(refreshTokens).values({
        userId: user.id,
        token: newRefreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      });

      console.log('Tokens refreshed successfully for user:', user.username);
      res.json({
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(401).json({ message: 'Invalid refresh token' });
    }
  });

  app.post("/api/register", async (req: Request, res: Response) => {
    try {
      const userData = insertUserSchema.parse(req.body);

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(409).json({ message: "Username already exists" });
      }

      // Set default values for new users
      userData.status = userData.status || 'active'; // Set status to active directly

      // Hash the password before saving
      const { hashPassword } = await import('./auth-utils');
      userData.password = hashPassword(userData.password);

      const user = await storage.createUser(userData);

      // Don't send the password back to the client
      const { password, ...userWithoutPassword } = user;

      // Automatically log in the user after registration
      req.session.user = userWithoutPassword;

      res.status(201).json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors });
      }
      console.error('Error registering user:', error);
      res.status(500).json({ message: "Failed to register user" });
    }
  });

  // Logout endpoint
  app.post("/api/logout", (req: Request, res: Response) => {
    console.log('Logout attempt, session ID:', req.sessionID);

    if (req.session.user) {
      console.log(`Logging out user: ${req.session.user.username}, ID: ${req.session.user.id}`);
    } else {
      console.log('No user in session to logout');
    }

    // Destroy session completely
    req.session.destroy((err) => {
      if (err) {
        console.error('Error destroying session:', err);
        return res.status(500).json({ message: "Logout failed" });
      }

      // Clear session cookie
      res.clearCookie('eps_session', {
        path: '/',
        httpOnly: true,
        secure: false,
        sameSite: 'lax'
      });

      console.log('Session destroyed successfully, user logged out');
      res.status(200).json({ message: "Logged out successfully" });
    });
  });

  app.post("/api/login", async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      console.log(`Login attempt for username: ${username}`);
      console.log('Session ID at login start:', req.sessionID);

      if (!username || !password) {
        console.log('Login failed: Username and password are required');
        return res.status(400).json({ message: "Username and password are required" });
      }

      const user = await storage.getUserByUsername(username);

      // If user doesn't exist
      if (!user) {
        console.log(`Login failed: User ${username} not found`);
        return res.status(401).json({ message: "Invalid username or password" });
      }

      console.log(`User found: ${user.username}, ID: ${user.id}, Role: ${user.role}`);
      console.log('Password format check:', {
        hasPassword: !!user.password,
        passwordLength: user.password?.length,
        includesDot: user.password?.includes('.'),
        startsWithBcrypt: user.password?.startsWith('$2')
      });

      let passwordMatch = false;

      // Try different password comparison methods
      if (user.password) {
        // Method 1: Try bcrypt first (most common)
        if (user.password.startsWith('$2')) {
          try {
            const bcrypt = await import('bcryptjs');
            passwordMatch = await bcrypt.compare(password, user.password);
            if (passwordMatch) {
              console.log('Password matched using bcrypt');
            } else {
              console.log('Bcrypt password comparison failed');
            }
          } catch (error) {
            console.error('Error during bcrypt password comparison:', error);
          }
        }
        // Method 2: Try custom hash.salt format
        else if (user.password.includes('.')) {
          try {
            const { comparePassword } = await import('./auth-utils');
            passwordMatch = comparePassword(password, user.password);
            if (passwordMatch) {
              console.log('Password matched using custom hash.salt format');
            } else {
              console.log('Custom hash password comparison failed');
            }
          } catch (error) {
            console.error('Error during custom password comparison:', error);
          }
        }
        // Method 3: Try plain text comparison (for development/testing only)
        else {
          passwordMatch = (password === user.password);
          if (passwordMatch) {
            console.log('Password matched using plain text (WARNING: insecure)');
          } else {
            console.log('Plain text password comparison failed');
          }
        }
      } else {
        console.log('User has no password set');
      }

      // If password doesn't match
      if (!passwordMatch) {
        console.log('Login failed: Invalid password');
        return res.status(401).json({ message: "Invalid username or password" });
      }

      // Update last login time
      await storage.updateUser(user.id, {
        lastLogin: new Date()
      });

      // Don't send the password back to the client
      const { password: _, ...userWithoutPassword } = user;

      // Generate JWT tokens for consistent auth experience
      let accessToken, refreshToken;
      try {
        const tokenPayload = {
          userId: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        };

        const { generateAccessToken, generateRefreshToken } = await import('./utils/jwt');
        const { db } = await import('./db');
        const { refreshTokens } = await import('@shared/schema');

        accessToken = generateAccessToken(tokenPayload);
        refreshToken = generateRefreshToken(tokenPayload);

        // Store refresh token
        await db.insert(refreshTokens).values({
          userId: user.id,
          token: refreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        });

        console.log('JWT tokens generated successfully');
      } catch (jwtError) {
        console.error('Error generating JWT tokens:', jwtError);
        // Continue without JWT tokens - session auth will still work
      }

      // Create a fresh session by regenerating it
      req.session.regenerate((regenerateErr) => {
        if (regenerateErr) {
          console.error('Error regenerating session:', regenerateErr);
          return res.status(500).json({ message: "Session regeneration failed" });
        }

        // Save user information to new session
        req.session.user = userWithoutPassword;
        console.log('User saved to regenerated session:', userWithoutPassword);

        // Save the session explicitly
        req.session.save((saveErr) => {
          if (saveErr) {
            console.error('Error saving session:', saveErr);
            return res.status(500).json({ message: "Failed to save session" });
          }

          console.log('Session saved successfully, new session ID:', req.sessionID);
          console.log('Login successful, returning user data with tokens');

          const response = {
            ...userWithoutPassword,
            sessionId: req.sessionID, // Include session ID for debugging
            message: "Login successful - redirecting to dashboard"
          };

          // Add JWT tokens if they were generated
          if (accessToken) {
            response.accessToken = accessToken;
          }
          if (refreshToken) {
            response.refreshToken = refreshToken;
          }

          res.json(response);
        });
      });
    } catch (error) {
      console.error('Error during login:', error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  // ================ DASHBOARD ROUTES ================
  app.get("/api/dashboard/stats", requireAdmin, async (req: Request, res: Response) => {
    try {
      const [
        usersCount,
        activeUsersCount,
        examsCount,
        attemptsCount,
        completedAttemptsCount,
        topExams,
        examScores
      ] = await Promise.all([
        storage.getUsersCount(),
        storage.getActiveUsersCount(),
        storage.getExamsCount(),
        storage.getExamAttemptsCount(),
        storage.getCompletedExamAttemptsCount(),
        storage.getTopExams(5),
        storage.getExamAverageScores()
      ]);

      res.json({
        usersCount,
        activeUsersCount,
        examsCount,
        attemptsCount,
        completedAttemptsCount,
        topExams,
        examScores
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      res.status(500).json({ message: "Failed to fetch dashboard stats" });
    }
  });

  // ================ PROXY ROUTES ================
  // Get direct URL for Google Drive files
  app.get("/api/direct-url/gdrive", async (req: Request, res: Response) => {
    try {
      const fileId = req.query.id;

      if (!fileId || typeof fileId !== 'string') {
        console.error('Missing or invalid file ID:', fileId);
        return res.status(400).json({ error: 'Missing or invalid file ID' });
      }

      // Validate file ID format (basic validation)
      if (!/^[a-zA-Z0-9_-]+$/.test(fileId)) {
        console.error('Invalid file ID format:', fileId);
        return res.status(400).json({ error: 'Invalid file ID format' });
      }

      // Create direct URL for Google Drive file
      // This URL should work directly in audio/video elements
      const directUrl = `https://docs.google.com/uc?export=download&id=${fileId}`;

      // Verify the file exists and is accessible
      try {
        console.log(`Verifying file access for ID: ${fileId}`);
        const response = await fetch(directUrl, { method: 'HEAD' });

        if (!response.ok) {
          console.error(`File access verification failed: ${response.status} ${response.statusText}`);
          return res.status(404).json({
            error: 'File not accessible',
            details: `Status: ${response.status} ${response.statusText}`
          });
        }

        // Check if we're getting HTML instead of file (Google Drive access denied page)
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('text/html')) {
          console.warn(`File might require authentication. Content-Type: ${contentType}`);
          // We'll still return the URL but log a warning
        }

        console.log(`File verified. Content-Type: ${contentType}`);
      } catch (verifyError) {
        console.error('Error verifying file access:', verifyError);
        // Continue despite verification error - client will handle fallback
      }

      // Log success
      console.log(`Generated direct URL for file ID: ${fileId}`);

      // Return the direct URL to the client
      res.json({
        url: directUrl,
        fileId,
        alternateUrl: `/api/proxy/gdrive?id=${fileId}` // Include fallback URL
      });
    } catch (error) {
      console.error('Error generating direct URL:', error);
      res.status(500).json({ error: 'Failed to generate direct URL' });
    }
  });

  // Proxy for Google Drive files to avoid CORS issues (legacy method)
  app.get("/api/proxy/gdrive", async (req: Request, res: Response) => {
    try {
      const fileId = req.query.id;

      if (!fileId || typeof fileId !== 'string') {
        console.error('Missing or invalid file ID:', fileId);
        return res.status(400).json({ error: 'Missing or invalid file ID' });
      }

      // Validate file ID format (basic validation)
      if (!/^[a-zA-Z0-9_-]+$/.test(fileId)) {
        console.error('Invalid file ID format:', fileId);
        return res.status(400).json({ error: 'Invalid file ID format' });
      }

      console.log(`Proxying file with ID: ${fileId}`);

      // Create URL for Google Drive file
      const url = `https://drive.usercontent.google.com/download?id=${fileId}&export=download&authuser=0&confirm=t`;

      // Fetch the file from Google Drive with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`Failed to fetch file: ${response.status} ${response.statusText}`);
        return res.status(response.status).json({
          error: `Failed to fetch file: ${response.statusText}`,
          status: response.status
        });
      }

      // Get content type and size from response
      const contentType = response.headers.get('content-type') || 'application/octet-stream';
      const contentLength = response.headers.get('content-length');

      // Check file size limit (100MB)
      if (contentLength && parseInt(contentLength) > 100 * 1024 * 1024) {
        return res.status(413).send('File too large');
      }

      // Set appropriate headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
      res.setHeader('Cache-Control', 'no-cache');

      // Check if we're getting HTML instead of file (Google Drive access denied page)
      if (contentType.includes('text/html')) {
        console.warn('Received HTML instead of file - might be access denied page');
        // We'll still return the content but log a warning
      }

      console.log(`Streaming file with content type: ${contentType}, size: ${contentLength || 'unknown'}`);

      // Stream the response
      if (!response.body) {
        return res.status(500).send('No response body');
      }

      // Get a reader once from the response body
      const reader = response.body.getReader();

      // Convert ReadableStream to Node.js Readable using a different approach
      const nodeReadable = new Readable({
        read() {
          // This is intentionally empty as we'll push data manually
        }
      });

      // Start the reading process
      (async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              nodeReadable.push(null);
              break;
            }
            // Only push if the stream hasn't been destroyed
            if (!nodeReadable.destroyed) {
              const canContinue = nodeReadable.push(value);
              // If backpressure is detected, wait for drain event
              if (!canContinue) {
                await new Promise(resolve => nodeReadable.once('drain', resolve));
              }
            }
          }
        } catch (error) {
          console.error('Error reading from stream:', error);
          nodeReadable.destroy(error as Error);
        }
      })();

      nodeReadable.pipe(res);

      // Handle errors during streaming
      nodeReadable.on('error', (error: Error) => {
        console.error('Streaming error:', error);
        if (!res.headersSent) {
          res.status(500).send('Error streaming file');
        } else if (!res.writableEnded) {
          res.end();
        }
      });

      // Handle client disconnect
      res.on('close', () => {
        if (nodeReadable && !nodeReadable.destroyed) {
          nodeReadable.destroy();
        }
      });

    } catch (error: unknown) {
      console.error('Proxy error:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        res.status(504).send('Request timeout');
      } else {
        res.status(500).send('Error proxying Google Drive file');
      }
    }
  });

  // Create and return HTTP server (will be started by index.ts)
  const server = createServer(app);
  return server;
}