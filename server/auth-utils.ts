import crypto from 'crypto';

/**
 * Securely hash a password using scrypt with a random salt
 * @param password The plaintext password to hash
 * @returns The hashed password with salt in the format 'hash.salt'
 */
export function hashPassword(password: string): string {
  // Create a random salt
  const salt = crypto.randomBytes(16).toString('hex');
  
  // Use scrypt to hash the password with the salt (64 bytes output)
  const hash = crypto.scryptSync(password, salt, 64).toString('hex');
  
  // Return in the format 'hash.salt'
  return `${hash}.${salt}`;
}

/**
 * Compare a provided password with a stored hashed password
 * @param providedPassword The plaintext password to check
 * @param storedPassword The stored hashed password
 * @returns True if passwords match, false otherwise
 */
export function comparePassword(providedPassword: string, storedPassword: string): boolean {
  try {
    // Check if the stored password contains a salt (indicated by a period)
    if (!storedPassword.includes('.')) {
      console.log('Password format does not contain salt separator');
      return false;
    }
    
    // Split the hash and salt from the stored password
    const [hashedPassword, salt] = storedPassword.split('.');
    
    if (!hashedPassword || !salt) {
      console.log('Invalid password format, missing hash or salt');
      return false;
    }
    
    // Create hash from the provided password with the stored salt
    const hash = crypto.scryptSync(providedPassword, salt, 64).toString('hex');
    
    // Compare the two hashes
    const match = hash === hashedPassword;
    return match;
  } catch (error) {
    console.error('Error comparing passwords:', error);
    return false;
  }
}