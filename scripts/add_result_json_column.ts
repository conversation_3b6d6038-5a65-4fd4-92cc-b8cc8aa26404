import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { sql } from 'drizzle-orm';

async function main() {
  // Create a new pool
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  // Create the drizzle database instance
  const db = drizzle(pool);

  console.log('Adding result_json column to exam_attempts table...');

  try {
    // Add the result_json column
    await db.execute(sql`
      ALTER TABLE exam_attempts
      ADD COLUMN IF NOT EXISTS result_json JSONB;
    `);

    console.log('Successfully added result_json column');
  } catch (error) {
    console.error('Error adding result_json column:', error);
    process.exit(1);
  }

  await pool.end();
  process.exit(0);
}

main().catch(console.error); 