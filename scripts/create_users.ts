// Script để tạo tài khoản người dùng admin và người dùng thường
import { db } from '../server/db';
import { users } from '../shared/schema';
import { hashPassword } from '../server/auth-utils';
import { eq } from 'drizzle-orm';

async function createUser(username: string, password: string, fullName: string, role: 'admin' | 'user') {
  try {
    // Kiểm tra xem người dùng đã tồn tại chưa
    const existingUsers = await db
      .select()
      .from(users)
      .where(eq(users.username, username));

    if (existingUsers.length > 0) {
      console.log(`User ${username} already exists, skipping...`);
      return;
    }

    // Tạo người dùng mới
    const hashedPassword = hashPassword(password);
    await db.insert(users).values({
      username,
      password: hashedPassword,
      fullName,
      role,
      status: 'active',
      createdAt: new Date()
    });

    console.log(`Created ${role} user: ${username}`);
  } catch (error) {
    console.error(`Error creating user ${username}:`, error);
  }
}

async function main() {
  try {
    // Tạo tài khoản admin
    await createUser('frankie', 'Tamdao@1', 'Frankie Admin', 'admin');
    
    // Tạo tài khoản thường
    await createUser('normUser', 'Tamdao@1', 'Normal User', 'user');
    
    // Tạo tài khoản test
    await createUser('testAcc', 'Testpwd1', 'Test Account', 'user');

    console.log('User accounts created successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error creating user accounts:', error);
    process.exit(1);
  }
}

main();