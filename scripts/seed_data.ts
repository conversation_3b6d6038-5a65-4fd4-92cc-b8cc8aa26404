import { db } from '../server/db';
import { exams, users, examAttempts } from '../shared/schema';
import { createHash } from 'crypto';

// Helper functions
function hashPassword(password: string): string {
  return createHash('sha256').update(password).digest('hex');
}

// Clear existing data
async function clearDatabase() {
  console.log('Clearing existing data...');
  await db.delete(examAttempts);
  await db.delete(exams);
  await db.delete(users);
  console.log('Database cleared');
}

// Insert seed data
async function seedDatabase() {
  try {
    // Clear existing data first
    await clearDatabase();
    
    console.log('Inserting seed data...');
    
    // Insert admin user
    const [admin] = await db.insert(users).values({
      username: 'admin',
      password: hashPassword('adminpass'),
      email: '<EMAIL>',
      fullName: 'Admin User',
      role: 'admin',
      status: 'active',
    }).returning();
    
    console.log('Admin user created:', admin.id);
    
    // Insert regular users
    const [user1] = await db.insert(users).values({
      username: 'user1',
      password: hashPassword('password1'),
      email: '<EMAIL>',
      fullName: 'Regular User 1',
      role: 'user',
      status: 'active',
    }).returning();
    
    const [user2] = await db.insert(users).values({
      username: 'user2',
      password: hashPassword('password2'),
      email: '<EMAIL>',
      fullName: 'Regular User 2',
      role: 'user',
      status: 'active',
    }).returning();
    
    console.log('Regular users created:', user1.id, user2.id);
    
    // Create sample exam with reading and listening sections
    const sampleExam1Questions = [
      // Reading section (10 questions)
      {
        id: 1,
        type: "reading",
        text: "다음 중 '안전벨트'를 나타내는 표지는 무엇입니까?",
        subText: "다음 중 안전 표지를 올바르게 고르세요.",
        hasImageOptions: true,
        options: [
          {
            id: 1,
            imageUrl: "https://i.ibb.co/VxLXxQB/safety-vest.png"
          },
          {
            id: 2,
            imageUrl: "https://i.ibb.co/jVkJN3n/safety-helmet.png"
          },
          {
            id: 3,
            imageUrl: "https://i.ibb.co/1GQ9vZ3/safety-belt.png"
          },
          {
            id: 4,
            imageUrl: "https://i.ibb.co/FqXWbKt/fire-extinguisher.png"
          }
        ],
        correctAnswer: 3
      },
      {
        id: 2,
        type: "reading",
        text: "다음 문장의 뜻으로 가장 알맞은 것을 고르십시오.",
        subText: "작업장에서는 항상 안전모를 착용해야 합니다.",
        options: [
          {
            id: 1,
            text: "작업장에서는 안전모를 벗어도 됩니다."
          },
          {
            id: 2,
            text: "작업장에 들어가기 전에 안전모를 벗어야 합니다."
          },
          {
            id: 3,
            text: "작업장에서는 반드시 안전모를 써야 합니다."
          },
          {
            id: 4,
            text: "작업장에서는 안전모를 쓰지 않아도 됩니다."
          }
        ],
        correctAnswer: 3
      },
      {
        id: 3,
        type: "reading",
        text: "다음 글을 읽고 물음에 답하십시오.",
        subText: "직원들은 매일 아침 8시까지 출근해야 합니다. 지각하면 급여에서 차감됩니다. 3번 이상 지각하면 경고를 받게 됩니다.",
        options: [
          {
            id: 1,
            text: "출근 시간은 9시입니다."
          },
          {
            id: 2,
            text: "지각하면 돈을 내야 합니다."
          },
          {
            id: 3,
            text: "2번 지각하면 경고를 받습니다."
          },
          {
            id: 4,
            text: "지각해도 급여는 차감되지 않습니다."
          }
        ],
        correctAnswer: 2
      },
      {
        id: 4,
        type: "reading",
        text: "다음 그림을 보고 알맞은 설명을 고르십시오.",
        hasImageOptions: false,
        imageUrl: "https://i.ibb.co/HrfBK19/no-smoking.png",
        options: [
          {
            id: 1,
            text: "여기에서는 담배를 피워도 됩니다."
          },
          {
            id: 2,
            text: "여기에서는 담배를 피울 수 없습니다."
          },
          {
            id: 3,
            text: "여기에서는 담배를 살 수 있습니다."
          },
          {
            id: 4,
            text: "여기에서는 담배를 팔고 있습니다."
          }
        ],
        correctAnswer: 2
      },
      {
        id: 5,
        type: "reading",
        text: "다음 안내문의 내용과 일치하는 것을 고르십시오.",
        subText: "공지사항: 내일 오전 10시부터 12시까지 전기 점검이 있어서 작업을 중단합니다. 모든 직원은 이 시간에 안전 교육에 참석해 주시기 바랍니다.",
        options: [
          {
            id: 1,
            text: "내일 오후에 전기 점검이 있습니다."
          },
          {
            id: 2,
            text: "전기 점검 시간에 작업을 계속합니다."
          },
          {
            id: 3,
            text: "전기 점검은 2시간 동안 진행됩니다."
          },
          {
            id: 4,
            text: "안전 교육은 선택사항입니다."
          }
        ],
        correctAnswer: 3
      },
      {
        id: 6,
        type: "reading",
        text: "다음 중 작업장에서 '금지'되는 행동을 고르십시오.",
        hasImageOptions: true,
        options: [
          {
            id: 1,
            imageUrl: "https://i.ibb.co/0MZzMKd/wearing-mask.png"
          },
          {
            id: 2,
            imageUrl: "https://i.ibb.co/47kxnxZ/using-phone.png"
          },
          {
            id: 3,
            imageUrl: "https://i.ibb.co/FhVVnMg/washing-hands.png"
          },
          {
            id: 4,
            imageUrl: "https://i.ibb.co/LPdYnCZ/wearing-helmet.png"
          }
        ],
        correctAnswer: 2
      },
      {
        id: 7,
        type: "reading",
        text: "다음 안내문의 내용과 일치하는 것을 고르십시오.",
        subText: "점심 시간은 12시부터 1시까지입니다. 식사 후에는 반드시，식당 뒷정리를 해 주세요.",
        options: [
          {
            id: 1,
            text: "점심 시간은 1시간입니다."
          },
          {
            id: 2,
            text: "점심은 11시에 시작합니다."
          },
          {
            id: 3,
            text: "식사 후 설거지는 다른 사람이 합니다."
          },
          {
            id: 4,
            text: "식당은 오후 2시에 문을 닫습니다."
          }
        ],
        correctAnswer: 1
      },
      {
        id: 8,
        type: "reading",
        text: "다음 두 사람의 대화 내용으로 보아 남자가 해야 할 일이 아닌 것은 무엇입니까?",
        subText: "여자: 철수 씨, 제품이 완성되면 검사하고 포장해 주세요.\n남자: 네, 알겠습니다. 그리고 납품 일정도 확인해야 하나요?\n여자: 아니요, 납품 일정은 제가 확인할게요. 그냥 검사와 포장만 해 주세요.",
        options: [
          {
            id: 1,
            text: "제품 완성하기"
          },
          {
            id: 2,
            text: "제품 검사하기"
          },
          {
            id: 3,
            text: "제품 포장하기"
          },
          {
            id: 4,
            text: "납품 일정 확인하기"
          }
        ],
        correctAnswer: 4
      },
      {
        id: 9,
        type: "reading",
        text: "다음 글에서 알 수 있는 내용으로 알맞은 것을 고르십시오.",
        subText: "저는 한국에서 3년 동안 제조업체에서 일했습니다. 처음에는 한국어를 잘 몰라서 어려웠지만 지금은 많이 익숙해졌습니다. 한국에서 돈을 모아 고향에 집을 짓고 싶습니다.",
        options: [
          {
            id: 1,
            text: "현재 한국어를 공부하고 있습니다."
          },
          {
            id: 2,
            text: "한국에서 1년 동안 일했습니다."
          },
          {
            id: 3,
            text: "고향에 집을 짓고 싶어합니다."
          },
          {
            id: 4,
            text: "공장에서 일하는 것이 쉽습니다."
          }
        ],
        correctAnswer: 3
      },
      {
        id: 10,
        type: "reading",
        text: "다음 중 비상 상황에서 해야 할 일을 순서대로 나열한 것을 고르십시오.",
        options: [
          {
            id: 1,
            text: "1. 비상벨 누르기 2. 대피하기 3. 담당자에게 보고하기"
          },
          {
            id: 2,
            text: "1. 대피하기 2. 비상벨 누르기 3. 담당자에게 보고하기"
          },
          {
            id: 3,
            text: "1. 담당자에게 보고하기 2. 비상벨 누르기 3. 대피하기"
          },
          {
            id: 4,
            text: "1. 대피하기 2. 담당자에게 보고하기 3. 비상벨 누르기"
          }
        ],
        correctAnswer: 1
      },
      // Listening section (2 questions)
      {
        id: 11,
        type: "listening",
        text: "들은 내용과 관계있는 그림을 고르십시오.",
        subText: "오디오: '작업장에서는 항상 안전장갑을 착용하십시오.'",
        hasImageOptions: true,
        options: [
          {
            id: 1,
            imageUrl: "https://i.ibb.co/wWFkw9D/safety-gloves.png"
          },
          {
            id: 2,
            imageUrl: "https://i.ibb.co/jVkJN3n/safety-helmet.png"
          },
          {
            id: 3,
            imageUrl: "https://i.ibb.co/DtbWC5C/safety-boots.png"
          },
          {
            id: 4,
            imageUrl: "https://i.ibb.co/VxLXxQB/safety-vest.png"
          }
        ],
        correctAnswer: 1
      },
      {
        id: 12,
        type: "listening",
        text: "다음 대화를 듣고 남자가 전화를 건 목적을 고르십시오.",
        subText: "오디오 대화: \n남자: '여보세요, 김 과장님 계십니까?'\n여자: '네, 잠시만 기다려 주세요.'\n남자: '김 과장님, 오늘 회의 시간이 변경되었습니다. 3시가 아니라 4시에 시작합니다.'",
        options: [
          {
            id: 1,
            text: "회의 장소를 알려주기 위해"
          },
          {
            id: 2,
            text: "회의 시간 변경을 알려주기 위해"
          },
          {
            id: 3,
            text: "회의 취소를 알려주기 위해"
          },
          {
            id: 4,
            text: "과장을 찾기 위해"
          }
        ],
        correctAnswer: 2
      }
    ];
    
    const [exam1] = await db.insert(exams).values({
      title: "EPS-TOPIK 기출 모의고사 1회",
      description: "2023년 EPS-TOPIK 기출문제 기반 모의고사입니다. 읽기와 듣기 섹션을 포함합니다.",
      duration: 70,
      readingTime: 40,
      listeningTime: 30,
      questions: sampleExam1Questions,
      isActive: true,
      createdBy: admin.id
    }).returning();
    
    console.log('Sample exam 1 created:', exam1.id);

    // Create a second sample exam
    const sampleExam2Questions = [
      // Reading section (10 questions)
      {
        id: 1,
        type: "reading",
        text: "다음 표지판의 의미로 맞는 것을 고르십시오.",
        hasImageOptions: false,
        imageUrl: "https://i.ibb.co/zQM9LxF/danger-high-voltage.png",
        options: [
          {
            id: 1,
            text: "위험! 고압 전기"
          },
          {
            id: 2,
            text: "여기서 휴식하세요"
          },
          {
            id: 3,
            text: "전기 사용 가능"
          },
          {
            id: 4,
            text: "조명 스위치"
          }
        ],
        correctAnswer: 1
      },
      {
        id: 2,
        type: "reading",
        text: "다음 중 작업장에서 '금지'되는 행동을 고르십시오.",
        hasImageOptions: true,
        options: [
          {
            id: 1,
            imageUrl: "https://i.ibb.co/0MZzMKd/wearing-mask.png"
          },
          {
            id: 2,
            imageUrl: "https://i.ibb.co/47kxnxZ/using-phone.png"
          },
          {
            id: 3,
            imageUrl: "https://i.ibb.co/FhVVnMg/washing-hands.png"
          },
          {
            id: 4,
            imageUrl: "https://i.ibb.co/LPdYnCZ/wearing-helmet.png"
          }
        ],
        correctAnswer: 2
      },
      {
        id: 3,
        type: "reading",
        text: "다음 안내문의 내용과 일치하는 것을 고르십시오.",
        subText: "점심 시간은 12시부터 1시까지입니다. 식사 후에는 반드시，식당 뒷정리를 해 주세요.",
        options: [
          {
            id: 1,
            text: "점심 시간은 1시간입니다."
          },
          {
            id: 2,
            text: "점심은 11시에 시작합니다."
          },
          {
            id: 3,
            text: "식사 후 설거지는 다른 사람이 합니다."
          },
          {
            id: 4,
            text: "식당은 오후 2시에 문을 닫습니다."
          }
        ],
        correctAnswer: 1
      },
      {
        id: 4,
        type: "reading",
        text: "다음 두 사람의 대화 내용으로 보아 남자가 해야 할 일이 아닌 것은 무엇입니까?",
        subText: "여자: 철수 씨, 제품이 완성되면 검사하고 포장해 주세요.\n남자: 네, 알겠습니다. 그리고 납품 일정도 확인해야 하나요?\n여자: 아니요, 납품 일정은 제가 확인할게요. 그냥 검사와 포장만 해 주세요.",
        options: [
          {
            id: 1,
            text: "제품 완성하기"
          },
          {
            id: 2,
            text: "제품 검사하기"
          },
          {
            id: 3,
            text: "제품 포장하기"
          },
          {
            id: 4,
            text: "납품 일정 확인하기"
          }
        ],
        correctAnswer: 4
      },
      {
        id: 5,
        type: "reading",
        text: "다음 글에서 알 수 있는 내용으로 알맞은 것을 고르십시오.",
        subText: "저는 한국에서 3년 동안 제조업체에서 일했습니다. 처음에는 한국어를 잘 몰라서 어려웠지만 지금은 많이 익숙해졌습니다. 한국에서 돈을 모아 고향에 집을 짓고 싶습니다.",
        options: [
          {
            id: 1,
            text: "현재 한국어를 공부하고 있습니다."
          },
          {
            id: 2,
            text: "한국에서 1년 동안 일했습니다."
          },
          {
            id: 3,
            text: "고향에 집을 짓고 싶어합니다."
          },
          {
            id: 4,
            text: "공장에서 일하는 것이 쉽습니다."
          }
        ],
        correctAnswer: 3
      },
      {
        id: 6,
        type: "reading",
        text: "다음 중 기계를 작동하기 전에 확인해야 할 사항을 고르십시오.",
        options: [
          {
            id: 1,
            text: "안전장비 착용 여부"
          },
          {
            id: 2,
            text: "기계 상태 점검"
          },
          {
            id: 3,
            text: "작업 환경 확인"
          },
          {
            id: 4,
            text: "모든 위 사항"
          }
        ],
        correctAnswer: 4
      },
      {
        id: 7,
        type: "reading",
        text: "다음 중 기계 작동 중 주의해야 할 사항을 고르십시오.",
        options: [
          {
            id: 1,
            text: "기계 소음에 주의하기"
          },
          {
            id: 2,
            text: "기계 상태 모니터링하기"
          },
          {
            id: 3,
            text: "갑작스러운 정지에 대비하기"
          },
          {
            id: 4,
            text: "모든 위 사항"
          }
        ],
        correctAnswer: 4
      },
      {
        id: 8,
        type: "reading",
        text: "다음 중 화재 발생 시 해야 할 일을 고르십시오.",
        options: [
          {
            id: 1,
            text: "소화기로 불을 끄고 대피하기"
          },
          {
            id: 2,
            text: "바로 대피하기"
          },
          {
            id: 3,
            text: "담당자에게 보고하고 대피하기"
          },
          {
            id: 4,
            text: "불이 작으면 계속 작업하기"
          }
        ],
        correctAnswer: 2
      },
      {
        id: 9,
        type: "reading",
        text: "다음 중 작업장에서 발생할 수 있는 사고의 원인을 고르십시오.",
        options: [
          {
            id: 1,
            text: "안전장비 미착용"
          },
          {
            id: 2,
            text: "기계 조작 미숙"
          },
          {
            id: 3,
            text: "작업 환경 불량"
          },
          {
            id: 4,
            text: "모든 위 사항"
          }
        ],
        correctAnswer: 4
      },
      {
        id: 10,
        type: "reading",
        text: "다음 중 작업장에서 지켜야 할 안전 수칙을 고르십시오.",
        options: [
          {
            id: 1,
            text: "안전장비 착용하기"
          },
          {
            id: 2,
            text: "작업 지시 따르기"
          },
          {
            id: 3,
            text: "비상구 확인하기"
          },
          {
            id: 4,
            text: "모든 위 사항"
          }
        ],
        correctAnswer: 4
      },
      // Listening section (2 questions)
      {
        id: 11,
        type: "listening",
        text: "들은 내용과 일치하는 그림을 고르십시오.",
        subText: "오디오: '건물 2층에서 불이 났습니다. 모든 직원은 즉시 대피하십시오.'",
        hasImageOptions: true,
        options: [
          {
            id: 1,
            imageUrl: "https://i.ibb.co/FXk0mbF/emergency-exit.png"
          },
          {
            id: 2,
            imageUrl: "https://i.ibb.co/wB6RFqM/fire-alarm.png"
          },
          {
            id: 3,
            imageUrl: "https://i.ibb.co/9rxkT3D/building-construction.png"
          },
          {
            id: 4,
            imageUrl: "https://i.ibb.co/CJshpX7/elevator.png"
          }
        ],
        correctAnswer: 2
      },
      {
        id: 12,
        type: "listening",
        text: "다음을 듣고 여자가 남자에게 부탁한 것을 고르십시오.",
        subText: "오디오: '내일까지 이 서류를 작성해서 제출해 주세요. 특히 마지막 페이지는 꼼꼼하게 확인해 주세요.'",
        options: [
          {
            id: 1,
            text: "내일 회의에 참석하기"
          },
          {
            id: 2,
            text: "오늘 서류 작성하기"
          },
          {
            id: 3,
            text: "서류 작성하고 제출하기"
          },
          {
            id: 4,
            text: "마지막 페이지 삭제하기"
          }
        ],
        correctAnswer: 3
      }
    ];

    const [exam2] = await db.insert(exams).values({
      title: "EPS-TOPIK 실전 모의고사 2회",
      description: "한국 산업 현장에서 자주 사용되는 용어와 상황 중심의 실전 모의고사입니다.",
      duration: 70,
      readingTime: 40,
      listeningTime: 30,
      questions: sampleExam2Questions,
      isActive: true,
      createdBy: admin.id
    }).returning();
    
    console.log('Sample exam 2 created:', exam2.id);
    
    // Create exam attempts
    const [attempt1] = await db.insert(examAttempts).values({
      userId: user1.id,
      examId: exam1.id,
      answers: { "1": 3, "2": 3, "3": 2, "4": 2, "5": 3 },
      score: 5,
      completed: true,
      completedAt: new Date()
    }).returning();
    
    const [attempt2] = await db.insert(examAttempts).values({
      userId: user2.id,
      examId: exam1.id,
      answers: { "1": 3, "2": 1, "3": 2, "4": 2, "5": 1 },
      score: 3,
      completed: true,
      completedAt: new Date()
    }).returning();
    
    const [attempt3] = await db.insert(examAttempts).values({
      userId: user1.id,
      examId: exam2.id,
      answers: {},
      completed: false
    }).returning();
    
    console.log('Exam attempts created:', attempt1.id, attempt2.id, attempt3.id);
    
    console.log('Seed data inserted successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the seed function
seedDatabase();