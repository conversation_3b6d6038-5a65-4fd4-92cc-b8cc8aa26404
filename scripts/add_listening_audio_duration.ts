import { Client } from 'pg';

async function addListeningAudioDuration() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgres://postgres@localhost:5432/eps_simulator',
  });
  await client.connect();

  console.log('Connected to database. Starting migration...');

  try {
    // Add listeningAudioDuration column if it doesn't exist
    await client.query(`
      ALTER TABLE exams 
      ADD COLUMN IF NOT EXISTS "listeningAudioDuration" INTEGER;
    `);
    console.log('Added listeningAudioDuration column to exams table');

    // Update existing exams to set listeningAudioDuration equal to listeningTime
    // This is a reasonable default since both should be in seconds
    await client.query(`
      UPDATE exams 
      SET "listeningAudioDuration" = listening_time 
      WHERE "listeningAudioDuration" IS NULL;
    `);
    console.log('Updated existing exams with default listeningAudioDuration values');

    console.log('Migration completed successfully.');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the migration
addListeningAudioDuration().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
