// Script to fix all users' passwords to use the secure hash.salt format
import { db } from '../server/db';
import { users } from '../shared/schema';
import { hashPassword } from '../server/auth-utils';
import { eq } from 'drizzle-orm';

const PASSWORD_MAP = {
  'admin': 'adminpass',
  'user1': 'password1',
  'user2': 'password2',
  'testAcc': 'Testpwd1',
  'frankie': 'Tamdao@1',
  'normUser': 'Tamdao@1'
};

type UserRow = {
  id: number;
  username: string;
  password: string;
};

async function updateUserPassword(user: UserRow): Promise<void> {
  try {
    const knownPassword = PASSWORD_MAP[user.username as keyof typeof PASSWORD_MAP];
    
    if (!knownPassword) {
      console.log(`No known password for user ${user.username}, skipping...`);
      return;
    }
    
    // Skip users that already have secure passwords (in the hash.salt format)
    if (user.password.includes('.') && user.password.length > 100) {
      console.log(`User ${user.username} already has a secure password, skipping...`);
      return;
    }
    
    // Otherwise, update the password with a secure hash
    const securePassword = hashPassword(knownPassword);
    
    await db
      .update(users)
      .set({ password: securePassword })
      .where(eq(users.id, user.id));
    
    console.log(`Updated password for user ${user.username}`);
  } catch (error) {
    console.error(`Error updating user ${user.username}:`, error);
  }
}

async function main() {
  try {
    // Get all users
    const allUsers = await db.select().from(users);
    
    console.log(`Found ${allUsers.length} users to process`);
    
    // Update passwords for each user
    for (const user of allUsers) {
      await updateUserPassword(user);
    }
    
    console.log('Finished updating user passwords');
    process.exit(0);
  } catch (error) {
    console.error('Error in main function:', error);
    process.exit(1);
  }
}

main();