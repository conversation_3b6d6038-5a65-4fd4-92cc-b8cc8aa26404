import trafilatura
import json

# URLs to scrape for EPS-TOPIK questions
urls = [
    "https://www.koreanclass101.com/blog/2022/02/19/korean-eps-topik-practice-test/",
    "https://www.90daykorean.com/eps-topik/",
    "https://www.topik.go.kr/TWOSVCR/TWOSVCR14R01.do",
    "https://stukoreaeducation.com/eps-topik/"
]

def fetch_content(url):
    try:
        print(f"Fetching content from {url}...")
        downloaded = trafilatura.fetch_url(url)
        text = trafilatura.extract(downloaded)
        return text
    except Exception as e:
        print(f"Error fetching {url}: {e}")
        return None

# Fetch and save content from all URLs
all_content = []
for url in urls:
    content = fetch_content(url)
    if content:
        all_content.append({"url": url, "content": content})

# Save all fetched content to a file
with open('eps_content.json', 'w', encoding='utf-8') as f:
    json.dump(all_content, f, ensure_ascii=False, indent=2)

print("Content fetched and saved to eps_content.json")