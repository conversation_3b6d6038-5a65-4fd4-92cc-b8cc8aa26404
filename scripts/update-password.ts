import { db } from '../server/db';
import { users } from '../shared/schema';
import { hashPassword } from '../server/auth-utils';
import { eq } from 'drizzle-orm';

async function updatePassword(username: string, newPassword: string) {
  try {
    // Find the user
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.username, username)
    });

    if (!user) {
      console.error(`User ${username} not found`);
      process.exit(1);
    }

    // Hash the new password
    const hashedPassword = hashPassword(newPassword);

    // Update the user's password
    await db.update(users)
      .set({ password: hashedPassword })
      .where(eq(users.id, user.id));

    console.log(`Password updated successfully for user ${username}`);
    process.exit(0);
  } catch (error) {
    console.error('Error updating password:', error);
    process.exit(1);
  }
}

// Update password for admin user
updatePassword('frankie', 'Tamdao@1'); 