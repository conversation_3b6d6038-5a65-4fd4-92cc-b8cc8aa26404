import { Client } from 'pg';

async function migrate() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgres://postgres@localhost:5432/eps_simulator',
  });
  await client.connect();

  // 1. Thêm trường listeningAudioUrl vào exams nếu chưa có
  await client.query(`ALTER TABLE exams ADD COLUMN IF NOT EXISTS "listeningAudioUrl" TEXT;`);

  // 2. Duyệt qua từng exam để migrate audioUrl và duration
  const { rows: exams } = await client.query('SELECT id, questions FROM exams');
  for (const exam of exams) {
    let questions = exam.questions;
    if (typeof questions === 'string') {
      questions = JSON.parse(questions);
    }
    let listeningAudioUrl = null;
    let changed = false;
    for (const q of questions) {
      if (q.type === 'listening') {
        // Lấy audioUrl đầu tiên
        if (!listeningAudioUrl && q.audioUrl) {
          listeningAudioUrl = q.audioUrl;
        }
        // Xóa audioUrl khỏi question
        if (q.audioUrl) {
          delete q.audioUrl;
          changed = true;
        }
        // Đảm bảo có trường duration
        if (q.duration === undefined) {
          q.duration = 0;
          changed = true;
        }
      }
    }
    if (changed || listeningAudioUrl) {
      await client.query(
        'UPDATE exams SET questions = $1, "listeningAudioUrl" = $2 WHERE id = $3',
        [JSON.stringify(questions), listeningAudioUrl, exam.id]
      );
      console.log(`Exam ${exam.id} migrated. listeningAudioUrl: ${listeningAudioUrl}`);
    }
  }

  // 3. Xóa toàn bộ record của exam_attempts
  await client.query('DELETE FROM exam_attempts;');
  console.log('All exam_attempts records deleted.');

  await client.end();
  console.log('Migration completed.');
}

migrate().catch(e => {
  console.error('Migration failed:', e);
  process.exit(1);
}); 