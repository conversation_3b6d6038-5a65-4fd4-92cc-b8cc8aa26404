import { Client } from 'pg';

async function migrateTimeToSeconds() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgres://postgres@localhost:5432/eps_simulator',
  });
  await client.connect();

  console.log('Connected to database. Starting migration...');

  try {
    // 1. Get all exams
    const { rows: exams } = await client.query('SELECT id, reading_time, listening_time FROM exams');
    console.log(`Found ${exams.length} exams to process.`);

    // 2. Process each exam
    for (const exam of exams) {
      const { id, reading_time, listening_time } = exam;
      
      // Check if values are likely in minutes (less than 100)
      const shouldConvertReading = reading_time < 100;
      const shouldConvertListening = listening_time < 100;
      
      if (shouldConvertReading || shouldConvertListening) {
        // Convert minutes to seconds
        const newReadingTime = shouldConvertReading ? reading_time * 60 : reading_time;
        const newListeningTime = shouldConvertListening ? listening_time * 60 : listening_time;
        
        // Update the exam
        await client.query(
          'UPDATE exams SET reading_time = $1, listening_time = $2 WHERE id = $3',
          [newReadingTime, newListeningTime, id]
        );
        
        console.log(`Exam ${id} updated: reading_time ${reading_time} -> ${newReadingTime}, listening_time ${listening_time} -> ${newListeningTime}`);
      } else {
        console.log(`Exam ${id} already using seconds: reading_time ${reading_time}, listening_time ${listening_time}`);
      }
    }

    console.log('Migration completed successfully.');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the migration
migrateTimeToSeconds().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
