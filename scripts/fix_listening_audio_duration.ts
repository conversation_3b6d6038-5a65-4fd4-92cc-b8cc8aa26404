import { Client } from 'pg';

async function fixListeningAudioDuration() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgres://postgres@localhost:5432/eps_simulator',
  });
  
  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database. Starting fix...');

    // Check if the column exists
    const { rows: columns } = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'exams' AND column_name = 'listeningAudioDuration';
    `);

    if (columns.length === 0) {
      console.log('listeningAudioDuration column does not exist. Adding it...');
      await client.query(`
        ALTER TABLE exams 
        ADD COLUMN "listeningAudioDuration" INTEGER;
      `);
      console.log('Added listeningAudioDuration column to exams table');
    } else {
      console.log('listeningAudioDuration column already exists');
    }

    // Update existing exams to set listeningAudioDuration equal to listening_time
    console.log('Updating exams with null listeningAudioDuration...');
    const { rowCount } = await client.query(`
      UPDATE exams 
      SET "listeningAudioDuration" = listening_time 
      WHERE "listeningAudioDuration" IS NULL;
    `);
    console.log(`Updated ${rowCount} exams with default listeningAudioDuration values`);

    // Print all exams for debugging
    const { rows: exams } = await client.query(`
      SELECT id, reading_time, listening_time, "listeningAudioDuration" 
      FROM exams;
    `);
    console.log('Current exams in database:');
    exams.forEach(exam => {
      console.log(`Exam ${exam.id}: reading_time=${exam.reading_time}, listening_time=${exam.listening_time}, listeningAudioDuration=${exam.listeningAudioDuration}`);
    });

    console.log('Fix completed successfully.');
  } catch (error) {
    console.error('Error during fix:', error);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the fix
fixListeningAudioDuration().catch(error => {
  console.error('Fix failed:', error);
  process.exit(1);
});
