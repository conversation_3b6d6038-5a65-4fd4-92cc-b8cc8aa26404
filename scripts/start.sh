#!/bin/bash

# Set environment variables
export DATABASE_URL="postgres://postgres:postgres@localhost:5432/eps_simulator"
export PORT=3000
export NODE_ENV=development
export CORS_ORIGIN="http://localhost:5173"
export VITE_API_URL="http://localhost:3000/api"

# Kill any existing process on the port
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# Start the application
nohup npm run dev > app.log 2>&1 &

# Get the process ID
PID=$!

# Save the PID to a file
echo $PID > app.pid

echo "Application started with PID: $PID"
echo "Logs are being written to app.log"
echo "Application will be available at http://localhost:3000" 