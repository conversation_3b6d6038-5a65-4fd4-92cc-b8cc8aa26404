#!/bin/bash

# Kill any process running on port 3000
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

if [ -f app.pid ]; then
    PID=$(cat app.pid)
    if ps -p $PID > /dev/null; then
        echo "Stopping application with PID: $PID"
        kill $PID
        rm app.pid
        echo "Application stopped"
    else
        echo "No running application found"
        rm app.pid
    fi
else
    echo "No PID file found. Application might not be running."
fi 