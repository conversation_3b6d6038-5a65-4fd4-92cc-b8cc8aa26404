import { db } from '../server/db';
import { users } from '../shared/schema';
import bcrypt from 'bcryptjs';
import { eq } from 'drizzle-orm';

async function createAdmin() {
  const username = 'frankie';
  const password = '<PERSON><PERSON><PERSON>@1';
  const email = '<EMAIL>';

  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Check if user exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.username, username)
    });

    if (existingUser) {
      // Update existing user
      await db.update(users)
        .set({
          password: hashedPassword,
          role: 'admin',
          status: 'active',
          emailVerified: true
        })
        .where(eq(users.username, username));
      
      console.log('Admin user updated successfully');
    } else {
      // Create new admin user
      await db.insert(users).values({
        username,
        password: hashedPassword,
        email,
        role: 'admin',
        status: 'active',
        authProvider: 'local',
        emailVerified: true
      });
      
      console.log('Admin user created successfully');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }

  process.exit(0);
}

createAdmin(); 