import { db } from '../server/db';
import { exams } from '@shared/schema';
import { eq } from 'drizzle-orm';

async function insertMockExams() {
  try {
    // Mock Exam 1: Basic Safety Signs
    const mockExam1 = {
      title: "EPS-TOPIK 기출 모의고사 1회",
      description: "2023년 EPS-TOPIK 기출문제 기반 모의고사입니다. 읽기와 듣기 섹션을 포함합니다.",
      duration: 47,
      readingTime: 40,
      listeningTime: 7,
      questions: [
        {
          id: 1,
          text: "다음 중 '안전벨트'를 나타내는 표지는 무엇입니까?",
          type: "reading",
          options: [
            { id: 1, text: "안전조끼" },
            { id: 2, text: "안전모" },
            { id: 3, text: "안전벨트" },
            { id: 4, text: "소화기" }
          ],
          correctAnswer: 3
        },
        {
          id: 2,
          text: "다음 문장의 뜻으로 가장 알맞은 것을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "작업장에서는 안전모를 벗어도 됩니다." },
            { id: 2, text: "작업장에 들어가기 전에 안전모를 벗어야 합니다." },
            { id: 3, text: "작업장에서는 반드시 안전모를 써야 합니다." },
            { id: 4, text: "작업장에서는 안전모를 쓰지 않아도 됩니다." }
          ],
          correctAnswer: 3
        }
      ],
      isActive: true,
      createdBy: 1
    };

    // Mock Exam 2: Workplace Communication
    const mockExam2 = {
      title: "EPS-TOPIK 실전 모의고사 2회",
      description: "한국 산업 현장에서 자주 사용되는 용어와 상황 중심의 실전 모의고사입니다.",
      duration: 50,
      readingTime: 35,
      listeningTime: 15,
      questions: [
        {
          id: 1,
          text: "다음 대화를 읽고 여자가 남자에게 부탁한 것을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "내일 회의에 참석하기" },
            { id: 2, text: "오늘 서류 작성하기" },
            { id: 3, text: "서류 작성하고 제출하기" },
            { id: 4, text: "마지막 페이지 삭제하기" }
          ],
          correctAnswer: 3
        },
        {
          id: 2,
          text: "다음 안내문의 내용과 일치하는 것을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "내일 오후에 전기 점검이 있습니다." },
            { id: 2, text: "전기 점검 시간에 작업을 계속합니다." },
            { id: 3, text: "전기 점검은 2시간 동안 진행됩니다." },
            { id: 4, text: "안전 교육은 선택사항입니다." }
          ],
          correctAnswer: 3
        }
      ],
      isActive: true,
      createdBy: 1
    };

    // Mock Exam 3: Safety Procedures
    const mockExam3 = {
      title: "EPS-TOPIK 실전 모의고사 3회",
      description: "산업 현장 안전 절차와 관련된 용어와 상황을 중심으로 구성된 모의고사입니다.",
      duration: 45,
      readingTime: 30,
      listeningTime: 15,
      questions: [
        {
          id: 1,
          text: "다음 중 비상 상황에서 해야 할 일을 순서대로 나열한 것을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "1. 비상벨 누르기 2. 대피하기 3. 담당자에게 보고하기" },
            { id: 2, text: "1. 대피하기 2. 비상벨 누르기 3. 담당자에게 보고하기" },
            { id: 3, text: "1. 담당자에게 보고하기 2. 비상벨 누르기 3. 대피하기" },
            { id: 4, text: "1. 대피하기 2. 담당자에게 보고하기 3. 비상벨 누르기" }
          ],
          correctAnswer: 1
        },
        {
          id: 2,
          text: "다음 중 화재 발생 시 해야 할 일을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "소화기로 불을 끄고 대피하기" },
            { id: 2, text: "바로 대피하기" },
            { id: 3, text: "담당자에게 보고하고 대피하기" },
            { id: 4, text: "불이 작으면 계속 작업하기" }
          ],
          correctAnswer: 2
        }
      ],
      isActive: true,
      createdBy: 1
    };

    // Mock Exam 4: Equipment Operation
    const mockExam4 = {
      title: "EPS-TOPIK 실전 모의고사 4회",
      description: "기계 장비 조작과 관련된 용어와 상황을 중심으로 구성된 모의고사입니다.",
      duration: 55,
      readingTime: 40,
      listeningTime: 15,
      questions: [
        {
          id: 1,
          text: "다음 중 기계를 작동하기 전에 확인해야 할 사항을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "안전장비 착용 여부" },
            { id: 2, text: "기계 상태 점검" },
            { id: 3, text: "작업 환경 확인" },
            { id: 4, text: "모든 위 사항" }
          ],
          correctAnswer: 4
        },
        {
          id: 2,
          text: "다음 중 기계 작동 중 주의해야 할 사항을 고르십시오.",
          type: "reading",
          options: [
            { id: 1, text: "기계 소음에 주의하기" },
            { id: 2, text: "기계 상태 모니터링하기" },
            { id: 3, text: "갑작스러운 정지에 대비하기" },
            { id: 4, text: "모든 위 사항" }
          ],
          correctAnswer: 4
        }
      ],
      isActive: true,
      createdBy: 1
    };

    // Insert all mock exams
    const [exam1] = await db.insert(exams).values(mockExam1).returning();
    const [exam2] = await db.insert(exams).values(mockExam2).returning();
    const [exam3] = await db.insert(exams).values(mockExam3).returning();
    const [exam4] = await db.insert(exams).values(mockExam4).returning();

    console.log('Mock exams inserted successfully:');
    console.log('Exam 1:', exam1.id);
    console.log('Exam 2:', exam2.id);
    console.log('Exam 3:', exam3.id);
    console.log('Exam 4:', exam4.id);

  } catch (error) {
    console.error('Error inserting mock exams:', error);
  } finally {
    process.exit(0);
  }
}

insertMockExams(); 