// Global type declarations
interface Window {
  preloadedMainAudioUrl?: string;
  preloadedMainAudioElement?: HTMLAudioElement;
  currentPlayingAudio?: HTMLAudioElement;
  downloadedMainAudio?: {
    audioBuffer: ArrayBuffer;
    audioBlob: Blob;
    audioUrl: string;
    originalUrl: string;
    fileName: string;
    fileSize: number;
  };
  exactListeningDuration?: number;
  exactListeningTimeMMSS?: string;
  currentListeningTimeMMSS?: string;
}
