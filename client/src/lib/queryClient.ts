import { QueryClient } from "@tanstack/react-query";

type GetQueryFnOptions = {
  on401?: "throw" | "returnNull";
};

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 0,
    },
  },
});

// Helper function to handle HTTP errors
async function handleHttpErrorResponse(res: Response) {
  if (!res.ok) {
    let errorText = "";
    try {
      const errorData = await res.json();
      errorText = errorData.message || "Request failed";
    } catch {
      errorText = await res.text() || "Request failed";
    }
    throw new Error(errorText);
  }
  return res;
}

// Helper function for API requests
export async function apiRequest(
  method: string,
  path: string,
  data?: any
): Promise<Response> {
  const options: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include", // Include cookies for cross-origin requests
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  return fetch(path, options).then(handleHttpErrorResponse);
}

// Query function for React Query
export function getQueryFn(
  options: GetQueryFnOptions = { on401: "throw" }
): (params: { queryKey: string[] }) => Promise<any> {
  return async ({ queryKey }) => {
    const path = queryKey[0];
    try {
      const res = await fetch(path, {
        credentials: "include", // Include cookies for cross-origin requests
      });

      if (res.status === 401 && options.on401 === "returnNull") {
        return null;
      }

      await handleHttpErrorResponse(res);
      return res.json();
    } catch (err) {
      if ((err as any)?.status === 401 && options.on401 === "returnNull") {
        return null;
      }
      throw err;
    }
  };
}