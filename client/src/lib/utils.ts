import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { apiRequest } from "./queryClient"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Trích xuất ID file từ URL Google Drive
 * @param url URL của file từ Google Drive
 * @returns ID của file hoặc null nếu không tìm thấy
 */
export const extractGoogleDriveFileId = (url: string): string | null => {
  if (!url) return null;

  try {
    // Format: https://drive.google.com/file/d/FILE_ID/view?usp=sharing
    if (url.includes('drive.google.com/file/d/')) {
      const regex = /\/d\/([^\/]+)/;
      const match = url.match(regex);
      return match && match[1] ? match[1] : null;
    }

    // Format: https://drive.google.com/open?id=FILE_ID
    if (url.includes('drive.google.com/open?id=')) {
      const regex = /id=([^&]+)/;
      const match = url.match(regex);
      return match && match[1] ? match[1] : null;
    }

    return null;
  } catch (error) {
    console.error('Error extracting Google Drive file ID:', error);
    return null;
  }
}

// Cache cho URL đã lấy để tránh phải gọi API nhiều lần
const audioUrlCache = new Map<string, string>();

/**
 * Lấy URL trực tiếp từ Google Drive mà không cần qua server
 * @param fileId ID của file Google Drive
 * @returns URL trực tiếp
 */
export const getDirectGoogleDriveUrl = (fileId: string): string => {
  return `https://docs.google.com/uc?export=download&id=${fileId}`;
};

/**
 * Chuyển đổi URL Google Drive thành URL có thể phát trực tiếp
 * @param url URL của file từ Google Drive
 * @param useProxy Có sử dụng proxy qua backend không (mặc định: false)
 * @param bypassCache Bỏ qua cache và lấy URL mới (mặc định: false)
 * @returns URL có thể phát trực tiếp
 */
export const getPlayableAudioUrl = async (url: string, useProxy: boolean = false, bypassCache: boolean = false): Promise<string> => {
  if (!url) {
    console.error('URL is empty');
    throw new Error('URL không được để trống');
  }

  // Kiểm tra cache trước khi xử lý
  if (!bypassCache && audioUrlCache.has(url)) {
    console.log('Using cached URL for:', url);
    return audioUrlCache.get(url)!;
  }

  console.log('Processing URL:', url);

  // Nếu không phải URL Google Drive, trả về nguyên bản
  if (!url.includes('drive.google.com')) {
    console.log('Not a Google Drive URL, returning as is');
    return url;
  }

  const fileId = extractGoogleDriveFileId(url);
  if (!fileId) {
    console.error('Không thể trích xuất ID file từ URL:', url);
    throw new Error('Không thể trích xuất ID file từ URL. Vui lòng kiểm tra lại định dạng URL Google Drive.');
  }

  console.log('Extracted file ID:', fileId);

  // Sử dụng proxy qua backend (cách cũ)
  if (useProxy) {
    const proxyUrl = `/api/proxy/gdrive?id=${fileId}`;
    console.log('Using proxy URL:', proxyUrl);
    return proxyUrl;
  }

  try {
    console.log('Fetching direct URL from API...');
    // Sử dụng API mới để lấy URL trực tiếp
    const response = await fetch(`/api/direct-url/gdrive?id=${fileId}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error response from direct-url API: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`Lỗi khi lấy URL trực tiếp: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Received direct URL:', data.url);

    // Kiểm tra nếu có alternateUrl trong response
    if (data.alternateUrl) {
      console.log('Alternate URL available:', data.alternateUrl);
    }

    // Kiểm tra URL có thể truy cập được không
    try {
      const testResponse = await fetch(data.url, { method: 'HEAD' });
      if (!testResponse.ok) {
        console.warn(`Direct URL not accessible: ${testResponse.status} ${testResponse.statusText}`);
        // Nếu có alternateUrl, sử dụng nó
        if (data.alternateUrl) {
          console.log('Using alternate URL instead:', data.alternateUrl);
          return data.alternateUrl;
        }
        // Nếu không có alternateUrl, vẫn sử dụng URL trực tiếp (có thể sẽ gặp lỗi)
      }
      console.log('Direct URL is accessible');
      // Lưu URL vào cache trước khi trả về
      audioUrlCache.set(url, data.url);
      return data.url;
    } catch (testError) {
      console.warn('Error testing direct URL accessibility:', testError);
      // Nếu có lỗi khi kiểm tra URL và có alternateUrl, sử dụng alternateUrl
      if (data.alternateUrl) {
        console.log('Using alternate URL due to test error:', data.alternateUrl);
        // Lưu URL thay thế vào cache
        audioUrlCache.set(url, data.alternateUrl);
        return data.alternateUrl;
      }
      // Nếu không có alternateUrl, vẫn sử dụng URL trực tiếp
      audioUrlCache.set(url, data.url);
      return data.url;
    }
  } catch (error) {
    console.error('Error getting direct URL:', error);
    // Fallback to proxy if direct URL fails
    const fallbackUrl = `/api/proxy/gdrive?id=${fileId}`;
    console.log('Falling back to proxy URL:', fallbackUrl);
    // Lưu URL proxy vào cache
    audioUrlCache.set(url, fallbackUrl);
    return fallbackUrl;
  }
}

/**
 * Lấy thời lượng của file âm thanh từ URL Google Drive
 * @param audioUrl URL của file âm thanh từ Google Drive
 * @returns Thời lượng của file âm thanh tính bằng giây
 */
export const getAudioDuration = async (audioUrl: string): Promise<number> => {
  if (!audioUrl) {
    console.error("URL audio không hợp lệ");
    throw new Error("URL audio không được để trống");
  }

  console.log('Getting audio duration for URL:', audioUrl);

  try {
    // Lấy URL có thể phát trực tiếp (sử dụng phương thức mới)
    console.log('Getting playable URL...');
    const playableUrl = await getPlayableAudioUrl(audioUrl);
    console.log('Got playable URL:', playableUrl);

    // Tạo một audio element tạm thời
    const audio = new Audio();

    // Trả về Promise để xử lý bất đồng bộ
    return new Promise((resolve, reject) => {
      let timeoutId: NodeJS.Timeout;

      // Xử lý khi metadata được load
      audio.onloadedmetadata = () => {
        clearTimeout(timeoutId);
        if (audio.duration && !isNaN(audio.duration) && audio.duration !== Infinity) {
          const duration = Math.ceil(audio.duration);
          console.log(`Audio duration loaded successfully: ${duration} seconds`);
          resolve(duration); // Làm tròn lên số nguyên giây
        } else {
          console.error('Invalid audio duration:', audio.duration);
          reject(new Error("Không thể lấy thời lượng audio"));
        }
      };

      // Xử lý khi có lỗi
      audio.onerror = (event) => {
        clearTimeout(timeoutId);
        const error = audio.error;
        let errorMessage = "Không thể load audio";

        if (error) {
          console.error("Lỗi khi load audio:", error.code, error.message);
          switch (error.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorMessage = "Quá trình tải file bị hủy.";
              break;
            case MediaError.MEDIA_ERR_NETWORK:
              errorMessage = "Lỗi kết nối mạng khi tải file.";
              break;
            case MediaError.MEDIA_ERR_DECODE:
              errorMessage = "Không thể giải mã file âm thanh.";
              break;
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMessage = "File âm thanh không được hỗ trợ hoặc không thể truy cập.";
              break;
          }
        }

        reject(new Error(errorMessage));
      };

      // Xử lý khi audio được load
      audio.onloadeddata = () => {
        console.log('Audio data loaded successfully');
      };

      // Xử lý khi audio sẵn sàng phát
      audio.oncanplay = () => {
        console.log('Audio can play now');
      };

      // Set source và load audio
      console.log('Setting audio source and loading:', playableUrl);
      audio.crossOrigin = "anonymous"; // Thêm CORS support
      audio.src = playableUrl;
      audio.load();

      // Timeout sau 15 giây nếu không load được
      timeoutId = setTimeout(() => {
        console.error('Timeout when loading audio metadata');
        audio.onloadedmetadata = null;
        audio.onerror = null;
        audio.onloadeddata = null;
        audio.oncanplay = null;
        reject(new Error("Timeout khi lấy thời lượng audio. Vui lòng kiểm tra lại URL hoặc quyền truy cập file."));
      }, 15000); // Tăng timeout lên 15 giây
    });
  } catch (error) {
    console.error("Lỗi khi lấy thời lượng audio:", error);
    throw error; // Throw lỗi để component xử lý
  }
};

/**
 * Tính thời lượng của câu hỏi từ thời điểm bắt đầu
 * @param questions Danh sách câu hỏi đã sắp xếp theo thứ tự
 * @param index Vị trí của câu hỏi cần tính thời lượng
 * @param totalDuration Tổng thời lượng của file âm thanh
 * @returns Thời lượng của câu hỏi (giây)
 */
export const calculateQuestionDuration = (questions: any[], index: number, totalDuration: number): number => {
  if (!questions || !Array.isArray(questions) || index < 0 || index >= questions.length) {
    console.warn(`Invalid parameters for calculateQuestionDuration: questions=${questions?.length}, index=${index}, totalDuration=${totalDuration}`);
    return 0;
  }

  // Lấy thời điểm bắt đầu của câu hỏi hiện tại
  const currentStartTime = questions[index].start_at || 0;
  console.log(`Calculating duration for question at index ${index}: start_at=${currentStartTime}s, totalDuration=${totalDuration}s`);

  // Nếu là câu hỏi cuối cùng, thời lượng = tổng thời lượng - thời điểm bắt đầu
  if (index === questions.length - 1) {
    // Kiểm tra nếu totalDuration không tồn tại hoặc không hợp lệ
    if (!totalDuration || isNaN(totalDuration) || totalDuration <= 0) {
      // Thử tìm thời gian phần Listening trong các thuộc tính của đề thi
      const exam = questions[0]?.exam;
      if (exam && exam.listeningTime && exam.listeningTime > 0) {
        const listeningTimeSeconds = exam.listeningTime * 60;
        const duration = Math.max(listeningTimeSeconds - currentStartTime, 30);
        console.log(`Last question duration from exam.listeningTime: ${duration}s (listeningTime=${exam.listeningTime}m = ${listeningTimeSeconds}s - currentStartTime=${currentStartTime}s)`);
        return duration;
      }

      console.warn('totalDuration is invalid and no listeningTime found, using default duration of 60s for last question');
      // Sử dụng giá trị mặc định 60 giây cho câu cuối cùng
      return 60;
    }

    // Đảm bảo thời lượng ít nhất là 30 giây cho câu cuối cùng
    const duration = Math.max(totalDuration - currentStartTime, 30);
    console.log(`Last question duration: ${duration}s (totalDuration=${totalDuration}s - currentStartTime=${currentStartTime}s)`);
    return duration;
  }

  // Nếu không phải câu hỏi cuối cùng, thời lượng = thời điểm bắt đầu câu tiếp theo - thời điểm bắt đầu hiện tại
  const nextStartTime = questions[index + 1].start_at || 0;
  const duration = Math.max(nextStartTime - currentStartTime, 1); // Đảm bảo ít nhất 1 giây
  console.log(`Question ${index+1} duration: ${duration}s (nextStartTime=${nextStartTime}s - currentStartTime=${currentStartTime}s)`);
  return duration;
};

/**
 * Tính tổng thời lượng của tất cả các file âm thanh trong đề thi
 * @param questions Danh sách câu hỏi trong đề thi
 * @param totalDuration Tổng thời lượng của file âm thanh
 * @returns Tổng thời lượng (giây)
 */
/**
 * Chuyển đổi số giây thành định dạng mm:ss
 * @param seconds Số giây cần chuyển đổi
 * @returns Chuỗi định dạng mm:ss
 */
export const formatTimeMMSS = (seconds: number): string => {
  if (!seconds || isNaN(seconds) || seconds < 0) return '00:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Chuyển đổi chuỗi định dạng mm:ss thành số giây
 * @param timeString Chuỗi định dạng mm:ss
 * @returns Số giây
 */
export const parseTimeMMSS = (timeString: string): number => {
  if (!timeString) return 0;

  const parts = timeString.split(':');
  if (parts.length !== 2) return 0;

  const minutes = parseInt(parts[0], 10);
  const seconds = parseInt(parts[1], 10);

  if (isNaN(minutes) || isNaN(seconds)) return 0;

  return minutes * 60 + seconds;
};

export const calculateTotalListeningDuration = (questions: any[], totalDuration: number = 0): number => {
  if (!questions || !Array.isArray(questions)) {
    console.warn('Invalid questions array for calculateTotalListeningDuration');
    return 0;
  }

  // Lọc ra các câu hỏi listening và sắp xếp theo thời điểm bắt đầu
  const listeningQuestions = questions
    .filter(q => q.type === "listening")
    .sort((a, b) => (a.start_at || 0) - (b.start_at || 0));

  console.log(`Found ${listeningQuestions.length} listening questions, totalDuration=${totalDuration}s`);

  if (listeningQuestions.length === 0) return 0;

  // Nếu có totalDuration, sử dụng nó
  if (totalDuration > 0) {
    // Sử dụng trực tiếp totalDuration
    console.log(`Using provided totalDuration: ${totalDuration}s`);
    return totalDuration;
  } else {
    // Nếu không có totalDuration, kiểm tra xem có câu hỏi cuối cùng có audioDuration không
    const lastQuestion = listeningQuestions[listeningQuestions.length - 1];
    if (lastQuestion && lastQuestion.audioDuration > 0) {
      // Tính tổng thời lượng = thời điểm bắt đầu của câu cuối cùng + thời lượng của câu cuối cùng
      const calculatedTotal = (lastQuestion.start_at || 0) + lastQuestion.audioDuration;
      console.log(`Calculated totalDuration from last question: ${calculatedTotal}s (start_at=${lastQuestion.start_at || 0}s + audioDuration=${lastQuestion.audioDuration}s)`);
      return calculatedTotal;
    }

    // Thử tính tổng thời lượng từ audioDuration của tất cả các câu hỏi (nếu có)
    const calculatedDuration = listeningQuestions.reduce((total, question) => {
      // Nếu có audioDuration, sử dụng nó
      if (typeof question.audioDuration === "number" && question.audioDuration > 0) {
        console.log(`Adding question duration: ${question.audioDuration}s`);
        return total + question.audioDuration;
      }
      return total;
    }, 0);

    if (calculatedDuration > 0) {
      console.log(`Calculated total listening duration from all questions: ${calculatedDuration}s`);
      return calculatedDuration;
    }

    // Nếu không có thông tin nào, sử dụng giá trị mặc định 300 giây (5 phút)
    console.warn('No duration information available, using default of 300s (5 minutes)');
    return 300;
  }
};
