/* Import Google Fonts - Noto Sans KR for Korean text */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@300;400;500;700&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --eps-gray: #d3d3d3;
  --eps-light-blue: #d8e4f0;
  --eps-blue: #a0c4e0;
  --eps-dark-blue: #7891b3;
  --eps-orange-gold: #f8b93b;
}

body {
  font-family: 'Noto Sans KR', sans-serif;
  background-color: var(--eps-gray);
  color: #333;
  line-height: 1.6;
}

/* Styling for empty cells in question grid */
.empty-cell {
  background-color: #e8e8e8 !important;
  border: 1px dashed #ccc !important;
  cursor: default !important;
  opacity: 0.5;
}

/* <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> chuy<PERSON> tiếp từ Reading sang Listening */
.listening-transition {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.listening-transition-content {
  width: 100%;
  max-width: 800px;
  text-align: center;
}

.listening-transition h1 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: normal;
}

.divider-line {
  height: 1px;
  background-color: #000;
  width: 100%;
  margin: 1rem 0;
}

.listening-note {
  font-size: 0.9rem;
  margin-top: 1.5rem;
  text-align: right;
  color: #333;
}

/* Transition screen styles - hiển thị trong QuestionArea */
.transition-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 100%;
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.transition-screen h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: bold;
  color: #333;
}

.transition-screen .divider-line {
  height: 2px;
  background-color: #ddd;
  width: 60%;
  margin: 1.5rem 0;
}

.transition-screen .transition-note {
  font-size: 1rem;
  margin-top: 2rem;
  text-align: right;
  color: #666;
  align-self: flex-end;
  padding-right: 20%;
  font-style: italic;
}

.transition-screen p {
  font-size: 0.9rem;
  margin-top: 1.5rem;
  text-align: center;
  color: #666;
}

/* Auth Pages Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 2rem;
}

.auth-container {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 2rem;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.auth-header p {
  color: #6c757d;
  font-size: 1rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.error-message {
  background-color: #fee2e2;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  text-align: center;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: #2c3e50;
  font-size: 0.875rem;
  font-weight: 500;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
}

.form-group input::placeholder {
  color: #a0aec0;
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-button:hover {
  background-color: #2980b9;
}

.submit-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.auth-footer {
  margin-top: 2rem;
  text-align: center;
}

.auth-footer p {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0.5rem 0;
}

.auth-footer .link {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
}

.auth-footer .link:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .auth-container {
    padding: 1.5rem;
  }

  .auth-header h1 {
    font-size: 1.5rem;
  }

  .form-group input {
    padding: 0.625rem;
  }

  .submit-button {
    padding: 0.625rem;
  }
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Exam page styles */
.exam-container {
  min-height: 100vh;
  background-color: #f0f4f8;
  display: flex;
  flex-direction: column;
}

.exam-header {
  background: linear-gradient(to bottom, #c4d3e6, #a0c4e0);
  color: #333;
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #7891b3;
  border-top: 1px solid #fff;
}

.header-left {
  flex: 1;
}

.header-title {
  font-size: 1.2rem;
  font-weight: bold;
}

.header-center {
  flex: 2;
  text-align: center;
}

.header-subtitle {
  font-size: 1rem;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.application-box {
  background-color: #f5f5f5;
  border: 1px solid #999;
  border-radius: 2px;
  padding: 0.15rem 0.5rem;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
}

.application-label {
  font-weight: bold;
  margin-right: 0.25rem;
  color: #444;
}

.exam-version {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.2rem 1rem;
  background-color: var(--eps-gray);
  color: #555;
  font-size: 0.75rem;
  border-bottom: 1px solid #ccc;
}

.version-text {
  font-family: 'Consolas', monospace;
  font-weight: normal;
}

.font-size-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.font-button {
  background: linear-gradient(to bottom, #7cb5ec, #4a90e2);
  color: white;
  border: 1px solid #4a8ad7;
  border-radius: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.75rem;
}

.font-button:hover {
  background: #ff7f27; /* Màu cam khi hover */
}

.font-size-slider {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
}

.slider-line {
  width: 100px;
  height: 2px;
  background-color: #ccc;
  position: relative;
}

.slider-handle {
  width: 10px;
  height: 10px;
  background-color: #fff;
  border: 1px solid #999;
  border-radius: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel {
  flex: 7;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #ccc;
}

.right-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  background-color: var(--eps-light-blue);
  padding: 0.5rem;
}

/* Audio player styles */
.audio-player {
  width: 100%;
  margin-bottom: 15px;
  border-radius: 4px;
  background-color: #e8f4ff;
  padding: 8px;
  border: 1px solid #a0c4e0;
}

.hidden-audio {
  display: none;
}

/* Question area styles */
.question-area {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0.75rem;
}

.question-content {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid #eee;
}

.question-main-text {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.4;
  font-size: 0.95rem;
}

.question-subtext {
  color: #555;
  margin-top: 0.5rem;
  font-style: italic;
  font-size: 0.9rem;
}

/* Styling cho màn hình transition giữa Reading và Listening */
.transition-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.transition-screen h1 {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 1.5rem;
}

.transition-screen .divider-line {
  width: 200px;
  height: 2px;
  background-color: #ddd;
  margin: 1rem 0 2rem;
}

.transition-screen .transition-note {
  font-size: 18px;
  color: #666;
  font-style: italic;
  margin-bottom: 2rem;
}

.next-listening-button {
  background: linear-gradient(to bottom, #4a90e2, #3679c6);
  color: white;
  font-weight: bold;
  padding: 12px 24px;
  border-radius: 30px;
  border: none;
  font-size: 16px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.2s ease;
}

.next-listening-button:hover {
  background: #ff7f27; /* Màu cam khi hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.options-container {
  padding: 0.5rem 0;
}

.options-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.option-item {
  margin-bottom: 0.5rem;
  padding: 0.4rem 0.5rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.option-item:hover {
  background-color: #f8f9fa;
  border-color: #bbb;
}

.option-item.selected {
  background-color: #e8f4fd;
  border-color: #7cb5ec;
}

.option-circle {
  width: 20px;
  height: 20px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.option-number {
  font-size: 0.8rem;
  font-weight: 500;
  color: #444;
  z-index: 1;
}

.option-check {
  position: absolute;
  top: 35%;
  left: 65%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  color: #41a658;
  font-weight: bold;
  z-index: 2;
}

.option-item.selected .option-circle {
  background-color: #f1f9f1;
  border-color: #41a658;
}

.option-item.selected .option-number {
  color: #888;
  opacity: 0.4;
}

/* Image Options Styling */
.image-options-container {
  flex-grow: 1;
  width: 100%;
  padding: 15px;
}

.image-options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 5px; /* Reduced to 1/4 of original spacing */
  padding: 5px;
  justify-items: center;
}

.image-option {
  position: relative;
  border: 2px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-option:hover {
  border-color: #aaa;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.image-option.selected {
  border-color: #3498db;
  box-shadow: 0 3px 8px rgba(52, 152, 219, 0.3);
}

.option-image {
  width: 100%;
  height: auto;
  display: block;
}

.image-option-circle {
  position: absolute;
  bottom: 10px;
  left: 10px;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

/* Đặt số vào giữa chính xác */
.option-number {
  position: relative;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-option-circle.selected {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

/* Timer section styles */
.timer-container {
  background-color: var(--eps-light-blue);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1rem;
  border-radius: 6px;
  border: 1px solid #a0c5e5;
}

.timer-section-header {
  display: flex;
}

.timer-tab {
  flex: 1;
  text-align: center;
  padding: 0.35rem 0;
  font-size: 0.75rem;
  font-weight: bold;
}

.reading-tab {
  background-color: var(--eps-dark-blue);
  color: white;
}

.listening-tab {
  background-color: var(--eps-blue);
  color: #333;
}

.timer-displays {
  display: flex;
}

.timer-display {
  flex: 1;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid #ccc;
  position: relative;
}

.timer-label {
  font-size: 0.65rem;
  color: #555;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
}

.timer-value {
  font-family: 'Consolas', monospace;
  font-size: 1.15rem;
  font-weight: bold;
  color: #333;
}

.reading-timer .timer-value {
  color: #2c4b8c;
}

/* Kiểu mới cho questions table */
.questions-table {
  border: 1px solid #a0c5e5;
  background-color: white;
  border-radius: 6px;
  overflow: hidden;
  font-size: 0.7rem;
  max-width: 100%;
}

.questions-header {
  display: flex;
  border-bottom: 1px solid #ccc;
}

.reading-column-header, .listening-column-header {
  flex: 1;
  text-align: center;
  padding: 0.2rem 0;
  font-size: 0.65rem;
  font-weight: bold;
  color: #333;
  background-color: #c3d8ed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.reading-column-header {
  border-right: 1px solid #a0c5e5;
}

.questions-time-displays {
  display: flex;
  background-color: #f5f8fc;
  border-bottom: 1px solid #a0c5e5;
}

.time-display {
  flex: 1;
  display: flex;
  padding: 0.3rem;
  gap: 0.3rem;
  align-items: center;
  justify-content: center;
}

.time-display:first-child {
  border-right: 1px solid #a0c5e5;
}

.time-display-label {
  background-color: #555;
  color: white;
  padding: 0.15rem 0.3rem;
  font-size: 0.65rem;
  font-weight: bold;
  border-radius: 3px;
}

.time-display-value {
  font-family: 'Consolas', monospace;
  font-size: 0.75rem;
  font-weight: bold;
  color: #333;
}

.questions-grid {
  display: flex;
  background-color: white;
}

.questions-column {
  flex: 1;
  border-right: 1px solid #a0c5e5;
}

.questions-column:last-child {
  border-right: none;
}

.question-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0.1rem 0;
  font-size: 0.6rem;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #ddd;
  height: 12px;
}

.question-cell:hover {
  background-color: #f0f7fd;
}

.question-cell.current {
  background-color: #e8f5fe;
  font-weight: bold;
  border-color: #4a90e2;
}

.listening-inactive {
  color: #aaa;
  cursor: not-allowed;
  background-color: #f8f8f8;
  border-color: #eee;
}

/* Style khi không thể click trong phần listening */
.question-cell.navigation-blocked {
  cursor: not-allowed !important;
  opacity: 0.7;
  background-color: #f5f5f5;
  pointer-events: none;
}

.question-cell.navigation-blocked:hover {
  background-color: #f5f5f5;
  transform: none;
}

.listening-active {
  cursor: pointer;
}





/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}

.toast {
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease;
}

.toast:hover {
  transform: translateX(-5px);
}

.toast-default {
  background: white;
  border: 1px solid #e2e8f0;
  color: #1a202c;
}

.toast-destructive {
  background: #fed7d7;
  border: 1px solid #fc8181;
  color: #742a2a;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.toast-description {
  font-size: 13px;
  opacity: 0.8;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.answer-dot {
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background-color: #4fc3f7;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0.4rem;
  gap: 0.5rem;
}

.prev-button, .next-button {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.prev-button:hover, .next-button:hover {
  background-color: #ff7f27; /* Màu cam khi hover */
}

.prev-button:disabled, .next-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.nav-button {
  flex: 1;
  padding: 0.3rem;
  border-radius: 15px;
  border: none;
  background: linear-gradient(to bottom, #62a0e1, #3d88d5);
  color: white;
  font-size: 0.65rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.nav-button:hover:not(:disabled) {
  background: #ff7f27; /* Màu cam khi hover */
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Question navigation styles */
.question-navigation {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  flex: 1;
}

.navigation-header {
  background-color: var(--eps-light-blue);
  border-bottom: 1px solid #ccc;
  padding: 0.4rem 0;
}

.navigation-title {
  font-weight: bold;
  font-size: 0.8rem;
  color: #333;
  text-align: center;
}

.two-column-grid {
  display: flex;
  padding: 0.5rem;
  gap: 0.5rem;
}

.question-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.35rem;
}

.question-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.3rem;
  border: 1px solid #ccc;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.15s;
  background-color: #f9f9f9;
}

.question-button:hover {
  background-color: #f0f0f0;
  border-color: #999;
}

.question-button.answered {
  background-color: #e1f5fe;
  border-color: #4fc3f7;
  color: #0288d1;
}

.question-button.current {
  border: 2px solid #f44336;
}

/* Navigation buttons are defined above */

/* Submit button styles */
.submit-button-container {
  margin-bottom: 0.3rem;
  position: relative;
}

.submit-button {
  background: linear-gradient(to bottom, #62a0e1, #3d88d5);
  color: white;
  border: 1px solid #3d7ec0;
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.7rem;
  font-weight: bold;
  text-align: center;
  transition: all 0.2s;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.submit-button.submit-active:hover {
  background: #ff7f27; /* Màu cam khi hover */
}

.submit-button.submit-inactive {
  background: linear-gradient(to bottom, #aac8e7, #8ab3db);
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-button:disabled {
  background: linear-gradient(to bottom, #aac8e7, #8ab3db);
  opacity: 0.7;
  cursor: not-allowed;
}

/* Confirmation dialog styles */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirmation-dialog {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  text-align: center;
}

/* Test Shutdown Dialog */
.test-shutdown-dialog {
  padding: 1.5rem;
  border-radius: 4px;
  width: 350px;
}

.test-shutdown-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
  gap: 10px;
}

/* Test End Screen */
.test-end-screen {
  background-color: white;
  border-radius: 0;
  width: 90%;
  max-width: 900px;
  height: 80vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.test-end-header {
  background-color: #5b92d2;
  color: white;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-end-title h1 {
  margin: 0;
  font-size: 1.2rem;
}

.test-end-title p {
  margin: 0;
  font-size: 0.9rem;
}

.test-end-info {
  display: flex;
  gap: 20px;
  font-size: 0.8rem;
}

.info-value {
  font-weight: bold;
  margin-left: 5px;
}

.test-end-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.thank-you-message {
  color: #5b92d2;
  margin-bottom: 20px;
}

.test-end-image {
  background-color: #f0f0f0;
  width: 250px;
  height: 150px;
  margin: 20px auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  position: relative;
}

.test-end-instructions {
  margin: 20px 0;
  text-align: left;
  align-self: flex-start;
  margin-left: 20%;
}

.test-end-instructions p {
  margin: 8px 0;
  color: #555;
}

.test-end-actions {
  margin-top: 20px;
}

.test-end-actions button {
  background-color: #5b92d2;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.confirmation-dialog h3 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.confirmation-dialog p {
  margin-bottom: 1.5rem;
  color: #555;
}

.confirmation-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.confirm-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirm-button:hover {
  background-color: #d32f2f;
}

.cancel-button {
  background-color: #e0e0e0;
  color: #333;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: #bdbdbd;
}

/* Score Screen styles */
.score-screen {
  min-height: 100vh;
  background-color: #f0f4f8;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.score-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
  text-align: center;
}

.score-container h2 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 2rem;
  font-size: 1.75rem;
}

.score-display {
  margin-bottom: 2rem;
}

.score-value {
  font-size: 4rem;
  font-weight: bold;
  color: #3498db;
  line-height: 1;
}

.score-label {
  color: #7f8c8d;
  margin-top: 0.5rem;
  font-size: 1.1rem;
}

.score-details {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #7f8c8d;
}

.detail-value {
  font-weight: bold;
  color: #2c3e50;
}

.return-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.return-button:hover {
  background-color: #2980b9;
}

/* Styling for not-finished dialog based on attached image */
.not-finished-dialog {
  text-align: center;
  border-radius: 10px;
  overflow: hidden;
  padding: 0;
}

.not-finished-dialog p {
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  margin: 0;
  font-size: 1.1rem;
}

.not-finished-buttons {
  text-align: right;
  padding: 0.75rem;
}

.done-button {
  color: #4285f4; /* Google blue color */
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 1rem;
}

.done-button:hover {
  background-color: rgba(66, 133, 244, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .right-panel {
    order: -1;
  }

  .exam-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .exam-info {
    width: 100%;
    justify-content: space-between;
  }

  .auth-content {
    flex-direction: column;
  }

  .auth-image {
    padding: 1.5rem;
  }

  .auth-container {
    padding: 1rem;
  }

  .auth-features {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .score-container {
    padding: 1.5rem;
  }
}

/* Admin Page Styles */
.admin-page {
  padding: 2rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.admin-header {
  margin-bottom: 2rem;
  text-align: center;
}

.admin-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.admin-header p {
  color: #6c757d;
}

.admin-tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 2rem;
}

.admin-tab {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: #6c757d;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.admin-tab:hover {
  color: #495057;
}

.admin-tab.active {
  color: #3498db;
  border-bottom-color: #3498db;
}

.admin-message {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
}

.admin-message.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.admin-message.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.admin-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 2rem;
}

/* Exam Creator Styles */
.admin-creator {
  max-width: 1000px;
  margin: 0 auto;
}

.admin-creator h2 {
  margin-bottom: 2rem;
  color: #2c3e50;
  text-align: center;
}

.creator-section {
  margin-bottom: 2.5rem;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  padding: 1.5rem;
  background-color: #fafafa;
}

.creator-section h3 {
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #eaeaea;
  color: #2c3e50;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-row .form-group {
  flex: 1;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin-right: 0.5rem;
}

.options-section {
  margin-top: 1.5rem;
}

.options-section h4 {
  margin-bottom: 1rem;
  color: #495057;
}

.option-item {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.option-header {
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-bottom: 1px solid #dee2e6;
}

.option-content {
  padding: 1rem;
}

.image-preview {
  margin-top: 0.75rem;
  border: 1px solid #dee2e6;
  padding: 0.5rem;
  border-radius: 4px;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 2px;
}

.button-group {
  text-align: center;
  margin-top: 1.5rem;
}

.add-question-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-question-button:hover {
  background-color: #218838;
}

.questions-list {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.question-list-item {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-list-item:last-child {
  border-bottom: none;
}

.question-info {
  flex: 1;
}

.question-type {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: bold;
  margin-right: 0.5rem;
}

.question-type.reading {
  background-color: #cce5ff;
  color: #004085;
}

.question-type.listening {
  background-color: #d4edda;
  color: #155724;
}



.question-id {
  color: #6c757d;
  font-size: 0.875rem;
}

.question-text {
  margin-top: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.question-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-button,
.delete-button,
.view-button {
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.view-button {
  background-color: #e9ecef;
  color: #495057;
}

.view-button:hover {
  background-color: #dee2e6;
}

.edit-button {
  background-color: #17a2b8;
  color: white;
}

.edit-button:hover {
  background-color: #138496;
}

.delete-button {
  background-color: #dc3545;
  color: white;
}

.delete-button:hover {
  background-color: #c82333;
}

.form-actions {
  text-align: center;
  margin-top: 2rem;
}

.save-exam-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-exam-button:hover {
  background-color: #0069d9;
}

.exams-list h2 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.exams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.exam-card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.exam-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.exam-header {
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-header h3 {
  margin: 0;
  color: #343a40;
  font-size: 1.1rem;
}

.exam-id {
  color: #6c757d;
  font-size: 0.875rem;
}

.exam-body {
  padding: 1rem;
}

.exam-body p {
  color: #6c757d;
  margin-bottom: 1rem;
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.exam-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px dashed #dee2e6;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #6c757d;
}

.stat-value {
  font-weight: 500;
  color: #495057;
}

.exam-actions {
  padding: 1rem;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  display: flex;
  gap: 0.5rem;
}

.exam-actions button {
  flex: 1;
}

/* Dashboard Styles */
.dashboard {
  padding: 2rem;
}

.dashboard-title {
  color: #2c3e50;
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1.8rem;
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 1rem;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-icon {
  background-color: #e3f2fd;
  color: #1976d2;
}

.active-user-icon {
  background-color: #e8f5e9;
  color: #388e3c;
}

.exam-icon {
  background-color: #fff8e1;
  color: #ffa000;
}

.attempt-icon {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #546e7a;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #37474f;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #78909c;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.chart-container h3 {
  color: #455a64;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  text-align: center;
}

.chart-wrapper {
  width: 100%;
}

.dashboard-tables {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table-container h3 {
  color: #455a64;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  text-align: center;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #546e7a;
}

.data-table tr:hover td {
  background-color: #f5f7fa;
}

.data-table .no-data {
  text-align: center;
  padding: 2rem;
  color: #9e9e9e;
  font-style: italic;
}

/* Exam Editor Styles */
.exam-editor-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.back-button {
  padding: 0.5rem 1rem;
  background-color: #f1f1f1;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  color: #555;
  transition: background-color 0.3s ease;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.exam-editor-header h2 {
  margin: 0;
  color: #2c3e50;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.dashboard-error button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.dashboard-error button:hover {
  background-color: #2980b9;
}

/* Admin Page Styles */
.admin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #2c3e50;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-header .logo {
  display: flex;
  flex-direction: column;
}

.admin-header .logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.admin-header .logo span {
  font-size: 0.8rem;
  opacity: 0.8;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-name {
  font-weight: 500;
}

.logout-button {
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.admin-content {
  display: flex;
  flex: 1;
}

.admin-sidebar {
  width: 240px;
  background-color: #34495e;
  color: white;
  padding: 1.5rem 0;
}

.admin-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-sidebar li {
  margin-bottom: 0.25rem;
}

.admin-sidebar li button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.admin-sidebar li button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.admin-sidebar li.active button {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 500;
  border-left: 3px solid #3498db;
}

.admin-sidebar .icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  width: 24px;
  display: inline-flex;
  justify-content: center;
}

.admin-panel {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.admin-panel-content h2 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

/* Responsive design for mobile */
/* Exam List Styles */
.exam-list {
  width: 100%;
}

.exam-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.exam-list-header h2 {
  margin: 0;
  color: #2c3e50;
}

.add-exam-button {
  padding: 0.5rem 1rem;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.add-exam-button:hover {
  background-color: #219955;
}

.exam-list-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-filter {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.exam-table-container {
  overflow-x: auto;
  margin-top: 1rem;
}

.exam-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.exam-table th,
.exam-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.exam-table th {
  background-color: #f5f7fa;
  color: #546e7a;
  font-weight: 600;
  white-space: nowrap;
}

.exam-table tr:hover {
  background-color: #f8f9fa;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #e8f5e9;
  color: #388e3c;
}

.status-badge.inactive {
  background-color: #ffebee;
  color: #d32f2f;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  white-space: nowrap;
}

.edit-button,
.take-exam-button {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.edit-button {
  background-color: #3498db;
  color: white;
}

.edit-button:hover {
  background-color: #2980b9;
}

.take-exam-button {
  background-color: #f39c12;
  color: white;
}

.take-exam-button:hover {
  background-color: #d35400;
}

.exam-list-loading,
.exam-list-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.no-exams {
  padding: 2rem;
  text-align: center;
  color: #95a5a6;
  font-style: italic;
}

@media (max-width: 768px) {
  .exam-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .exam-list-filters {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .admin-content {
    flex-direction: column;
  }

  .admin-sidebar {
    width: 100%;
    padding: 0.5rem 0;
  }

  .admin-sidebar ul {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
  }

  .admin-sidebar li {
    margin-bottom: 0;
    margin-right: 0.5rem;
  }

  .admin-sidebar li button {
    padding: 0.5rem 1rem;
  }

  .admin-panel {
    padding: 1rem;
  }
}

.exam-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-right: 24px;
}

.exam-table th.sortable:hover {
  background-color: #e9ecef;
}

.exam-table th.sortable svg {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.exam-table th.sortable:hover svg {
  color: #495057;
}

/* User Dashboard Styles */
.user-dashboard-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.user-dashboard-container .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #2c3e50;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-dashboard-container .logo {
  display: flex;
  flex-direction: column;
}

.user-dashboard-container .logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.user-dashboard-container .logo span {
  font-size: 0.8rem;
  opacity: 0.8;
}

.user-dashboard-container .user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-dashboard-container .user-name {
  font-weight: 500;
}

.user-dashboard-container .logout-button {
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.user-dashboard-container .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-dashboard-container .dashboard-content {
  display: flex;
  flex: 1;
}

.user-dashboard-container .sidebar {
  width: 240px;
  background-color: #34495e;
  color: white;
  padding: 1.5rem 0;
}

.user-dashboard-container .sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-dashboard-container .sidebar li {
  margin-bottom: 0.25rem;
}

.user-dashboard-container .sidebar li button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.user-dashboard-container .sidebar li button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.user-dashboard-container .sidebar li.active button {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 500;
  border-left: 3px solid #3498db;
}

.user-dashboard-container .sidebar .icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  width: 24px;
  display: inline-flex;
  justify-content: center;
}

.user-dashboard-container .content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.user-dashboard-container .overview-tab {
  max-width: 1200px;
  margin: 0 auto;
}

.user-dashboard-container .welcome-message {
  margin-bottom: 2rem;
}

.user-dashboard-container .welcome-message h2 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.user-dashboard-container .welcome-message p {
  color: #6c757d;
  font-size: 1.1rem;
}

.user-dashboard-container .dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.user-dashboard-container .stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.user-dashboard-container .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.user-dashboard-container .stat-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #546e7a;
}

.user-dashboard-container .stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #37474f;
}

.user-dashboard-container .recent-exams {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.user-dashboard-container .recent-exams h3 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.user-dashboard-container .exam-card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.user-dashboard-container .exam-card-header {
  background-color: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.user-dashboard-container .exam-card-header h3 {
  margin: 0;
  color: #343a40;
  font-size: 1.1rem;
}

.user-dashboard-container .exam-card-body {
  padding: 1rem;
}

.user-dashboard-container .attempt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
}

.user-dashboard-container .attempt-item:last-child {
  border-bottom: none;
}

.user-dashboard-container .attempt-info h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
}

.user-dashboard-container .attempt-info p {
  margin: 0.25rem 0 0 0;
  color: #6c757d;
  font-size: 0.875rem;
}

.user-dashboard-container .view-results-button {
  padding: 0.5rem 1rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.3s ease;
}

.user-dashboard-container .view-results-button:hover {
  background-color: #2980b9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-dashboard-container .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .user-dashboard-container .dashboard-content {
    flex-direction: column;
  }

  .user-dashboard-container .sidebar {
    width: 100%;
    padding: 0.5rem 0;
  }

  .user-dashboard-container .sidebar ul {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
  }

  .user-dashboard-container .sidebar li {
    margin-bottom: 0;
    margin-right: 0.5rem;
  }

  .user-dashboard-container .sidebar li button {
    padding: 0.5rem 1rem;
  }

  .user-dashboard-container .content-area {
    padding: 1rem;
  }
}

/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.landing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #2c3e50;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.landing-header .logo {
  display: flex;
  flex-direction: column;
}

.landing-header .logo h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.landing-header .logo span {
  font-size: 0.8rem;
  opacity: 0.8;
}

.landing-header .auth-buttons {
  display: flex;
  gap: 1rem;
}

.landing-header .login-button,
.landing-header .register-button,
.landing-header .dashboard-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.landing-header .login-button {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
}

.landing-header .login-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.landing-header .register-button,
.landing-header .dashboard-button {
  background-color: #3498db;
  color: white;
  border: none;
}

.landing-header .register-button:hover,
.landing-header .dashboard-button:hover {
  background-color: #2980b9;
}

.landing-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.slider-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(.77,0,.18,1);
  width: 100%;
  height: 100%;
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
  margin: 0 auto;
  padding: 2rem 1rem;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.slide-content h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.slide-content p {
  font-size: 1.1rem;
  color: #6c757d;
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  width: 100%;
  margin-top: 1rem;
}

@media (min-width: 600px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.feature-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-card .feature-icon {
  width: 48px;
  height: 48px;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: #1976d2;
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.feature-card p {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.5;
}

.slider-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 10;
}

.slider-button:hover {
  background: #e3f2fd;
}

.slider-button.prev {
  left: 1rem;
}

.slider-button.next {
  right: 1rem;
}

.slider-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #bdc3c7;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dot.active {
  background: #3498db;
}

@media (max-width: 600px) {
  .slide-content {
    max-width: 95vw;
    padding: 1rem 0.5rem;
  }
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  .slider-button {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 400px) {
  .slide-content {
    padding: 0.5rem 0.2rem;
  }
  .slider-button {
    width: 28px;
    height: 28px;
  }
}