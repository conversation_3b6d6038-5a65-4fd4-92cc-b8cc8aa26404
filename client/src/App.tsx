import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, useNavigate, useParams, useLocation } from 'react-router-dom';
import './index.css';
import './assets/quick-access.css';
import './assets/common-buttons.css';
import './assets/dialog-styles.css';
import './assets/test-end.css';
import './assets/test-result.css';
import './assets/start-screens.css';
import './assets/exam-layout.css';
import { FiEdit, FiHome, FiUser, FiBookOpen, FiPlay, FiSettings } from 'react-icons/fi';
import UserDashboard from './components/user/UserDashboard';

// EPS-TOPIK Components
import ExamHeader from './components/ExamHeader';
import QuestionArea, { Question } from './components/QuestionArea';
import TimerSection from './components/TimerSection';
import QuestionNavigation from './components/QuestionNavigation';
import SubmitButton from './components/SubmitButton';
import TestEndScreen from './components/TestEndScreen';
import StartScreens from './components/StartScreens';
import AdminPage from './components/admin/AdminPage';

// EPS-TOPIK Exam View component

// Login and Register component in separate file to avoid circular dependency
import LoginPage from './components/LoginPage';
import RegisterPage from './components/RegisterPage';
import { ProtectedRoute } from './components/ProtectedRoute';

// Exam View component
import { useAuth } from './hooks/use-auth';

// Exam Result Screen component (tách riêng)
export const ExamResultScreen = ({ exam, answers, score, onReturn }: any) => {
  // Tính số câu đúng và số câu sai
  const answeredCount = Object.keys(answers).length;
  const correctCount = Object.entries(answers).filter(([questionId, answerId]) => {
    const question = exam.questions.find((q: any) => q.id === parseInt(questionId));
    if (question && 'correctAnswer' in question) {
      return answerId === (question).correctAnswer;
    }
    return false;
  }).length;
  const wrongCount = answeredCount - correctCount;
  const notAnsweredCount = exam.questions.length - answeredCount;
  const readingQuestions = exam.questions.filter((q: any) => q.type === 'reading');
  const listeningQuestions = exam.questions.filter((q: any) => q.type === 'listening');
  const totalScore = 200;
  const calculatedScore = correctCount * 5;

  return (
    <div className="score-screen">
      <div className="score-container">
        <div className="score-display">
          <div className="score-value">{score} / 200</div>
          <div className="score-label">Score</div>
        </div>
        <div className="two-grid-layout">
          <div className="result-column">
            <div className="column-header">Summary</div>
            <div className="score-details">
              <div className="detail-item"><span className="detail-label">Total Questions:</span><span className="detail-value">{exam.questions.length}</span></div>
              <div className="detail-item"><span className="detail-label">Answered:</span><span className="detail-value">{answeredCount}</span></div>
              <div className="detail-item"><span className="detail-label">Correct:</span><span className="detail-value" style={{color:'#388e3c'}}>{correctCount}</span></div>
              <div className="detail-item"><span className="detail-label">Wrong:</span><span className="detail-value" style={{color:'#e53935'}}>{wrongCount}</span></div>
            </div>
          </div>
          <div className="answers-column">
            <div className="column-header">Answer Review</div>
            {exam.questions.map((q: any, index: number) => {
              const userAnswer = answers[q.id];
              const isCorrect = userAnswer === q.correctAnswer;
              const hasAnswered = userAnswer !== undefined && userAnswer !== null;
              const displayIndex = q.display_index || (index + 1);
              return (
                <div key={q.id} className="preview-question">
                  <div className="question-number">
                    Question {displayIndex} - {q.type.charAt(0).toUpperCase() + q.type.slice(1)}
                  </div>
                  <div className="question-text">{q.text}</div>
                  {q.subText && <div className="question-subtext">{q.subText}</div>}
                  <div className="answer-status">
                    <div className={`status-indicator ${hasAnswered ? (isCorrect ? 'status-correct' : 'status-incorrect') : 'status-not-answered'}`}></div>
                    <div className="status-text">
                      {hasAnswered ? (isCorrect ? 'Correct' : 'Incorrect') : 'Not Answered'}
                    </div>
                  </div>
                  <div className="options-container">
                    {q.options.map((opt: any) => {
                      const isUserChoice = userAnswer === opt.id;
                      const isCorrectOption = opt.id === q.correctAnswer;
                      return (
                        <div key={opt.id} className={`answer-option${isUserChoice ? ' user-choice' : ''}${isCorrectOption ? ' correct-option' : ''}`}>
                          <span className="option-number">{opt.id}.</span>
                          <span className="option-text">
                            {opt.imageUrl ? (
                              <img src={opt.imageUrl} alt={`Option ${opt.id}`} className="option-image" />
                            ) : (
                              opt.text
                            )}
                            {isCorrectOption && <span className="correct-mark">✓</span>}
                            {isUserChoice && !isCorrectOption && <span className="user-mark">✗</span>}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {onReturn && <button className="return-button" onClick={onReturn}>Return</button>}
      </div>
    </div>
  );
};

// Google SSO callback handler
function AuthCallbackPage() {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.log('[AUTH CALLBACK] Starting callback processing...');
    console.log('[AUTH CALLBACK] Current URL:', window.location.href);
    console.log('[AUTH CALLBACK] Search params:', location.search);

    const params = new URLSearchParams(location.search);
    const accessToken = params.get('accessToken');
    const refreshToken = params.get('refreshToken');

    console.log('[AUTH CALLBACK] Tokens received:', {
      accessToken: accessToken ? 'YES' : 'NO',
      refreshToken: refreshToken ? 'YES' : 'NO'
    });

    if (accessToken && refreshToken) {
      console.log('[AUTH CALLBACK] Storing tokens and redirecting to dashboard...');
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);

      // Force reload để auth context nhận tokens mới
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1000);
    } else {
      console.log('[AUTH CALLBACK] No tokens found, redirecting to login...');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    }
  }, [location, navigate]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontSize: '18px'
    }}>
      <div>🔄 Đang xử lý đăng nhập Google...</div>
      <div style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
        Vui lòng đợi trong giây lát...
      </div>
    </div>
  );
}

const ExamView = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [exam, setExam] = useState<{
    id: number;
    title: string;
    description: string | null;
    duration: number;
    readingTime: number;
    listeningTime: number;
    questions: Question[];
    createdAt: Date;
    createdBy: number;
    isActive?: boolean | null;
    listeningAudioUrl?: string;
  } | null>(null);
  const [currentDisplayIndex, setCurrentDisplayIndex] = useState(1);
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [fontSize, setFontSize] = useState(16);
  const [isReadingActive, setIsReadingActive] = useState(true);
  const [readingTimeRemaining, setReadingTimeRemaining] = useState(0);
  const [listeningTimeRemaining, setListeningTimeRemaining] = useState(0);
  const [showScoreScreen, setShowScoreScreen] = useState(false);
  const [score, setScore] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [preloadedAudio, setPreloadedAudio] = useState<Record<number, string>>({});
  const [lastListeningDone, setLastListeningDone] = useState(false);
  const [showTestEndScreen, setShowTestEndScreen] = useState(false);
  const [showStartScreens, setShowStartScreens] = useState(true);

  const { user } = useAuth();

  // Đảm bảo hiển thị StartScreens khi component mount
  useEffect(() => {
    console.log('Setting showStartScreens to true on mount');
    setShowStartScreens(true);
  }, []);

  // Xử lý khi hoàn thành các màn hình bắt đầu
  const handleStartScreensComplete = () => {
    console.log('StartScreens completed, hiding screens');
    setShowStartScreens(false);
  };

  // Fetch exam data
  useEffect(() => {
    const fetchExam = async () => {
      try {
        setLoading(true);

        // Use the ID from the URL, or default to the first available exam if not provided
        const examId = id;

        if (!examId) {
          throw new Error('Không có ID đề thi được cung cấp');
        }

        const response = await fetch(`/api/exams/${examId}`);

        if (!response.ok) {
          throw new Error('Không tìm thấy đề thi');
        }

        const examData = await response.json();

        // Parse questions
        let examQuestions: Question[] = typeof examData.questions === 'string'
          ? JSON.parse(examData.questions)
          : examData.questions;
        // Luôn sort lại theo display_index tăng dần (nếu có), nếu không thì theo id
        examQuestions = examQuestions.slice().sort((a, b) => {
          if (a.display_index && b.display_index) return a.display_index - b.display_index;
          if (a.display_index) return -1;
          if (b.display_index) return 1;
          return a.id - b.id;
        });

        // Chuẩn bị dữ liệu
        const readingQuestions = examQuestions.filter(q => q.type === 'reading');
        const listeningQuestions = examQuestions.filter(q => q.type === 'listening');

        // Format the exam data
        const formattedExam = {
          ...examData,
          questions: examQuestions,
          createdAt: new Date(examData.createdAt),
          listeningAudioUrl: examData.listeningAudioUrl
        };

        setExam(formattedExam);
        setReadingTimeRemaining(formattedExam.readingTime * 60);
        setListeningTimeRemaining(formattedExam.listeningTime * 60);
        setError(null);

        // Đếm số câu hỏi listening để hiển thị thông báo
        const listeningQuestionsCount = examQuestions.filter((q: any) => q.type === 'listening').length;
        setListeningCount(listeningQuestionsCount);

        // Import utils functions
        const utils = await import('./lib/utils');
        const getPlayableAudioUrl = utils.getPlayableAudioUrl;
        const audioUrls: Record<number, string> = {};

        // Bắt đầu tải audio file hoàn toàn trong background
        if (formattedExam.listeningAudioUrl) {
          console.log('[AUDIO DOWNLOAD] Starting background download of main exam audio...');

          // Import audio download service
          const { audioDownloadService } = await import('./services/AudioDownloadService');

          // Bắt đầu download trong background - không chờ kết quả
          audioDownloadService.downloadAudio(formattedExam.listeningAudioUrl, 'main-exam-audio')
            .then((result) => {
              console.log('[AUDIO DOWNLOAD] Background download completed:', {
                fileName: result.fileName,
                fileSize: result.fileSize,
                audioUrl: result.audioUrl
              });

              // Lưu kết quả vào window object để sử dụng sau
              window.downloadedMainAudio = result;
            })
            .catch((error) => {
              console.error('[AUDIO DOWNLOAD] Background download failed:', error);
            });
        }

        // Preload individual listening audio files
        const questionsWithAudio = examQuestions.filter((q: any) => q.type === 'listening' && q.audioUrl);
        if (questionsWithAudio.length > 0) {
          // Preload audio files using the new direct URL method
          for (const question of questionsWithAudio) {
            if (question.audioUrl) {
              try {
                // Get direct playable URL
                const playableUrl = await getPlayableAudioUrl(question.audioUrl);

                audioUrls[question.id] = playableUrl;

                // Tạo element audio và preload
                const audio = new Audio();
                audio.src = playableUrl;
                audio.preload = 'auto';

                // Bắt đầu tải file audio (không phát)
                audio.load();

                console.log(`Preloading audio for question ${question.id}: ${playableUrl}`);
              } catch (error) {
                console.error(`Error preloading audio for question ${question.id}:`, error);
              }
            }
          }

          setPreloadedAudio(audioUrls);
        }
      } catch (err) {
        console.error('Error fetching exam:', err);
        setExam(null);
        setError('Không thể tải đề thi. Vui lòng thử lại sau hoặc chọn đề thi khác.');
      } finally {
        setLoading(false);
      }
    };

    fetchExam();
  }, [id]);

  const applicationNo = '00120135040001';
  const seatNo = 'A-7';

  // Count of listening questions for display purposes
  const [listeningCount, setListeningCount] = useState(0);

  // Theo dõi khi thời gian Reading hết
  useEffect(() => {
    if (readingTimeRemaining <= 0 && isReadingActive && exam) {
      setIsReadingActive(false);

      // Tìm câu hỏi đầu tiên của phần Listening
      const firstListeningIndex = exam.questions.findIndex(q => q.type === 'listening');

      // Nếu có câu hỏi Listening, chuyển đến câu đó, ngược lại giữ nguyên vị trí
      if (firstListeningIndex !== -1) {
        setCurrentDisplayIndex(firstListeningIndex + 1);
      }

      // Log thông báo chuyển sang phần Listening
      console.log('Hết thời gian Reading, chuyển sang phần thi Listening');
    }
  }, [readingTimeRemaining, isReadingActive, exam]);

  const handleSelectOption = (questionId: number, optionId: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: optionId
    }));
  };

  // Helper to get the current question by display_index
  const getCurrentQuestion = () => {
    if (!exam) return null;
    return exam.questions.find(q => q.display_index === currentDisplayIndex) || null;
  };

  // Sort questions by display_index when loading the exam
  const sortedQuestions = React.useMemo(() => {
    if (!exam?.questions) return [];
    return [...exam.questions].sort((a, b) => a.display_index - b.display_index);
  }, [exam?.questions]);

  // Update the question navigation to use display_index
  const handleNavigate = (displayIndex: number) => {
    if (!exam) return;
    const question = exam.questions.find(q => q.display_index === displayIndex);
    if (question) {
      setCurrentDisplayIndex(displayIndex);
      setIsReadingActive(question.type !== 'listening');
    }
  };

  const handleSubmit = async () => {
    console.log('[DEBUG] handleSubmit called, exam:', exam);
    if (!exam) {
      console.log('[DEBUG] handleSubmit early return because exam is null');
      return;
    }
    // Tính điểm bằng cách đếm số câu trả lời đúng
    const correctAnswers = Object.entries(answers).filter(([questionId, answerId]) => {
      const question = exam.questions.find((q: Question) => q.id === parseInt(questionId));
      if (question && 'correctAnswer' in question) {
        return answerId === (question as any).correctAnswer;
      } else {
        return question && answerId === 1;
      }
    }).length;
    const calculatedScore = Math.round((correctAnswers / exam.questions.length) * 100);
    setScore(calculatedScore);

    // Chuẩn bị result_json chứa đầy đủ thông tin từng câu hỏi
    const result_json = exam.questions.map((question: Question) => {
      const userAnswer = answers[question.id];
      const isCorrect = userAnswer === question.correctAnswer;
      return {
        questionId: question.id,
        questionText: question.text,
        questionType: question.type,
        options: question.options,
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect
      };
    });

    if (user && user.role !== 'admin') {
      try {
        const now = new Date();
        const vietnamTime = new Date(now.getTime() + 7 * 60 * 60 * 1000);
        const payload = {
          userId: user.id,
          examId: exam.id,
          score: calculatedScore,
          result_json,
          startedAt: new Date(exam.createdAt).toISOString(),
          completedAt: vietnamTime.toISOString(),
        };
        console.log('[DEBUG] Sending full exam result to backend:', payload);
        const res = await fetch('/api/exam-attempts/full', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(payload),
        });
        console.log('[DEBUG] Received response from backend (full):', res);
        if (!res.ok) throw new Error('Failed to save full exam result');
        const data = await res.json();
        console.log('[DEBUG] Backend saved full exam result:', data);
      } catch (err) {
        console.error('[DEBUG] Failed to save full exam result:', err);
        alert('Failed to save exam results. Please try again later.');
      }
      setShowScoreScreen(true);
    } else {
      // User chưa đăng nhập, chỉ hiển thị kết quả local, không gọi API
      console.log('Anonymous user: not saving exam results to server.');
      setShowScoreScreen(true);
    }
  };

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 24));
  };

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 12));
  };

  const handleNextQuestion = () => {
    if (!exam) return;
    const nextDisplayIndex = currentDisplayIndex + 1;
    const next = exam.questions.find(q => q.display_index === nextDisplayIndex);
    if (next) {
      setCurrentDisplayIndex(nextDisplayIndex);
      setIsReadingActive(next.type !== 'listening');
    }
  };

  const handleTimerUpdate = (section: 'reading' | 'listening', timeRemaining: number) => {
    if (section === 'reading') {
      setReadingTimeRemaining(timeRemaining);
    } else {
      setListeningTimeRemaining(timeRemaining);
    }
  };

  // Helper: kiểm tra có phải câu listening cuối cùng không
  const isLastListeningQuestion = () => {
    if (!exam) return false;
    const listeningQuestions = exam.questions.filter(q => q.type === 'listening');
    if (listeningQuestions.length === 0) return false;
    const lastListening = listeningQuestions[listeningQuestions.length - 1];
    return !!(currentQuestion && currentQuestion.id === lastListening.id);
  };

  // Hàm chuyển sang câu listening tiếp theo
  const goToNextListeningQuestion = () => {
    if (!exam) return;
    // Tìm display_index câu listening tiếp theo sau câu hiện tại
    const listeningQuestions = exam.questions.filter(q => q.type === 'listening' && q.display_index > currentDisplayIndex);
    if (listeningQuestions.length > 0) {
      const nextListening = listeningQuestions.reduce((min, q) => q.display_index < min.display_index ? q : min, listeningQuestions[0]);
      setCurrentDisplayIndex(nextListening.display_index);
      setIsReadingActive(false);
    } else {
      // Không còn câu listening nào
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Đang tải đề thi...</p>
      </div>
    );
  }

  // Error state
  if (error && !exam) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.href = '/'}>Quay lại trang chủ</button>
      </div>
    );
  }

  // Make sure exam exists before proceeding
  if (!exam) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <p>Không tìm thấy đề thi.</p>
        <button onClick={() => window.location.href = '/'}>Quay lại trang chủ</button>
      </div>
    );
  }

  // Kiểm tra xem questions có tồn tại và có phần tử ở currentDisplayIndex không
  const currentQuestion = getCurrentQuestion();
  const answeredCount = Object.keys(answers).length;

  // Nếu không tìm thấy câu hỏi hiện tại
  if (!currentQuestion) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <p>Lỗi: Không tìm thấy câu hỏi ở vị trí {currentDisplayIndex}.</p>
        <button onClick={() => window.location.href = '/'}>Quay lại trang chủ</button>
      </div>
    );
  }

  return (
    <div className="exam-container" style={{ fontSize: `${fontSize}px` }}>
      {showStartScreens ? (
        <StartScreens onComplete={handleStartScreensComplete} />
      ) : (
        <>
          <ExamHeader
            applicationNo={applicationNo}
            seatNo={seatNo}
          />

          <div className="exam-sidebar">
            <div className="timer-section">
              <div className="timer-title">
                {isReadingActive ? 'Reading Time Remaining' : 'Listening Time Remaining'}
              </div>
              <div className="timer-display">
                {isReadingActive
                  ? Math.floor(readingTimeRemaining / 60).toString().padStart(2, '0') + ':' + (readingTimeRemaining % 60).toString().padStart(2, '0')
                  : Math.floor(listeningTimeRemaining / 60).toString().padStart(2, '0') + ':' + (listeningTimeRemaining % 60).toString().padStart(2, '0')
                }
              </div>
              <TimerSection
                readingTime={readingTimeRemaining}
                listeningTime={listeningTimeRemaining}
                isReadingActive={isReadingActive}
                onTimerUpdate={handleTimerUpdate}
                hideDisplay={true}
              />
            </div>

            <div className="question-navigation">
              <div className="nav-title">Reading Questions</div>
              <div className="question-grid">
                {sortedQuestions
                  .filter(q => q.type === 'reading')
                  .map(question => (
                    <button
                      key={question.id}
                      className={`question-button ${
                        currentDisplayIndex === question.display_index ? 'active' : ''
                      } ${answers[question.id] !== undefined ? 'answered' : ''}`}
                      onClick={() => handleNavigate(question.display_index)}
                    >
                      {question.display_index}
                    </button>
                  ))}
              </div>

              {listeningCount > 0 && (
                <>
                  <div className="question-section-divider">Listening Questions</div>
                  <div className="question-grid">
                    {sortedQuestions
                      .filter(q => q.type === 'listening')
                      .map(question => (
                        <button
                          key={question.id}
                          className={`question-button ${
                            currentDisplayIndex === question.display_index ? 'active' : ''
                          } ${answers[question.id] !== undefined ? 'answered' : ''}`}
                          onClick={() => handleNavigate(question.display_index)}
                        >
                          {question.display_index}
                        </button>
                      ))}
                  </div>
                </>
              )}
            </div>

            <div className="exam-controls">
              <div className="font-controls">
                <button className="font-button" onClick={decreaseFontSize}>A-</button>
                <button className="font-button" onClick={increaseFontSize}>A+</button>
              </div>
              <SubmitButton onSubmit={handleSubmit} disabled={showScoreScreen || showTestEndScreen} />
            </div>
          </div>

          <div className="main-content">
            <div className="question-container">
              <div className="question-header">
                <div className="question-number">Question {currentDisplayIndex}</div>
                <div className={`question-type ${currentQuestion.type}`}>
                  {currentQuestion.type.charAt(0).toUpperCase() + currentQuestion.type.slice(1)}
                </div>
              </div>

              {currentQuestion.type === 'listening' && currentQuestion.audioUrl && (
                <div className="audio-section">
                  <div className="audio-icon">🔊</div>
                  <div className="audio-controls">
                    <button className="audio-play-button">▶</button>
                    <div className="audio-progress">
                      <div className="audio-progress-bar" style={{ width: '30%' }}></div>
                    </div>
                    <div className="audio-time">00:30 / 01:45</div>
                  </div>
                </div>
              )}

              <div className="question-content">
                <div className="question-text" style={{ fontSize: `${fontSize}px` }}>
                  {currentQuestion.text}
                </div>
                {currentQuestion.subText && (
                  <div className="question-subtext" style={{ fontSize: `${fontSize - 2}px` }}>
                    {currentQuestion.subText}
                  </div>
                )}
              </div>

              <div className="options-list">
                {currentQuestion.options.map(option => (
                  <div
                    key={option.id}
                    className={`option ${answers[currentQuestion.id] === option.id ? 'selected' : ''}`}
                    onClick={() => handleSelectOption(currentQuestion.id, option.id)}
                  >
                    <span className="option-number">{option.id}.</span>
                    <span className="option-text">
                      {option.text}
                      {option.imageUrl && (
                        <img src={option.imageUrl} alt={`Option ${option.id}`} className="option-image" />
                      )}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <QuestionArea
              question={currentQuestion}
              fontSize={fontSize}
              onSelectOption={handleSelectOption}
              selectedOption={answers[currentQuestion.id]}
              isReadingActive={isReadingActive}
              isLastListeningQuestion={isLastListeningQuestion()}
              goToNextListeningQuestion={goToNextListeningQuestion}
              hideDisplay={true}
            />
          </div>

          {showScoreScreen && (
            <ExamResultScreen
              exam={exam}
              answers={answers}
              score={score}
              onReturn={() => setShowScoreScreen(false)}
            />
          )}
          {showTestEndScreen && <TestEndScreen onReturn={() => setShowTestEndScreen(false)} />}
        </>
      )}
    </div>
  );
};

// CSS inline cho màn hình StartScreens
const startScreensStyle = `
  .start-screens-wrapper {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
  }
`;

// Main App component with Routes
const App = () => {
  return (
    <>
      <style>{startScreensStyle}</style>
      <Routes>
      <Route path="/" element={<UserDashboard />} />
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />
      <Route path="/auth/callback" element={<AuthCallbackPage />} />
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <UserDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin"
        element={
          <ProtectedRoute adminOnly>
            <AdminPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/exam/:id"
        element={
          <ProtectedRoute>
            <ExamView />
          </ProtectedRoute>
        }
      />
      <Route path="*" element={<div>Page not found</div>} />
    </Routes>
    </>
  );
};

export default App;