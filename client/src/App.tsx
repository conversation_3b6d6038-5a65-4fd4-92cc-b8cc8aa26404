import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, useNavigate, useParams, useLocation } from 'react-router-dom';
import './index.css';
import './assets/quick-access.css';
import './assets/common-buttons.css';
import './assets/dialog-styles.css';
import './assets/test-end.css';
import './assets/test-result.css';
import { FiEdit, FiHome, FiUser, FiBookOpen, FiPlay, FiSettings } from 'react-icons/fi';
import UserDashboard from './components/user/UserDashboard';

// EPS-TOPIK Components
import ExamHeader from './components/ExamHeader';
import QuestionArea, { Question } from './components/QuestionArea';
import TimerSection from './components/TimerSection';
import QuestionNavigation from './components/QuestionNavigation';
import SubmitButton from './components/SubmitButton';
import TestEndScreen from './components/TestEndScreen';
import StartScreens from './components/StartScreens';
import AdminPage from './components/admin/AdminPage';

// EPS-TOPIK Exam View component

// Login and Register component in separate file to avoid circular dependency
import LoginPage from './components/LoginPage';
import RegisterPage from './components/RegisterPage';
import { ProtectedRoute } from './components/ProtectedRoute';

// Exam View component
import { useAuth } from './hooks/use-auth';

// Exam Result Screen component (tách riêng)
export const ExamResultScreen = ({ exam, answers, score, onReturn }: any) => {
  // Tính số câu đúng và số câu sai
  const answeredCount = Object.keys(answers).length;
  const correctCount = Object.entries(answers).filter(([questionId, answerId]) => {
    const question = exam.questions.find((q: any) => q.id === parseInt(questionId));
    if (question && 'correctAnswer' in question) {
      return answerId === (question).correctAnswer;
    }
    return false;
  }).length;
  const wrongCount = answeredCount - correctCount;
  const notAnsweredCount = exam.questions.length - answeredCount;
  const readingQuestions = exam.questions.filter((q: any) => q.type === 'reading');
  const listeningQuestions = exam.questions.filter((q: any) => q.type === 'listening');
  const totalScore = 200;
  const calculatedScore = correctCount * 5;

  return (
    <div className="score-screen">
      <div className="score-container">
        <div className="score-display">
          <div className="score-value">{score} / 200</div>
          <div className="score-label">Score</div>
        </div>
        <div className="two-grid-layout">
          <div className="result-column">
            <div className="column-header">Summary</div>
            <div className="score-details">
              <div className="detail-item"><span className="detail-label">Total Questions:</span><span className="detail-value">{exam.questions.length}</span></div>
              <div className="detail-item"><span className="detail-label">Answered:</span><span className="detail-value">{answeredCount}</span></div>
              <div className="detail-item"><span className="detail-label">Correct:</span><span className="detail-value" style={{color:'#388e3c'}}>{correctCount}</span></div>
              <div className="detail-item"><span className="detail-label">Wrong:</span><span className="detail-value" style={{color:'#e53935'}}>{wrongCount}</span></div>
            </div>
          </div>
          <div className="answers-column">
            <div className="column-header">Answer Review</div>
            {exam.questions.map((q: any, index: number) => {
              const userAnswer = answers[q.id];
              const isCorrect = userAnswer === q.correctAnswer;
              const hasAnswered = userAnswer !== undefined && userAnswer !== null;
              const displayIndex = q.display_index || (index + 1);
              return (
                <div key={q.id} className="preview-question">
                  <div className="question-number">
                    Question {displayIndex} - {q.type.charAt(0).toUpperCase() + q.type.slice(1)}
                  </div>
                  <div className="question-text">{q.text}</div>
                  {q.subText && <div className="question-subtext">{q.subText}</div>}
                  <div className="answer-status">
                    <div className={`status-indicator ${hasAnswered ? (isCorrect ? 'status-correct' : 'status-incorrect') : 'status-not-answered'}`}></div>
                    <div className="status-text">
                      {hasAnswered ? (isCorrect ? 'Correct' : 'Incorrect') : 'Not Answered'}
                    </div>
                  </div>
                  <div className="options-container">
                    {q.options.map((opt: any) => {
                      const isUserChoice = userAnswer === opt.id;
                      const isCorrectOption = opt.id === q.correctAnswer;
                      return (
                        <div key={opt.id} className={`answer-option${isUserChoice ? ' user-choice' : ''}${isCorrectOption ? ' correct-option' : ''}`}>
                          <span className="option-number">{opt.id}.</span>
                          <span className="option-text">
                            {opt.imageUrl ? (
                              <img src={opt.imageUrl} alt={`Option ${opt.id}`} className="option-image" />
                            ) : (
                              opt.text
                            )}
                            {isCorrectOption && <span className="correct-mark">✓</span>}
                            {isUserChoice && !isCorrectOption && <span className="user-mark">✗</span>}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {onReturn && <button className="return-button" onClick={onReturn}>Return</button>}
      </div>
    </div>
  );
};

const ExamView = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [exam, setExam] = useState<{
    id: number;
    title: string;
    description: string | null;
    duration: number;
    readingTime: number;
    listeningTime: number;
    questions: Question[];
    createdAt: Date;
    createdBy: number;
    isActive?: boolean | null;
    listeningAudioUrl?: string;
  } | null>(null);
  const [currentDisplayIndex, setCurrentDisplayIndex] = useState(1);
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [fontSize, setFontSize] = useState(16);
  const [isReadingActive, setIsReadingActive] = useState(true);
  const [readingTimeRemaining, setReadingTimeRemaining] = useState(0);
  const [listeningTimeRemaining, setListeningTimeRemaining] = useState(0);
  const [showScoreScreen, setShowScoreScreen] = useState(false);
  const [score, setScore] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [preloadedAudio, setPreloadedAudio] = useState<Record<number, string>>({});
  const [lastListeningDone, setLastListeningDone] = useState(false);
  const [showTestEndScreen, setShowTestEndScreen] = useState(false);
  const [showStartScreens, setShowStartScreens] = useState(true);
  const [audioInfo, setAudioInfo] = useState<{
    fileName: string;
    fileSize: string;
    currentTime: number;
    duration: number;
    isPlaying: boolean;
    loadState: string;
    error?: string;
    url: string;
    readyState: number;
    networkState: number;
    bufferedRanges: string;
    bufferedPercent: number;
  } | null>(null);

  const { user } = useAuth();

  // Xử lý khi hoàn thành các màn hình bắt đầu
  const handleStartScreensComplete = () => {
    setShowStartScreens(false);
  };

  // Fetch exam data
  useEffect(() => {
    const fetchExam = async () => {
      try {
        setLoading(true);

        // Use the ID from the URL, or default to the first available exam if not provided
        const examId = id;

        if (!examId) {
          throw new Error('Không có ID đề thi được cung cấp');
        }

        const response = await fetch(`/api/exams/${examId}`);

        if (!response.ok) {
          throw new Error('Không tìm thấy đề thi');
        }

        const examData = await response.json();

        // Parse questions
        let examQuestions: Question[] = typeof examData.questions === 'string'
          ? JSON.parse(examData.questions)
          : examData.questions;
        // Luôn sort lại theo display_index tăng dần (nếu có), nếu không thì theo id
        examQuestions = examQuestions.slice().sort((a, b) => {
          if (a.display_index && b.display_index) return a.display_index - b.display_index;
          if (a.display_index) return -1;
          if (b.display_index) return 1;
          return a.id - b.id;
        });

        // Chuẩn bị dữ liệu
        const readingQuestions = examQuestions.filter(q => q.type === 'reading');
        const listeningQuestions = examQuestions.filter(q => q.type === 'listening');

        // Format the exam data
        const formattedExam = {
          ...examData,
          questions: examQuestions,
          createdAt: new Date(examData.createdAt),
          listeningAudioUrl: examData.listeningAudioUrl
        };

        setExam(formattedExam);
        setReadingTimeRemaining(formattedExam.readingTime * 60);
        setListeningTimeRemaining(formattedExam.listeningTime * 60);
        setError(null);

        // Đếm số câu hỏi listening để hiển thị thông báo
        const listeningQuestionsCount = examQuestions.filter((q: any) => q.type === 'listening').length;
        setListeningCount(listeningQuestionsCount);

        // Import utils functions
        const utils = await import('./lib/utils');
        const getPlayableAudioUrl = utils.getPlayableAudioUrl;
        const audioUrls: Record<number, string> = {};

        // Preload main exam listening audio if available
        if (formattedExam.listeningAudioUrl) {
          const preloadMainAudio = async () => {
            try {
              console.log('[AUDIO DEBUG] Starting preload of main exam audio file...');
              console.log('[AUDIO DEBUG] Original URL:', formattedExam.listeningAudioUrl);

              let playableUrl;
              let audioElement;

              try {
                // Lấy URL có thể phát trực tiếp
                playableUrl = await getPlayableAudioUrl(formattedExam.listeningAudioUrl);
                console.log('[AUDIO DEBUG] Got playable URL:', playableUrl);

                // Kiểm tra URL có hợp lệ không
                if (!playableUrl) {
                  throw new Error('URL trả về rỗng');
                }

                // Kiểm tra URL có thể truy cập được không và kiểm tra Range Requests
                try {
                  const testResponse = await fetch(playableUrl, {
                    method: 'HEAD',
                    signal: AbortSignal.timeout(10000) // 10 giây timeout
                  });
                  if (!testResponse.ok) {
                    throw new Error(`Không thể truy cập file: ${testResponse.status} ${testResponse.statusText}`);
                  }

                  // Kiểm tra hỗ trợ Range Requests
                  const acceptRanges = testResponse.headers.get('accept-ranges');
                  const contentLength = testResponse.headers.get('content-length');
                  const contentType = testResponse.headers.get('content-type');

                  console.log('[AUDIO DEBUG] Server capabilities:', {
                    status: testResponse.status,
                    acceptRanges,
                    contentLength,
                    contentType,
                    supportsRanges: acceptRanges === 'bytes'
                  });

                  // Nếu server không hỗ trợ Range Requests, cảnh báo và ưu tiên proxy
                  if (acceptRanges !== 'bytes') {
                    console.warn('[AUDIO DEBUG] Server does not support Range Requests - switching to proxy for better streaming');
                    throw new Error('No Range Requests support - use proxy instead');
                  }

                } catch (fetchError) {
                  console.error('[AUDIO DEBUG] Error testing URL accessibility:', fetchError);
                  // Nếu là timeout hoặc lỗi mạng, thử dùng proxy
                  throw fetchError;
                }

              } catch (urlError) {
                console.error('[AUDIO DEBUG] Error with direct URL, trying proxy:', urlError);

                // Thử sử dụng proxy URL nếu URL trực tiếp không hoạt động
                const utils = await import('./lib/utils');
                const extractGoogleDriveFileId = utils.extractGoogleDriveFileId;
                const fileId = extractGoogleDriveFileId(formattedExam.listeningAudioUrl);

                if (fileId) {
                  playableUrl = `/api/proxy/gdrive?id=${fileId}`;
                  console.log('[AUDIO DEBUG] Using proxy URL:', playableUrl);
                } else {
                  throw new Error('Không thể trích xuất ID file và không thể sử dụng proxy');
                }
              }

              // Lưu URL vào window object để sử dụng sau
              window.preloadedMainAudioUrl = playableUrl;

              // Tạo audio element với cơ chế xử lý lỗi tốt hơn
              audioElement = new Audio();
              audioElement.preload = 'metadata'; // Thay đổi từ 'auto' thành 'metadata' để giảm streaming
              audioElement.crossOrigin = 'anonymous'; // Thêm để xử lý CORS

              // Thêm event listeners để debug
              audioElement.addEventListener('loadstart', () => {
                console.log('[AUDIO DEBUG] Load started');
              });

              audioElement.addEventListener('loadedmetadata', () => {
                console.log('[AUDIO DEBUG] Metadata loaded, duration:', audioElement.duration);
              });

              audioElement.addEventListener('loadeddata', () => {
                console.log('[AUDIO DEBUG] Data loaded');
              });

              audioElement.addEventListener('canplay', () => {
                console.log('[AUDIO DEBUG] Can play');
              });

              audioElement.addEventListener('canplaythrough', () => {
                console.log('[AUDIO DEBUG] Can play through');
              });

              audioElement.addEventListener('error', (e) => {
                console.error('[AUDIO DEBUG] Audio error:', e, audioElement.error);
              });

              // Lưu audio element vào window object để tái sử dụng
              window.preloadedMainAudioElement = audioElement;

              // Thử tải file audio với cơ chế fallback
              try {
                console.log('[AUDIO DEBUG] Loading audio into element:', playableUrl);

                // Kiểm tra trước bằng fetch
                try {
                  const audioResponse = await fetch(playableUrl, {
                    signal: AbortSignal.timeout(15000) // 15 giây timeout
                  });
                  if (!audioResponse.ok) {
                    throw new Error(`Không thể tải file âm thanh: ${audioResponse.status} ${audioResponse.statusText}`);
                  }

                  // Kiểm tra content-type
                  const contentType = audioResponse.headers.get('content-type');
                  console.log('[AUDIO DEBUG] File content type:', contentType);

                  if (contentType && !contentType.includes('audio/') && !contentType.includes('video/') && !contentType.includes('application/octet-stream')) {
                    console.warn('[AUDIO DEBUG] File might not be an audio file. Content-Type:', contentType);
                  }
                } catch (fetchError) {
                  console.error('[AUDIO DEBUG] Error pre-checking audio file:', fetchError);
                  // Vẫn tiếp tục thử load vào audio element
                }

                // Đặt src và bắt đầu tải
                audioElement.src = playableUrl;
                audioElement.load();

                console.log('[AUDIO DEBUG] Audio element load initiated');

              } catch (loadError) {
                console.error('[AUDIO DEBUG] Error loading audio:', loadError);
                throw loadError;
              }

              // Đợi cho audio sẵn sàng với timeout ngắn hơn
              await new Promise((resolve, reject) => {
                let resolved = false;

                const cleanup = () => {
                  if (audioElement) {
                    audioElement.removeEventListener('canplaythrough', onCanPlayThrough);
                    audioElement.removeEventListener('error', onError);
                  }
                };

                const onCanPlayThrough = () => {
                  if (resolved) return;
                  resolved = true;
                  cleanup();
                  console.log('[AUDIO DEBUG] Main exam audio file fully loaded and ready to play');
                  console.log('[AUDIO DEBUG] Final audio state:', {
                    readyState: audioElement.readyState,
                    networkState: audioElement.networkState,
                    duration: audioElement.duration,
                    error: audioElement.error
                  });
                  resolve(void 0);
                };

                const onError = (e) => {
                  if (resolved) return;
                  resolved = true;
                  cleanup();
                  console.error('[AUDIO DEBUG] Audio loading error:', e, audioElement.error);
                  reject(new Error('Audio loading failed'));
                };

                audioElement.addEventListener('canplaythrough', onCanPlayThrough);
                audioElement.addEventListener('error', onError);

                // Timeout ngắn hơn - 3 giây
                setTimeout(() => {
                  if (resolved) return;
                  resolved = true;
                  cleanup();
                  console.log('[AUDIO DEBUG] Main exam audio file preloaded (timeout):', playableUrl);
                  console.log('[AUDIO DEBUG] Timeout audio state:', {
                    readyState: audioElement.readyState,
                    networkState: audioElement.networkState,
                    duration: audioElement.duration,
                    error: audioElement.error
                  });
                  // Không reject, chỉ resolve để tiếp tục
                  resolve(void 0);
                }, 3000);
              });
          } catch (error) {
            console.error('Error preloading main exam audio:', error);
          }
        };

        // Gọi hàm preload
        await preloadMainAudio();
      }

        // Preload individual listening audio files
        const questionsWithAudio = examQuestions.filter((q: any) => q.type === 'listening' && q.audioUrl);
        if (questionsWithAudio.length > 0) {
          // Preload audio files using the new direct URL method
          for (const question of questionsWithAudio) {
            if (question.audioUrl) {
              try {
                // Get direct playable URL
                const playableUrl = await getPlayableAudioUrl(question.audioUrl);

                audioUrls[question.id] = playableUrl;

                // Tạo element audio và preload
                const audio = new Audio();
                audio.src = playableUrl;
                audio.preload = 'auto';

                // Bắt đầu tải file audio (không phát)
                audio.load();

                console.log(`Preloading audio for question ${question.id}: ${playableUrl}`);
              } catch (error) {
                console.error(`Error preloading audio for question ${question.id}:`, error);
              }
            }
          }

          setPreloadedAudio(audioUrls);
        }
      } catch (err) {
        console.error('Error fetching exam:', err);
        setExam(null);
        setError('Không thể tải đề thi. Vui lòng thử lại sau hoặc chọn đề thi khác.');
      } finally {
        setLoading(false);
      }
    };

    fetchExam();
  }, [id]);

  const applicationNo = '00120135040001';
  const seatNo = 'A-7';

  // Count of listening questions for display purposes
  const [listeningCount, setListeningCount] = useState(0);

  // Theo dõi khi thời gian Reading hết
  useEffect(() => {
    if (readingTimeRemaining <= 0 && isReadingActive && exam) {
      setIsReadingActive(false);

      // Tìm câu hỏi đầu tiên của phần Listening
      const firstListeningIndex = exam.questions.findIndex(q => q.type === 'listening');

      // Nếu có câu hỏi Listening, chuyển đến câu đó, ngược lại giữ nguyên vị trí
      if (firstListeningIndex !== -1) {
        setCurrentDisplayIndex(firstListeningIndex + 1);
      }

      // Log thông báo chuyển sang phần Listening
      console.log('Hết thời gian Reading, chuyển sang phần thi Listening');
    }
  }, [readingTimeRemaining, isReadingActive, exam]);

  const handleSelectOption = (questionId: number, optionId: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: optionId
    }));
  };

  // Helper to get the current question by display_index
  const getCurrentQuestion = () => {
    if (!exam) return null;
    return exam.questions.find(q => q.display_index === currentDisplayIndex) || null;
  };

  // Sort questions by display_index when loading the exam
  const sortedQuestions = React.useMemo(() => {
    if (!exam?.questions) return [];
    return [...exam.questions].sort((a, b) => a.display_index - b.display_index);
  }, [exam?.questions]);

  // Update the question navigation to use display_index
  const handleNavigate = (displayIndex: number) => {
    if (!exam) return;
    const question = exam.questions.find(q => q.display_index === displayIndex);
    if (question) {
      setCurrentDisplayIndex(displayIndex);
      setIsReadingActive(question.type !== 'listening');
    }
  };

  const handleSubmit = async () => {
    console.log('[DEBUG] handleSubmit called, exam:', exam);
    if (!exam) {
      console.log('[DEBUG] handleSubmit early return because exam is null');
      return;
    }
    // Tính điểm bằng cách đếm số câu trả lời đúng
    const correctAnswers = Object.entries(answers).filter(([questionId, answerId]) => {
      const question = exam.questions.find((q: Question) => q.id === parseInt(questionId));
      if (question && 'correctAnswer' in question) {
        return answerId === (question as any).correctAnswer;
      } else {
        return question && answerId === 1;
      }
    }).length;
    const calculatedScore = Math.round((correctAnswers / exam.questions.length) * 100);
    setScore(calculatedScore);

    // Chuẩn bị result_json chứa đầy đủ thông tin từng câu hỏi
    const result_json = exam.questions.map((question: Question) => {
      const userAnswer = answers[question.id];
      const isCorrect = userAnswer === question.correctAnswer;
      return {
        questionId: question.id,
        questionText: question.text,
        questionType: question.type,
        options: question.options,
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect
      };
    });

    if (user && user.role !== 'admin') {
      try {
        const now = new Date();
        const vietnamTime = new Date(now.getTime() + 7 * 60 * 60 * 1000);
        const payload = {
          userId: user.id,
          examId: exam.id,
          score: calculatedScore,
          result_json,
          startedAt: new Date(exam.createdAt).toISOString(),
          completedAt: vietnamTime.toISOString(),
        };
        console.log('[DEBUG] Sending full exam result to backend:', payload);
        const res = await fetch('/api/exam-attempts/full', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(payload),
        });
        console.log('[DEBUG] Received response from backend (full):', res);
        if (!res.ok) throw new Error('Failed to save full exam result');
        const data = await res.json();
        console.log('[DEBUG] Backend saved full exam result:', data);
      } catch (err) {
        console.error('[DEBUG] Failed to save full exam result:', err);
        alert('Failed to save exam results. Please try again later.');
      }
      setShowScoreScreen(true);
    } else {
      // User chưa đăng nhập, chỉ hiển thị kết quả local, không gọi API
      console.log('Anonymous user: not saving exam results to server.');
      setShowScoreScreen(true);
    }
  };

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 24));
  };

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 12));
  };

  const handleNextQuestion = () => {
    if (!exam) return;
    const nextDisplayIndex = currentDisplayIndex + 1;
    const next = exam.questions.find(q => q.display_index === nextDisplayIndex);
    if (next) {
      setCurrentDisplayIndex(nextDisplayIndex);
      setIsReadingActive(next.type !== 'listening');
    }
  };

  const handleTimerUpdate = (section: 'reading' | 'listening', timeRemaining: number) => {
    if (section === 'reading') {
      setReadingTimeRemaining(timeRemaining);
    } else {
      setListeningTimeRemaining(timeRemaining);
    }
  };

  // Helper: kiểm tra có phải câu listening cuối cùng không
  const isLastListeningQuestion = () => {
    if (!exam) return false;
    const listeningQuestions = exam.questions.filter(q => q.type === 'listening');
    if (listeningQuestions.length === 0) return false;
    const lastListening = listeningQuestions[listeningQuestions.length - 1];
    return !!(currentQuestion && currentQuestion.id === lastListening.id);
  };

  // Hàm chuyển sang câu listening tiếp theo
  const goToNextListeningQuestion = () => {
    if (!exam) return;
    // Tìm display_index câu listening tiếp theo sau câu hiện tại
    const listeningQuestions = exam.questions.filter(q => q.type === 'listening' && q.display_index > currentDisplayIndex);
    if (listeningQuestions.length > 0) {
      const nextListening = listeningQuestions.reduce((min, q) => q.display_index < min.display_index ? q : min, listeningQuestions[0]);
      setCurrentDisplayIndex(nextListening.display_index);
      setIsReadingActive(false);
    } else {
      // Không còn câu listening nào
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Đang tải đề thi...</p>
      </div>
    );
  }

  // Error state
  if (error && !exam) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.href = '/'}>Quay lại trang chủ</button>
      </div>
    );
  }

  // Make sure exam exists before proceeding
  if (!exam) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <p>Không tìm thấy đề thi.</p>
        <button onClick={() => window.location.href = '/'}>Quay lại trang chủ</button>
      </div>
    );
  }

  // Kiểm tra xem questions có tồn tại và có phần tử ở currentDisplayIndex không
  const currentQuestion = getCurrentQuestion();
  const answeredCount = Object.keys(answers).length;

  // Nếu không tìm thấy câu hỏi hiện tại
  if (!currentQuestion) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <p>Lỗi: Không tìm thấy câu hỏi ở vị trí {currentDisplayIndex}.</p>
        <button onClick={() => window.location.href = '/'}>Quay lại trang chủ</button>
      </div>
    );
  }

  // Hiển thị màn hình kết quả kiểm tra nếu đã submit
  if (showScoreScreen) {
    console.log('[DEBUG] Rendering score screen');
    return (
      <ExamResultScreen
        exam={exam}
        answers={answers}
        score={score}
        onReturn={() => {
          if (user) {
            if (user.role === 'admin') {
              navigate('/admin');
            } else {
              navigate('/dashboard');
            }
          } else {
            navigate('/');
          }
        }}
      />
    );
  }

  // Log để debug
  console.log("currentQuestion:", currentQuestion);
  console.log("listeningCount:", listeningCount);

  // No transition screen content needed

  return (
    <div className="exam-container" style={{ fontSize: `${fontSize}px` }}>
      <ExamHeader
        applicationNo={applicationNo}
        seatNo={seatNo}
      />

      <div className="exam-version">
        <span className="version-text">Version: 2.10.12.1</span>

        <div className="font-size-controls">
          <button
            className="font-button"
            onClick={increaseFontSize}
          >
            A+
          </button>
          <button
            className="font-button"
            onClick={decreaseFontSize}
          >
            A-
          </button>

          <div className="font-size-slider">
            <span>유지크기:</span>
            <div className="slider-line">
              <div className="slider-handle" style={{ left: `${((fontSize - 12) / 12) * 100}%` }}></div>
            </div>
          </div>
        </div>
      </div>

      {showStartScreens ? (
        <StartScreens
          applicationNo={applicationNo}
          seatNo={seatNo}
          onComplete={handleStartScreensComplete}
        />
      ) : showTestEndScreen ? (
        <TestEndScreen
          applicationNo={applicationNo}
          seatNo={seatNo}
          onClose={() => {
            console.log('[DEBUG] TestEndScreen onClose called');
            setShowTestEndScreen(false);
            handleSubmit();
          }}
        />
      ) : (
        <div className="main-content">
          <div className="left-panel">
            <div className="question-area">
              {currentQuestion && (
                <div className="question active">
                  <div className="question-number">
                    Question {currentQuestion.display_index} - {currentQuestion.type.charAt(0).toUpperCase() + currentQuestion.type.slice(1)}
                  </div>
                  <QuestionArea
                    question={{
                      ...currentQuestion,
                      examListeningAudioUrl: exam.listeningAudioUrl
                    }}
                    currentQuestionNumber={currentQuestion.display_index}
                    selectedOption={answers[currentQuestion.id]}
                    onSelectOption={(optionId) => handleSelectOption(currentQuestion.id, optionId)}
                    onAudioEnded={currentQuestion.type === 'listening' ? goToNextListeningQuestion : undefined}
                    onNavigate={handleNavigate}
                    allQuestions={exam.questions}
                    onAudioInfoUpdate={setAudioInfo}
                  />
                </div>
              )}
            </div>
          </div>

          <div className="right-panel">
            <div style={{ marginBottom: '0.2rem' }}>
              <SubmitButton
                disabled={false}
                onSubmit={handleSubmit}
                isReadingActive={isReadingActive}
                timeRemaining={isReadingActive ? readingTimeRemaining : listeningTimeRemaining}
                lastListeningDone={isLastListeningQuestion()}
                onTestEnd={() => setShowTestEndScreen(true)}
              />
            </div>

            <TimerSection
              readingMinutes={Math.ceil(Number(exam.readingTime) / 60)}
              listeningMinutes={Math.ceil(Number(exam.listeningTime) / 60)}
              readingSeconds={Number(exam.readingTime)}
              listeningSeconds={Number(exam.listeningTime)}
              onTimeUp={handleSubmit}
              currentQuestion={currentDisplayIndex}
              totalQuestions={exam.questions.length}
              answeredQuestions={answers}
              onNavigate={handleNavigate}
              isReadingActive={isReadingActive}
              questions={exam.questions.map((q, idx) => ({ ...q, display_index: q.display_index || (idx + 1) }))}
              audioInfo={audioInfo}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Import our landing page
import LandingPage from './components/LandingPage';
import { TestHistoryContent } from './components/user/TestHistory';
import PublicTestResultPage from './components/PublicTestResultPage';

// Main App component
const App = () => {
  return (
    <Routes>
      {/* Public route that doesn't need auth */}
      <Route path="/" element={
        <ProtectedRoute publicRoute>
          <LandingPage />
        </ProtectedRoute>
      } />

      {/* Auth-related routes */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />

      {/* Protected routes */}
      <Route path="/exam/:id" element={
        <ProtectedRoute allowAnonymousForDemo={true}>
          <ExamView />
        </ProtectedRoute>
      } />
      <Route path="/admin" element={
        <ProtectedRoute adminOnly>
          <AdminPage />
        </ProtectedRoute>
      } />
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <UserDashboard />
        </ProtectedRoute>
      } />
      <Route path="/test-history/:testId" element={
        <ProtectedRoute>
          <TestHistoryContent />
        </ProtectedRoute>
      } />
      <Route path="/test-result/:attemptId" element={<PublicTestResultPage />} />

      <Route path="*" element={<div>Page Not Found</div>} />
    </Routes>
  );
};

export default App;