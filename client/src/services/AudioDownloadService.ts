interface AudioDownloadProgress {
  status: 'idle' | 'fetching-url' | 'downloading' | 'processing' | 'completed' | 'error';
  progress: number; // 0-100
  bytesLoaded: number;
  bytesTotal: number;
  error?: string;
  fileName?: string;
  url?: string;
}

interface AudioDownloadResult {
  audioBuffer: ArrayBuffer;
  audioBlob: Blob;
  audioUrl: string; // Object URL for playback
  originalUrl: string;
  fileName: string;
  fileSize: number;
}

class AudioDownloadService {
  private progressCallbacks: Map<string, (progress: AudioDownloadProgress) => void> = new Map();
  private downloadCache: Map<string, AudioDownloadResult> = new Map();
  private activeDownloads: Map<string, Promise<AudioDownloadResult>> = new Map();

  // Đăng ký callback để nhận updates về tiến độ
  onProgress(downloadId: string, callback: (progress: AudioDownloadProgress) => void) {
    this.progressCallbacks.set(downloadId, callback);
  }

  // Hủy đăng ký callback
  offProgress(downloadId: string) {
    this.progressCallbacks.delete(downloadId);
  }

  // Emit progress update
  private emitProgress(downloadId: string, progress: AudioDownloadProgress) {
    const callback = this.progressCallbacks.get(downloadId);
    if (callback) {
      callback(progress);
    }
  }

  // Tải audio file hoàn toàn giống AudioTimelineRecorder
  async downloadAudio(originalUrl: string, downloadId: string): Promise<AudioDownloadResult> {
    // Kiểm tra cache trước
    if (this.downloadCache.has(originalUrl)) {
      console.log('[AUDIO DOWNLOAD] Using cached audio for:', originalUrl);
      const cached = this.downloadCache.get(originalUrl)!;
      this.emitProgress(downloadId, {
        status: 'completed',
        progress: 100,
        bytesLoaded: cached.fileSize,
        bytesTotal: cached.fileSize,
        fileName: cached.fileName,
        url: cached.audioUrl
      });
      return cached;
    }

    // Kiểm tra nếu đang download
    if (this.activeDownloads.has(originalUrl)) {
      console.log('[AUDIO DOWNLOAD] Download already in progress for:', originalUrl);
      return this.activeDownloads.get(originalUrl)!;
    }

    // Bắt đầu download mới
    const downloadPromise = this.performDownload(originalUrl, downloadId);
    this.activeDownloads.set(originalUrl, downloadPromise);

    try {
      const result = await downloadPromise;
      this.downloadCache.set(originalUrl, result);
      return result;
    } finally {
      this.activeDownloads.delete(originalUrl);
    }
  }

  private async performDownload(originalUrl: string, downloadId: string): Promise<AudioDownloadResult> {
    try {
      console.log('[AUDIO DOWNLOAD] Starting download for:', originalUrl);
      
      this.emitProgress(downloadId, {
        status: 'fetching-url',
        progress: 0,
        bytesLoaded: 0,
        bytesTotal: 0,
        fileName: 'Getting URL...'
      });

      // Import utils để lấy playable URL
      const utils = await import('../lib/utils');
      const getPlayableAudioUrl = utils.getPlayableAudioUrl;
      const extractGoogleDriveFileId = utils.extractGoogleDriveFileId;

      let playableUrl: string;
      let fileName = 'audio.mp3';

      try {
        // Thử lấy direct URL trước
        playableUrl = await getPlayableAudioUrl(originalUrl);
        console.log('[AUDIO DOWNLOAD] Got playable URL:', playableUrl);
        
        // Lấy tên file từ URL
        fileName = playableUrl.split('/').pop()?.split('?')[0] || 'audio.mp3';
        
      } catch (urlError) {
        console.error('[AUDIO DOWNLOAD] Error getting direct URL, trying proxy:', urlError);
        
        // Fallback to proxy URL
        const fileId = extractGoogleDriveFileId(originalUrl);
        if (fileId) {
          playableUrl = `/api/proxy/gdrive?id=${fileId}`;
          fileName = `gdrive_${fileId}.mp3`;
          console.log('[AUDIO DOWNLOAD] Using proxy URL:', playableUrl);
        } else {
          throw new Error('Cannot extract file ID for proxy');
        }
      }

      this.emitProgress(downloadId, {
        status: 'downloading',
        progress: 5,
        bytesLoaded: 0,
        bytesTotal: 0,
        fileName,
        url: playableUrl
      });

      // Tải file với progress tracking
      const response = await fetch(playableUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentLength = response.headers.get('content-length');
      const totalBytes = contentLength ? parseInt(contentLength) : 0;
      
      console.log('[AUDIO DOWNLOAD] Starting download, size:', totalBytes, 'bytes');

      this.emitProgress(downloadId, {
        status: 'downloading',
        progress: 10,
        bytesLoaded: 0,
        bytesTotal: totalBytes,
        fileName,
        url: playableUrl
      });

      // Đọc response với progress tracking
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Cannot get response reader');
      }

      const chunks: Uint8Array[] = [];
      let bytesLoaded = 0;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        chunks.push(value);
        bytesLoaded += value.length;
        
        const progress = totalBytes > 0 ? 10 + (bytesLoaded / totalBytes) * 80 : 50;
        
        this.emitProgress(downloadId, {
          status: 'downloading',
          progress,
          bytesLoaded,
          bytesTotal: totalBytes || bytesLoaded,
          fileName,
          url: playableUrl
        });
      }

      this.emitProgress(downloadId, {
        status: 'processing',
        progress: 95,
        bytesLoaded,
        bytesTotal: bytesLoaded,
        fileName,
        url: playableUrl
      });

      // Tạo ArrayBuffer và Blob
      const audioBuffer = new Uint8Array(bytesLoaded);
      let offset = 0;
      for (const chunk of chunks) {
        audioBuffer.set(chunk, offset);
        offset += chunk.length;
      }

      const audioBlob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const audioUrl = URL.createObjectURL(audioBlob);

      const result: AudioDownloadResult = {
        audioBuffer: audioBuffer.buffer,
        audioBlob,
        audioUrl,
        originalUrl,
        fileName,
        fileSize: bytesLoaded
      };

      console.log('[AUDIO DOWNLOAD] Download completed:', {
        fileName,
        fileSize: bytesLoaded,
        audioUrl
      });

      this.emitProgress(downloadId, {
        status: 'completed',
        progress: 100,
        bytesLoaded,
        bytesTotal: bytesLoaded,
        fileName,
        url: audioUrl
      });

      return result;

    } catch (error) {
      console.error('[AUDIO DOWNLOAD] Download failed:', error);
      
      this.emitProgress(downloadId, {
        status: 'error',
        progress: 0,
        bytesLoaded: 0,
        bytesTotal: 0,
        error: error.message || 'Download failed'
      });

      throw error;
    }
  }

  // Cleanup downloaded audio URLs
  cleanup(originalUrl: string) {
    const cached = this.downloadCache.get(originalUrl);
    if (cached) {
      URL.revokeObjectURL(cached.audioUrl);
      this.downloadCache.delete(originalUrl);
      console.log('[AUDIO DOWNLOAD] Cleaned up cached audio for:', originalUrl);
    }
  }

  // Get cached audio if available
  getCached(originalUrl: string): AudioDownloadResult | null {
    return this.downloadCache.get(originalUrl) || null;
  }

  // Check if download is in progress
  isDownloading(originalUrl: string): boolean {
    return this.activeDownloads.has(originalUrl);
  }
}

// Export singleton instance
export const audioDownloadService = new AudioDownloadService();
export type { AudioDownloadProgress, AudioDownloadResult };
