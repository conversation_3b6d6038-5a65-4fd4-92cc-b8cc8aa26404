/* Quick access buttons in login page */
.quick-access {
  margin-top: 2em;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 10px;
}

.start-exam-button {
  background-color: #023b96;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: background-color 0.2s;
  width: 100%;
}

.start-exam-button:hover:not([disabled]) {
  background-color: #012b6e;
}

.start-exam-button[disabled] {
  background-color: #6995d0;
  cursor: not-allowed;
}

.admin-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  padding: 12px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s;
  color: #444;
  width: 100%;
}

.admin-button:hover {
  background-color: #e0e0e0;
  border-color: #ccc;
}