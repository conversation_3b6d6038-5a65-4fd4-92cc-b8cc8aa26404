/* User Dashboard Styles */
.user-dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.user-dashboard-container .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #023b96;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-dashboard-container .logo {
  display: flex;
  flex-direction: column;
}

.user-dashboard-container .logo h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
}

.user-dashboard-container .logo span {
  font-size: 0.9rem;
  opacity: 0.8;
}

.user-dashboard-container .user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-dashboard-container .user-name {
  font-weight: 500;
}

.user-dashboard-container .logout-button {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-dashboard-container .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.user-dashboard-container .dashboard-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.user-dashboard-container .sidebar {
  width: 240px;
  background-color: white;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
  padding-top: 1.5rem;
}

.user-dashboard-container .sidebar nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-dashboard-container .sidebar nav li {
  margin-bottom: 0.5rem;
}

.user-dashboard-container .sidebar nav button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #495057;
}

.user-dashboard-container .sidebar nav button:hover {
  background-color: #f0f3f7;
}

.user-dashboard-container .sidebar nav li.active button {
  background-color: #e9f0fb;
  color: #0052cc;
  border-left: 3px solid #0052cc;
}

.user-dashboard-container .sidebar .icon {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.user-dashboard-container .content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.user-dashboard-container .welcome-message {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-dashboard-container .welcome-message h2 {
  margin-top: 0;
  color: #212529;
}

.user-dashboard-container .welcome-message p {
  color: #6c757d;
  margin-bottom: 0;
}

.user-dashboard-container .dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.user-dashboard-container .stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-dashboard-container .stat-card h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #6c757d;
  font-weight: 500;
}

.user-dashboard-container .stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #212529;
}

.user-dashboard-container .recent-exams h3,
.user-dashboard-container .exams-tab h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #212529;
}