/* Test End Screen styles */
.test-end-container {
  background-color: white;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.test-end-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #c9d6e8;
  padding: 10px 20px;
  border-bottom: 1px solid #aaa;
}

.test-end-title h1 {
  margin: 0;
  color: #333;
  font-size: 22px;
}

.test-end-title p {
  margin: 0;
  color: #555;
  font-size: 14px;
}

.test-end-info {
  display: flex;
  color: #333;
}

.application-info, .seat-info {
  background-color: #eee;
  padding: 3px 8px;
  border-radius: 3px;
  margin-left: 10px;
  font-size: 12px;
}

.info-value {
  font-weight: bold;
}

.test-end-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
}

/* <PERSON><PERSON><PERSON> khung bên trong mô phỏng hình ảnh trong attached_assets/test end.png */
.test-end-image-frame {
  width: 580px; /* <PERSON><PERSON>ng kích thước chiều rộng */
  height: 400px; /* T<PERSON>ng kích thước chiều cao */
  background-color: white;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.test-end-blue-header {
  background-color: #5b9bd5;
  color: white;
  padding: 8px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.test-end-white-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 15px;
  font-size: 0.85em;
}

.papers-icon {
  width: 250px; /* Tăng kích thước chiều rộng */
  height: 120px; /* Tăng kích thước chiều cao */
  background-color: white;
  border: 2px solid #a6c2e0;
  border-radius: 5px;
  position: relative;
  margin: 10px 0;
  /* Styling to match the papers icon in the image */
  background-image: linear-gradient(to bottom, #a6c2e0, #a6c2e0);
  background-size: 250px 20px;
  background-repeat: no-repeat;
}

/* Styling for the hanging straps */
.papers-icon::before,
.papers-icon::after {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  background-color: white;
  border: 2px solid #a6c2e0;
  border-radius: 50%;
  top: -10px;
}

.papers-icon::before {
  left: 40%;
}

.papers-icon::after {
  right: 40%;
}

.test-end-instructions {
  width: 100%;
  margin-top: 20px;
}

.instruction-item {
  margin: 6px 0;
  color: #333;
  font-size: 0.9em;
  text-align: left;
}

.test-end-actions {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.close-button {
  padding: 5px 30px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #ff7f27; /* Màu cam khi hover */
}