/* Common button styles for EPS application */
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 4px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.icon-button:hover:not([disabled]) {
  background-color: #ff7f27; /* Màu cam khi hover */
  border-color: #ff7f27;
}

.icon-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon-button.primary {
  background-color: #023b96;
  color: white;
  border-color: #023b96;
}

.icon-button.primary:hover:not([disabled]) {
  background-color: #ff7f27;
  border-color: #ff7f27;
}

.icon-button.success {
  background-color: #34A853;
  color: white;
  border-color: #34A853;
}

.icon-button.success:hover:not([disabled]) {
  background-color: #ff7f27;
  border-color: #ff7f27;
}

.icon-button.danger {
  background-color: #EA4335;
  color: white;
  border-color: #EA4335;
}

.icon-button.danger:hover:not([disabled]) {
  background-color: #ff7f27;
  border-color: #ff7f27;
}

.icon-button.warning {
  background-color: #FBBC05;
  color: white;
  border-color: #FBBC05;
}

.icon-button.warning:hover:not([disabled]) {
  background-color: #ff7f27;
  border-color: #ff7f27;
}

/* Tooltip styling */
.icon-button .tooltip {
  visibility: hidden;
  width: max-content;
  max-width: 200px;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.2s;
  font-size: 0.75rem;
  font-weight: normal;
  white-space: nowrap;
}

.icon-button:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

/* Size variants */
.icon-button.small {
  padding: 4px;
  font-size: 0.8rem;
}

.icon-button.large {
  padding: 12px;
  font-size: 1.2rem;
}

/* Fixed width buttons */
.icon-button.fixed {
  width: 40px;
  height: 40px;
}

.icon-button.small.fixed {
  width: 30px;
  height: 30px;
}

.icon-button.large.fixed {
  width: 50px;
  height: 50px;
}

/* Icon + text button */
.button-with-text {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.2s;
}

.button-with-text:hover:not([disabled]) {
  background-color: #ff7f27; /* Màu cam khi hover */
  border-color: #ff7f27;
}

.button-with-text[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-with-text.primary {
  background-color: #023b96;
  color: white;
  border-color: #023b96;
}

.button-with-text.primary:hover:not([disabled]) {
  background-color: #ff7f27; /* Màu cam khi hover */
  border-color: #ff7f27;
}