// Màn hình Information check of applicant đã cắt phần button và khung
import React from 'react';
import '../start-screens.css';

interface InfoScreenProps {
  applicationNo: string;
  seatNo: string;
}

const InformationScreen: React.FC<InfoScreenProps> = ({ applicationNo, seatNo }) => {
  return (
    <div className="start-screen-content-wrapper">
      <div className="blue-header">Information check of applicant</div>
      <div className="dark-header">
        <span className="audio-icon">🔊</span>
        <span>Check your application and if there is no problem, click the [Confirm] button.</span>
      </div>
      <div className="content-area">
        <div className="applicant-info">
          <div className="seat-info">
            <h3>Seat Number</h3>
            <div className="big-number">7</div>
          </div>
          <div className="person-info">
            <div className="photo-placeholder"></div>
            <div className="info-rows">
              <div className="info-row">
                <div className="label">SEAT NO.</div>
                <div className="value">7</div>
              </div>
              <div className="info-row">
                <div className="label">TEST VENUE</div>
                <div className="value">Test Venue</div>
              </div>
              <div className="info-row">
                <div className="label">TEST ROOM</div>
                <div className="value">Test Room</div>
              </div>
              <div className="info-row">
                <div className="label">APPLICATION NO.</div>
                <div className="value">{applicationNo}</div>
              </div>
              <div className="info-row">
                <div className="label">NAME</div>
                <div className="value">HONG GIL DONG</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InformationScreen;