// Màn hình Practice Test đã cắt phần button và khung
import React from 'react';
import '../start-screens.css';

const PracticeScreen: React.FC = () => {
  return (
    <div className="start-screen-content-wrapper">
      <div className="blue-header">Practice Test of Proficiency in Korean(CBT)</div>
      <div className="dark-header">
        <span className="audio-icon">🔊</span>
        <span>After clicking [Practice Test] button, proceed practice test and if there is nothing wrong, click [Ready] button.</span>
      </div>
      <div className="content-area">
        <div className="practice-instructions">
          <div className="instruction-item">
            <div className="instruction-number">1</div>
            <div className="instruction-text">
              Click <span className="practice-btn">Practice Test</span> and proceed practice test.
              <div className="instruction-note">(If you don't proceed practice test, you can't take the test.)</div>
            </div>
          </div>
          
          <div className="instruction-item">
            <div className="instruction-number">2</div>
            <div className="instruction-text">
              If there is any problem in practice test, ask supervisor about it.
            </div>
          </div>
          
          <div className="instruction-item">
            <div className="instruction-number">3</div>
            <div className="instruction-text">
              After clicking <span className="ready-btn">Ready</span> button, please wait until supervisor give direction.
              <div className="instruction-note">(You can take a test after clicking [Ready].)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PracticeScreen;