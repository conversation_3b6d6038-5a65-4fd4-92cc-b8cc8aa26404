// <PERSON><PERSON>n hình Notice of applicant đã cắt phần button và khung
import React from 'react';
import '../start-screens.css';

const NoticeScreen: React.FC = () => {
  return (
    <div className="start-screen-content-wrapper">
      <div className="blue-header">Notice of applicant</div>
      <div className="dark-header">
        <span className="audio-icon">🔊</span>
        <span>After being fully aware of applicant notice below, click the [Confirm] button.</span>
      </div>
      <div className="content-area">
        <div className="notice-list">
          <div className="notice-item">
            <div className="notice-number">1</div>
            <div className="notice-text">You should organize all your belongings below your desk except test identification, ID card(passport).</div>
          </div>
          
          <div className="notice-item">
            <div className="notice-number">2</div>
            <div className="notice-text">
              <strong>Electronic device such as cell phone, camera etc. is not allowed to possess and use.</strong>
              <div>Please hand in to supervisor.</div>
            </div>
          </div>
          
          <div className="notice-item">
            <div className="notice-number">3</div>
            <div className="notice-text">
              <strong>If there is a technical problem</strong> of computer during the test, please raise your hand quietly without making any noise. If alarm arise, you can move other PC and keep your test following the procedure.
            </div>
          </div>
          
          <div className="notice-item">
            <div className="notice-number">4</div>
            <div className="notice-text">
              <strong>This test will be proceeded for 70minutes without break.</strong> It has all 50 questions and reading test is from 1 to 25, listening test is from 26 to 50. Listening test will be played two times.
            </div>
          </div>
          
          <div className="notice-item">
            <div className="notice-number">5</div>
            <div className="notice-text">
              <strong>Once you choose an answer, you can't change the answer.</strong> Please mark the answer carefully.
            </div>
          </div>
          
          <div className="notice-item">
            <div className="notice-number">6</div>
            <div className="notice-text">
              <strong>In case of cheating,</strong> test will be void, and examinees will NOT be eligible for taking the EPS-TOPIK for 2 years.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoticeScreen;