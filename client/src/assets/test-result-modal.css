/* Test Result Modal Styles */
.test-result-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.test-result-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

.test-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.test-result-header h2 {
  margin: 0;
  color: #00468C;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  line-height: 1;
}

.close-button:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.test-result-summary {
  background-color: #f8fafc;
  padding: 1.25rem 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-item .label {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.summary-item .value {
  font-weight: 600;
  color: #334155;
}

.summary-item .score,
.summary-item .percentage {
  font-size: 1.25rem;
  color: #00468C;
}

.test-result-details {
  padding: 1.5rem;
}

.test-result-details h3 {
  margin-top: 0;
  margin-bottom: 1.25rem;
  color: #334155;
  font-weight: 600;
}

.question-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.question-result-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.question-number {
  font-weight: 600;
  color: #334155;
}

.result-indicator {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

.result-indicator.correct {
  background-color: #dcfce7;
  color: #166534;
}

.result-indicator.incorrect {
  background-color: #fee2e2;
  color: #b91c1c;
}

.question-content {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.question-text {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #334155;
}

.question-subtext {
  margin: 0;
  color: #64748b;
  white-space: pre-line;
}

.answer-options {
  padding: 1rem;
}

.answer-option {
  display: flex;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  position: relative;
}

.answer-option.user-choice {
  background-color: #eff6ff;
  border: 1px solid #dbeafe;
}

.answer-option.correct-answer {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
}

.answer-option.user-choice.correct-answer {
  background-color: #dcfce7;
  border: 1px solid #86efac;
}

.option-marker {
  margin-right: 1rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #e2e8f0;
  background-color: white;
  flex-shrink: 0;
}

.answer-option.user-choice .option-marker {
  border-color: #00468C;
  background-color: #00468C;
  color: white;
}

.answer-option.correct-answer .option-marker {
  border-color: #22c55e;
}

.user-marker, .correct-marker {
  font-size: 0.875rem;
}

.option-content {
  flex: 1;
}

.option-text {
  color: #334155;
}

.option-image {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .test-result-summary {
    grid-template-columns: 1fr;
  }
  
  .test-result-modal {
    width: 95%;
    max-height: 95vh;
  }
}