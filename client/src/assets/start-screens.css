/* Styles for Start Screens */

.start-screens-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.start-screen-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1024px;
}

.start-screen-content-wrapper {
  width: 100%;
  max-width: 800px; /* Tăng kích thước chiều rộng */
  height: 550px; /* Tăng kích thước chiều cao */
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.blue-header {
  background-color: #5b9bd5;
  color: white;
  padding: 10px 15px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.dark-header {
  background-color: #4a4a4a;
  color: white;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.audio-icon {
  margin-right: 10px;
}

.content-area {
  padding: 12px;
  background-color: #f9f9f9;
  font-size: 0.85em; /* Giảm kích thước font chữ để vừa với khung nhỏ hơn */
}

/* Information Screen Styles */
.applicant-info {
  display: flex;
}

.seat-info {
  width: 120px;
  text-align: center;
}

.big-number {
  font-size: 100px;
  font-weight: bold;
  line-height: 1;
}

.person-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.photo-placeholder {
  width: 80px;
  height: 100px;
  background-color: #e0e0e0;
  border: 1px solid #ccc;
  margin-bottom: 15px;
}

.info-rows {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-row {
  display: flex;
  align-items: center;
}

.label {
  width: 150px;
  background-color: #5b9bd5;
  color: white;
  padding: 5px 10px;
  text-align: center;
  font-size: 12px;
  margin-right: 10px;
}

.value {
  flex: 1;
  border-bottom: 1px solid #ddd;
}

/* Notice Screen Styles */
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
}

.notice-number {
  width: 24px;
  height: 24px;
  background-color: #5b9bd5;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
}

.notice-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.notice-text strong {
  color: #4a8bc0;
}

/* Practice Screen Styles */
.practice-instructions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
}

.instruction-number {
  width: 24px;
  height: 24px;
  background-color: #4a4a4a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
}

.instruction-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.practice-btn, .ready-btn {
  display: inline-block;
  background-color: #5b9bd5;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 12px;
}

.instruction-note {
  font-size: 12px;
  color: #777;
  font-style: italic;
  margin-top: 5px;
}

.start-screen-footer {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.confirm-button, .ready-button {
  background-color: #5b9bd5;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 8px 30px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.confirm-button:hover, .ready-button:hover {
  background-color: #ff7f27; /* Màu cam khi hover như hình */
}

.ready-button {
  background-color: #5b9bd5;
}