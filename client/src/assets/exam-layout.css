/* New modern exam layout styling */
.exam-view-container {
  display: grid;
  grid-template-columns: 240px 1fr;
  grid-template-rows: 60px 1fr;
  grid-template-areas:
    "header header"
    "sidebar main";
  height: 100vh;
  background-color: #f8f9fa;
  overflow: hidden;
}

/* Header styles */
.exam-header {
  grid-area: header;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.exam-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.exam-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.exam-info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.info-label {
  font-size: 12px;
  color: #666;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* Sidebar styles */
.exam-sidebar {
  grid-area: sidebar;
  background-color: #fff;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.timer-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f7ff;
  border-radius: 8px;
}

.timer-title {
  font-size: 14px;
  color: #0056b3;
  margin-bottom: 10px;
  font-weight: 500;
}

.timer-display {
  font-size: 24px;
  font-weight: 700;
  color: #0056b3;
  text-align: center;
}

.question-navigation {
  flex: 1;
  overflow-y: auto;
}

.nav-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 500;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.question-button {
  width: 35px;
  height: 35px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.question-button:hover {
  background-color: #f0f7ff;
  border-color: #0056b3;
}

.question-button.active {
  background-color: #0056b3;
  color: #fff;
  border-color: #0056b3;
}

.question-button.answered {
  background-color: #e8f5e9;
  border-color: #4caf50;
  color: #4caf50;
}

.question-button.active.answered {
  background-color: #4caf50;
  color: #fff;
  border-color: #4caf50;
}

.question-section-divider {
  margin: 15px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.exam-controls {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.font-controls {
  display: flex;
  gap: 8px;
}

.font-button {
  width: 35px;
  height: 35px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.font-button:hover {
  background-color: #f0f7ff;
  border-color: #0056b3;
}

.submit-button {
  padding: 10px 20px;
  background-color: #0056b3;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.submit-button:hover {
  background-color: #003d82;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Main content area */
.main-content {
  grid-area: main;
  padding: 20px;
  overflow-y: auto;
}

.question-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.question-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.question-type {
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 20px;
  background-color: #f0f7ff;
  color: #0056b3;
}

.question-type.listening {
  background-color: #fff3e0;
  color: #e65100;
}

.question-content {
  margin-bottom: 20px;
}

.question-text {
  font-size: 16px;
  margin-bottom: 10px;
  line-height: 1.5;
}

.question-subtext {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.4;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option {
  display: flex;
  align-items: flex-start;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.option:hover {
  background-color: #f9f9f9;
  border-color: #bbb;
}

.option.selected {
  background-color: #e8f5e9;
  border-color: #4caf50;
}

.option-number {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-right: 10px;
  min-width: 20px;
}

.option-text {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.option-image {
  max-width: 100%;
  margin-top: 10px;
  border-radius: 4px;
}

/* Audio player styling */
.audio-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f7ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-icon {
  font-size: 24px;
  color: #0056b3;
}

.audio-controls {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-play-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #0056b3;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.audio-progress {
  flex: 1;
  height: 6px;
  background-color: #ddd;
  border-radius: 3px;
  position: relative;
}

.audio-progress-bar {
  height: 100%;
  background-color: #0056b3;
  border-radius: 3px;
}

.audio-time {
  font-size: 12px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .exam-view-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    grid-template-areas:
      "header"
      "sidebar"
      "main";
  }
  
  .exam-sidebar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  .question-grid {
    grid-template-columns: repeat(10, 1fr);
  }
}
