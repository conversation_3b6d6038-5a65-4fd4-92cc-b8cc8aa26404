/* Styles cho màn hình kết quả kiểm tra - bố cục 2 grid */
.score-screen {
  background: #f7f9fa;
  min-height: 100vh;
  padding: 32px 0;
}

.score-container {
  max-width: 1100px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 32px 24px 32px;
}

.score-container h2 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  text-align: center;
}

.two-grid-layout {
  display: flex;
  gap: 32px;
  margin-top: 24px;
  align-items: flex-start;
}

/* Grid 1: Thông tin kết quả */
.result-column {
  flex: 0 0 320px;
  background: #f5f8fa;
  border-radius: 8px;
  padding: 24px 20px 20px 20px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  border: 1px solid #e0e6ed;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box;
}

.score-display {
  text-align: center;
  margin-bottom: 18px;
}

.score-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2196f3;
}

.score-label {
  color: #888;
  font-size: 1.1rem;
  margin-top: 2px;
}

.score-details {
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 1rem;
}

.detail-label {
  color: #444;
}

.detail-value {
  font-weight: 500;
}

/* Grid 2: Danh sách đáp án */
.answers-column {
  flex: 1 1 0;
  background: #fafdff;
  border-radius: 8px;
  padding: 24px 8px 20px 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.02);
  border: 1px solid #e0e6ed;
  min-width: 0;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box;
  overflow-x: hidden;
}

.column-header {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 18px;
  color: #1a355b;
}

.preview-question {
  background: #fff;
  border: 1px solid #e0e6ed;
  border-radius: 12px;
  padding: 22px 18px 18px 18px;
  margin-bottom: 22px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.07), 0 1.5px 4px rgba(0,0,0,0.03);
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box;
  display: block;
}

.question-number {
  font-weight: bold;
  color: #1a355b;
  margin-bottom: 8px;
  font-size: 1.08rem;
}

.question-text {
  font-size: 1.12rem;
  color: #222;
  margin: 0 0 10px 0;
  line-height: 1.7;
  text-align: left !important;
  word-break: break-word;
  white-space: pre-line;
  width: 100%;
  max-width: 100% !important;
  box-sizing: border-box;
  overflow-wrap: anywhere;
}

.question-subtext {
  font-size: 1rem;
  color: #666;
  margin-top: 4px;
  font-style: italic;
  width: 100%;
  box-sizing: border-box;
  text-align: left !important;
}

.preview-question .question-subtext {
  text-align: left !important;
  width: 100% !important;
  display: block;
  margin-left: 0;
}

.answer-status {
  display: flex;
  align-items: center;
  margin: 8px 0 10px 0;
  padding: 5px 0 5px 0;
  background: none;
  border-radius: 4px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.status-correct {
  background-color: #4caf50;
}

.status-incorrect {
  background-color: #f44336;
}

.status-not-answered {
  background-color: #ffb300;
}

.status-text {
  font-size: 1rem;
  font-weight: 500;
}

.options-container {
  margin-top: 6px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  box-sizing: border-box;
}

.answer-option {
  display: flex;
  align-items: center;
  padding: 4px 0 4px 0;
  font-size: 1rem;
  border-radius: 4px;
  background: none;
}

.answer-option.user-choice {
  background: #e3f2fd;
}

.answer-option.correct-option {
  font-weight: bold;
  color: #388e3c;
}

.option-number {
  font-weight: 500;
  margin-right: 7px;
  color: #888;
}

.option-text {
  display: flex;
  align-items: center;
  gap: 6px;
  word-break: break-word;
  white-space: pre-line;
  width: 100%;
  box-sizing: border-box;
  overflow-wrap: anywhere;
}

.correct-mark {
  color: #43a047;
  font-size: 1.1em;
  margin-left: 4px;
}

.user-mark {
  color: #e53935;
  font-size: 1.1em;
  margin-left: 4px;
}

.option-image {
  max-width: 120px;
  max-height: 60px;
  margin-left: 8px;
  border-radius: 4px;
  border: 1px solid #e0e6ed;
}

.return-button {
  margin: 32px auto 0 auto;
  display: block;
  background: #2196f3;
  color: #fff;
  font-weight: bold;
  border: none;
  border-radius: 6px;
  padding: 12px 32px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.return-button:hover {
  background: #1769aa;
}

/* Responsive */
@media (max-width: 900px) {
  .two-grid-layout {
    flex-direction: column;
    gap: 18px;
  }
  .result-column, .answers-column {
    padding: 14px 2vw 10px 2vw;
    box-sizing: border-box;
  }
  .preview-question {
    padding: 14px 2vw 10px 2vw;
    margin-bottom: 14px;
    border-radius: 8px;
    font-size: 0.98rem;
    width: 100% !important;
    max-width: none !important;
    box-sizing: border-box;
  }
  .question-text {
    font-size: 1rem;
    line-height: 1.5;
    padding: 0;
    width: 100%;
    box-sizing: border-box;
  }
  .question-subtext {
    font-size: 0.92rem;
    width: 100%;
    box-sizing: border-box;
  }
}

@media (max-width: 600px) {
  .score-container {
    padding: 8px 0 8px 0;
    box-sizing: border-box;
  }
  .preview-question {
    padding: 10px 1vw 8px 1vw;
    margin-bottom: 10px;
    border-radius: 6px;
    font-size: 0.92rem;
    width: 100% !important;
    max-width: none !important;
    box-sizing: border-box;
  }
  .question-text {
    font-size: 0.98rem;
    line-height: 1.4;
    width: 100%;
    box-sizing: border-box;
  }
  .question-subtext {
    font-size: 0.88rem;
    width: 100%;
    box-sizing: border-box;
  }
}