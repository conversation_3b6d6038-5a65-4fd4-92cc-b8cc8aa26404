/* Submit Button */
.submit-button-container {
  margin-top: 20px;
}

.submit-button {
  background-color: #666666;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: not-allowed;
  width: 100%;
  font-size: 16px;
  transition: background-color 0.3s;
}

.submit-active {
  background-color: #4CAF50;
  cursor: pointer;
}

.submit-active:hover {
  background-color: #ff7f27; /* <PERSON>àu cam khi hover như hình */
}

.submit-inactive {
  background-color: #cccccc;
}

/* Confirmation Dialog */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirmation-dialog {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  width: 400px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.confirmation-dialog h3 {
  margin-top: 0;
  color: #333;
}

.confirmation-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.confirm-button, .cancel-button {
  padding: 8px 20px;
  margin-left: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-button {
  background-color: #4CAF50;
  color: white;
}

.cancel-button {
  background-color: #f44336;
  color: white;
}

/* Test Shutdown Dialog (Try testing has been shut down) */
.test-shutdown-dialog {
  border-radius: 10px;
  width: 320px;
  padding: 15px;
  text-align: center;
}

.test-shutdown-dialog p {
  font-size: 18px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 15px;
}

.test-shutdown-buttons {
  display: flex;
  justify-content: flex-end;
}

.test-shutdown-buttons button {
  padding: 8px 20px;
  border: none;
  background: none;
  color: #007bff;
  font-size: 16px;
  cursor: pointer;
  font-weight: bold;
}

/* Test End Screen (Final screen with Thank you message) */
.test-end-screen {
  background-color: white;
  width: 800px;
  height: 600px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.test-end-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #c9d6e8;
  padding: 10px 20px;
  border-bottom: 1px solid #aaa;
}

.test-end-title h1 {
  margin: 0;
  color: #333;
  font-size: 22px;
}

.test-end-title p {
  margin: 0;
  color: #555;
  font-size: 14px;
}

.test-end-info {
  display: flex;
  color: #333;
}

.application-info, .seat-info {
  background-color: #eee;
  padding: 3px 8px;
  border-radius: 3px;
  margin-left: 10px;
  font-size: 12px;
}

.info-value {
  font-weight: bold;
}

.test-end-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
}

.thank-you-message {
  background-color: #70a1d9;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  width: 100%;
  text-align: center;
  margin-bottom: 20px;
}

.test-end-image {
  background-color: #ccc;
  width: 300px;
  height: 150px;
  border: 5px solid #a6c2e0;
  border-radius: 8px;
  margin-bottom: 20px;
  position: relative;
}

.test-end-instructions {
  text-align: left;
  width: 100%;
  padding: 10px 20px;
}

.test-end-instructions p {
  margin: 10px 0;
  color: #333;
}

.test-end-actions {
  margin-top: auto;
  align-self: flex-end;
}

.test-end-actions button {
  padding: 5px 15px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;
}

/* Not Finished Dialog */
.not-finished-dialog {
  padding: 15px;
  width: 300px;
}

.not-finished-buttons {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.done-button {
  padding: 5px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}