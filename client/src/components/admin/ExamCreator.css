/* ExamCreator styles */
.question-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.question-editor-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 0;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  border: 2px solid #023b96;
}

.question-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px;
  background-color: #023b96;
  color: white;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.question-editor-header h2 {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Don't need this anymore as we're using icon-button */
.close-editor-button {
  display: none;
}

.question-editor-content {
  padding: 24px;
  background-color: #fafafa;
}

/* Style for form inputs and elements */
.disabled-input {
  background-color: #f0f0f0;
  color: #777;
  cursor: not-allowed;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  margin-bottom: 16px;
}

.form-group.full-width {
  flex: 0 0 100%;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #023b96;
  box-shadow: 0 0 0 1px rgba(2, 59, 150, 0.2);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.option-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.option-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.correct-option-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.correct-label {
  color: #2e7d32;
  font-weight: 500;
  margin-left: 8px;
}

.option-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.option-item:hover {
  border-color: #023b96;
  box-shadow: 0 2px 8px rgba(2, 59, 150, 0.15);
}

.option-content {
  margin-top: 10px;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Using button-with-text primary instead */
.save-question-button {
  display: none;
}

.image-preview {
  margin-top: 10px;
  border: 1px solid #ddd;
  padding: 8px;
  border-radius: 4px;
  max-width: 300px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview img {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 2px;
  display: block;
}

.audio-info {
  margin-top: 4px;
  color: #4a148c;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
}

.audio-icon {
  margin-right: 4px;
  font-size: 1rem;
}

.audio-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 4px;
}

.audio-start-time {
  background-color: rgba(0, 128, 0, 0.15);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #006600;
  display: inline-block;
}

.audio-duration {
  background-color: rgba(74, 158, 255, 0.15);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #0066cc;
  display: inline-block;
}

.audio-duration-missing {
  background-color: rgba(255, 152, 0, 0.15);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #ff6600;
  display: inline-block;
}

.total-audio-duration {
  font-size: 0.8rem;
  margin-left: 10px;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-weight: normal;
}

.form-hint {
  font-size: 0.8rem;
  color: #666;
  margin-top: 4px;
  font-style: italic;
  display: flex;
  align-items: center;
}

.input-with-hint {
  width: 100%;
}

.disabled-input {
  background-color: #f5f5f5;
  color: #666;
  cursor: not-allowed;
  border: 1px solid #ddd;
}

.audio-duration-status {
  margin-top: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #023b96;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: rgba(2, 59, 150, 0.05);
  border-left: 3px solid #023b96;
}

/* Styling for disabled buttons */
.icon-button.disabled {
  color: #aaa;
  cursor: not-allowed;
}

.icon-button.disabled:hover {
  background-color: transparent;
}

/* Responsive styles */
@media (max-width: 768px) {
  .question-editor-modal {
    width: 95%;
    max-height: 95vh;
  }

  .question-editor-header h2 {
    font-size: 1.2rem;
  }
}

.spinner-audio {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}