import React, { useState, useRef, useEffect } from 'react';
import { FiPlay, FiPause, FiRefreshCw } from 'react-icons/fi';

interface AudioDurationRecorderProps {
  audioUrl: string;
  onDurationsRecorded: (durations: number[]) => void;
  onClose: () => void;
}

const AudioDurationRecorder: React.FC<AudioDurationRecorderProps> = ({
  audioUrl,
  onDurationsRecorded,
  onClose
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentDuration, setCurrentDuration] = useState(0);
  const [recordedDurations, setRecordedDurations] = useState<number[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const startTimeRef = useRef<number>(0);
  const lastSpacePressRef = useRef<number>(0);
  const seekBarRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [totalDuration, setTotalDuration] = useState(0);

  // Handle space key press
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === 'Space' && isPlaying) {
        e.preventDefault(); // Prevent page scroll

        const now = Date.now();
        // Prevent multiple space presses within 500ms
        if (now - lastSpacePressRef.current < 500) return;
        lastSpacePressRef.current = now;

        const duration = Math.round((now - startTimeRef.current) / 1000);
        setRecordedDurations(prev => [...prev, duration]);
        setCurrentQuestionIndex(prev => prev + 1);
        setCurrentDuration(0);
        startTimeRef.current = now;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isPlaying]);

  // Khai báo state để lưu hàm getPlayableAudioUrl
  const [getPlayableAudioUrl, setGetPlayableAudioUrl] = useState<any>(null);

  // Import utility function
  useEffect(() => {
    const importUtils = async () => {
      try {
        const utils = await import('../../lib/utils');
        setGetPlayableAudioUrl(() => utils.getPlayableAudioUrl);
      } catch (error) {
        console.error('Error importing utils:', error);
        setError('Không thể tải các tiện ích cần thiết. Vui lòng tải lại trang.');
      }
    };

    importUtils();
  }, []);

  // Initialize audio element
  useEffect(() => {
    if (!getPlayableAudioUrl) return; // Chờ cho đến khi hàm được import

    const initAudio = async () => {
      if (audioRef.current && audioUrl) {
        try {
          const playableUrl = await getPlayableAudioUrl(audioUrl);
          console.log('Initializing audio with URL:', playableUrl);
          audioRef.current.src = playableUrl;
        } catch (error) {
          console.error('Error getting playable URL:', error);
          setError('Không thể xử lý URL audio. Vui lòng kiểm tra lại URL.');
        }
      }
    };

    initAudio();
  }, [audioUrl, getPlayableAudioUrl]);

  // Handle audio metadata loaded
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration);
      setTotalDuration(Math.round(audio.duration));
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    return () => audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
  }, []);

  // Update current duration while playing
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying && !isDragging) {
      interval = setInterval(() => {
        if (audioRef.current) {
          const currentTime = Math.round(audioRef.current.currentTime);
          setCurrentDuration(currentTime);
        }
      }, 100);
    }
    return () => clearInterval(interval);
  }, [isPlaying, isDragging]);

  // Handle seek bar interaction
  const handleSeekBarClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !seekBarRef.current || !totalDuration) return;

    const rect = seekBarRef.current.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const newTime = percent * totalDuration;

    console.log('Seeking to:', newTime, 'seconds');
    audioRef.current.currentTime = newTime;
    setCurrentDuration(Math.round(newTime));
  };

  const handleSeekBarMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
    handleSeekBarClick(e);
  };

  const handleSeekBarMouseUp = () => {
    setIsDragging(false);
  };

  const handleSeekBarMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging || !audioRef.current || !seekBarRef.current || !totalDuration) return;

    const rect = seekBarRef.current.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const newTime = percent * totalDuration;

    console.log('Dragging to:', newTime, 'seconds');
    audioRef.current.currentTime = newTime;
    setCurrentDuration(Math.round(newTime));
  };

  // Add global mouse up handler
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDragging(false);
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);
    return () => window.removeEventListener('mouseup', handleGlobalMouseUp);
  }, []);

  // Handle audio ended
  const handleAudioEnded = () => {
    console.log('Audio ended');
    setIsPlaying(false);
    if (currentDuration > 0) {
      setRecordedDurations(prev => [...prev, currentDuration]);
    }
  };

  // Handle save
  const handleSave = () => {
    if (recordedDurations.length > 0) {
      onDurationsRecorded(recordedDurations);
      onClose();
    }
  };

  // Handle reset
  const handleReset = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.pause();
    }
    setIsPlaying(false);
    setRecordedDurations([]);
    setCurrentQuestionIndex(0);
    setCurrentDuration(0);
  };

  // Format time in MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle play/pause
  const handlePlayPause = async () => {
    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        setIsLoading(true);
        setError(null);

        if (audioRef.current.currentTime === 0) {
          // Reset everything when starting from beginning
          setRecordedDurations([]);
          setCurrentQuestionIndex(0);
          setCurrentDuration(0);
        }

        if (getPlayableAudioUrl) {
          // Get playable URL
          const playableUrl = await getPlayableAudioUrl(audioUrl);
          console.log('Playing audio from URL:', playableUrl);

          // Set the audio source if it's different
          if (audioRef.current.src !== playableUrl) {
            audioRef.current.src = playableUrl;
          }
        } else {
          console.error('getPlayableAudioUrl function not available');
          setError('Không thể xử lý URL audio. Vui lòng tải lại trang.');
          return;
        }

        // Try to play the audio
        try {
          await audioRef.current.play();
          console.log('Audio playback started successfully');
          setIsPlaying(true);
          startTimeRef.current = Date.now();
        } catch (playError) {
          console.error('Error playing audio:', playError);
          setError('Không thể phát file âm thanh. Vui lòng kiểm tra lại URL hoặc quyền truy cập file.');
          setIsPlaying(false);
        } finally {
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Error in handlePlayPause:', error);
      setError('Có lỗi xảy ra khi xử lý file âm thanh. Vui lòng thử lại.');
      setIsPlaying(false);
      setIsLoading(false);
    }
  };

  // Add error handling for audio loading
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleError = (e: Event) => {
      console.error('Audio error:', e);
      const error = (e.target as HTMLAudioElement).error;
      let errorMessage = 'Lỗi khi tải file âm thanh.';

      if (error) {
        switch (error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = 'Quá trình tải file bị hủy.';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = 'Lỗi kết nối mạng khi tải file.';
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = 'Không thể giải mã file âm thanh.';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'Định dạng file không được hỗ trợ.';
            break;
        }
      }

      setError(errorMessage);
      setIsPlaying(false);
    };

    audio.addEventListener('error', handleError);
    return () => audio.removeEventListener('error', handleError);
  }, []);

  return (
    <div className="audio-recorder-overlay">
      <div className="audio-recorder-modal">
        <div className="audio-recorder-header">
          <h2>Ghi nhận thời lượng cho từng câu hỏi</h2>
          <p className="instructions">
            Nhấn phím Space mỗi khi kết thúc một câu hỏi để ghi nhận thời lượng.
          </p>
        </div>

        <div className="audio-recorder-content">
          <audio
            ref={audioRef}
            src={audioUrl}
            onEnded={handleAudioEnded}
            style={{ display: 'none' }}
          />

          <div className="audio-controls">
            <button
              className="control-button"
              onClick={handlePlayPause}
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="spinner" />
              ) : isPlaying ? (
                <FiPause size={24} />
              ) : (
                <FiPlay size={24} />
              )}
              {isLoading ? 'Đang tải...' : isPlaying ? 'Tạm dừng' : 'Phát'}
            </button>
            <button
              className="control-button"
              onClick={handleReset}
              disabled={isLoading}
            >
              <FiRefreshCw size={24} />
              Làm lại
            </button>
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="seek-bar-container">
            <div
              ref={seekBarRef}
              className="seek-bar"
              onClick={handleSeekBarClick}
              onMouseDown={handleSeekBarMouseDown}
              onMouseMove={handleSeekBarMouseMove}
            >
              <div
                className="seek-bar-progress"
                style={{ width: `${totalDuration ? (currentDuration / totalDuration) * 100 : 0}%` }}
              />
              <div
                className="seek-bar-handle"
                style={{ left: `${totalDuration ? (currentDuration / totalDuration) * 100 : 0}%` }}
              />
            </div>
            <div className="seek-time">
              <span>{formatTime(currentDuration)}</span>
              <span>{formatTime(totalDuration)}</span>
            </div>
          </div>

          <div className="duration-display">
            <div className="current-duration">
              Thời gian hiện tại: {currentDuration}s
            </div>
            <div className="recorded-durations">
              <h3>Các câu đã ghi nhận:</h3>
              {recordedDurations.map((duration, index) => (
                <div key={index} className="duration-item">
                  Câu {index + 1}: {duration}s
                </div>
              ))}
            </div>
          </div>

          <div className="recorder-actions">
            <button
              className="save-button"
              onClick={handleSave}
              disabled={recordedDurations.length === 0 || isLoading}
            >
              Lưu thời lượng
            </button>
            <button
              className="cancel-button"
              onClick={onClose}
              disabled={isLoading}
            >
              Hủy
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioDurationRecorder;