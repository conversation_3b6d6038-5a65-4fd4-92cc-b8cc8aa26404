import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveC<PERSON>r,
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';

// Type for dashboard statistics
interface DashboardStats {
  usersCount: number;
  activeUsersCount: number;
  examsCount: number;
  attemptsCount: number;
  completedAttemptsCount: number;
  topExams: {
    examId: number;
    title: string;
    attemptsCount: number;
  }[];
  examScores: {
    examId: number;
    title: string;
    averageScore: number;
  }[];
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/dashboard/stats');
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard stats');
        }
        const data = await response.json();
        setStats(data);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch dashboard stats:', err);
        setError('Không thể tải dữ liệu thống kê. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="spinner"></div>
        <p>Đang tải dữ liệu thống kê...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-error">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Thử lại</button>
      </div>
    );
  }

  // Prepare data for charts
  const usersData = [
    { name: 'Tổng số người dùng', value: stats?.usersCount || 0 },
    { name: 'Người dùng đã kích hoạt', value: stats?.activeUsersCount || 0 }
  ];

  const examsData = [
    { name: 'Tổng số đề thi', value: stats?.examsCount || 0 },
    { name: 'Tổng số lượt thi', value: stats?.attemptsCount || 0 },
    { name: 'Đã hoàn thành', value: stats?.completedAttemptsCount || 0 }
  ];

  return (
    <div className="dashboard">
      <h2 className="dashboard-title">Thống kê tổng quan</h2>
      
      <div className="dashboard-stats-grid">
        <div className="stat-card">
          <div className="stat-icon user-icon">👥</div>
          <div className="stat-content">
            <h3>Người dùng</h3>
            <div className="stat-number">{stats?.usersCount || 0}</div>
            <div className="stat-label">Người dùng đã đăng ký</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon active-user-icon">✓</div>
          <div className="stat-content">
            <h3>Người dùng đã kích hoạt</h3>
            <div className="stat-number">{stats?.activeUsersCount || 0}</div>
            <div className="stat-label">Người dùng đang hoạt động</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon exam-icon">📝</div>
          <div className="stat-content">
            <h3>Đề thi</h3>
            <div className="stat-number">{stats?.examsCount || 0}</div>
            <div className="stat-label">Tổng số đề thi</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon attempt-icon">🔄</div>
          <div className="stat-content">
            <h3>Lượt thi</h3>
            <div className="stat-number">{stats?.attemptsCount || 0}</div>
            <div className="stat-label">Tổng số lượt thi</div>
          </div>
        </div>
      </div>

      <div className="dashboard-charts">
        <div className="chart-container">
          <h3>Thống kê người dùng</h3>
          <div className="chart-wrapper" style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={usersData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {usersData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="chart-container">
          <h3>Thống kê đề thi</h3>
          <div className="chart-wrapper" style={{ height: '300px' }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={examsData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="dashboard-tables">
        <div className="table-container">
          <h3>Đề thi được thực hiện nhiều nhất</h3>
          <table className="data-table">
            <thead>
              <tr>
                <th>Tên đề thi</th>
                <th>Số lượt thi</th>
              </tr>
            </thead>
            <tbody>
              {stats?.topExams && stats.topExams.length > 0 ? (
                stats.topExams.map((exam) => (
                  <tr key={exam.examId}>
                    <td>{exam.title}</td>
                    <td>{exam.attemptsCount}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={2} className="no-data">Chưa có dữ liệu</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <div className="table-container">
          <h3>Điểm trung bình của đề thi</h3>
          <table className="data-table">
            <thead>
              <tr>
                <th>Tên đề thi</th>
                <th>Điểm trung bình</th>
              </tr>
            </thead>
            <tbody>
              {stats?.examScores && stats.examScores.length > 0 ? (
                stats.examScores.map((exam) => (
                  <tr key={exam.examId}>
                    <td>{exam.title}</td>
                    <td>{exam.averageScore.toFixed(1)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={2} className="no-data">Chưa có dữ liệu</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;