import React, { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import Dashboard from './Dashboard';
import ExamCreator from './ExamCreator';
import ExamList from './ExamList';

type AdminTab = 'dashboard' | 'exams' | 'users';
type ExamManagementMode = 'list' | 'create' | 'edit';

const AdminPage: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<AdminTab>('dashboard');
  const [examManagementMode, setExamManagementMode] = useState<ExamManagementMode>('list');
  const [selectedExamId, setSelectedExamId] = useState<number | null>(null);

  const handleLogout = () => {
    logout();
  };

  const handleEditExam = (examId: number) => {
    setSelectedExamId(examId);
    setExamManagementMode('edit');
  };

  const handleCreateExam = () => {
    setSelectedExamId(null);
    setExamManagementMode('create');
  };

  const handleSaveExam = (exam: any) => {
    console.log('Exam saved:', exam);
    setExamManagementMode('list');
  };

  const handleBackToList = () => {
    setExamManagementMode('list');
  };

  const renderExamsContent = () => {
    switch (examManagementMode) {
      case 'list':
        return (
          <>
            <div className="exam-list-header">
              <h2>Quản lý đề thi</h2>
              <button className="add-exam-button" onClick={handleCreateExam}>
                + Thêm đề thi mới
              </button>
            </div>
            <ExamList onEditExam={handleEditExam} />
          </>
        );
      case 'create':
        return (
          <div>
            <div className="exam-editor-header">
              <button className="back-button" onClick={handleBackToList}>
                &larr; Quay lại danh sách
              </button>
              <h2>Tạo đề thi mới</h2>
            </div>
            <ExamCreator onSaveExam={handleSaveExam} />
          </div>
        );
      case 'edit':
        return (
          <div>
            <div className="exam-editor-header">
              <button className="back-button" onClick={handleBackToList}>
                &larr; Quay lại danh sách
              </button>
              <h2>Chỉnh sửa đề thi {selectedExamId}</h2>
            </div>
            <ExamCreator 
              examId={selectedExamId || undefined} 
              onSaveExam={handleSaveExam} 
            />
          </div>
        );
      default:
        return <ExamList onEditExam={handleEditExam} />;
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'exams':
        return renderExamsContent();
      case 'users':
        return <div className="admin-panel-content">
          <h2>Quản lý người dùng</h2>
          <p>Chức năng quản lý người dùng đang được phát triển...</p>
        </div>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="admin-container">
      <div className="admin-header">
        <div className="logo">
          <h1>EPS-TOPIK</h1>
          <span>Hệ thống quản trị</span>
        </div>
        <div className="user-info">
          <span className="user-name">{user?.username || 'Admin'}</span>
          <button className="logout-button" onClick={handleLogout}>Đăng xuất</button>
        </div>
      </div>
      
      <div className="admin-content">
        <div className="admin-sidebar">
          <nav>
            <ul>
              <li className={activeTab === 'dashboard' ? 'active' : ''}>
                <button onClick={() => setActiveTab('dashboard')}>
                  <span className="icon">📊</span>
                  <span className="label">Tổng quan</span>
                </button>
              </li>
              <li className={activeTab === 'exams' ? 'active' : ''}>
                <button onClick={() => setActiveTab('exams')}>
                  <span className="icon">📝</span>
                  <span className="label">Quản lý đề thi</span>
                </button>
              </li>
              <li className={activeTab === 'users' ? 'active' : ''}>
                <button onClick={() => setActiveTab('users')}>
                  <span className="icon">👥</span>
                  <span className="label">Quản lý người dùng</span>
                </button>
              </li>
            </ul>
          </nav>
        </div>
        
        <div className="admin-panel">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default AdminPage;