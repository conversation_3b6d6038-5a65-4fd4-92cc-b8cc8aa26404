import React, { useEffect, useRef, useState, useCallback } from 'react';
import { FiPlay, FiPause, FiPlus } from 'react-icons/fi';
import WaveSurfer from 'wavesurfer.js';
// We'll use a different approach for markers since addMarker isn't directly available
import './AudioTimelineRecorder.css';
import { extractGoogleDriveFileId } from '../../lib/utils';

interface AudioTimelineRecorderProps {
  audioUrl: string;
  onStartTimesRecorded: (startTimes: number[], markerCount?: number, audioDuration?: number) => void;
  onClose: () => void;
  existingStartTimes?: number[]; // Thời điểm bắt đầu đã có của các câu hỏi nghe
}

interface Marker {
  id: string;
  time: number;
  label: string;
  color: string;
  isDragging?: boolean;
}

interface Region {
  id: string;
  start: number;
  end: number;
  color: string;
  drag?: boolean;
  resize?: boolean;
}

const AudioTimelineRecorder: React.FC<AudioTimelineRecorderProps> = ({
  audioUrl,
  onStartTimesRecorded,
  onClose,
  existingStartTimes
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [markers, setMarkers] = useState<Marker[]>([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [draggedMarker, setDraggedMarker] = useState<string | null>(null);
  const [markerErrors, setMarkerErrors] = useState<{[key: string]: string}>({});
  const [isDragging, setIsDragging] = useState(false);
  const [dragTooltip, setDragTooltip] = useState<{id: string, time: number, x: number, y: number} | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const waveformRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const regionsRef = useRef<Map<string, Region>>(new Map());
  const timelineContainerRef = useRef<HTMLDivElement>(null);
  const markersOverlayRef = useRef<HTMLDivElement>(null);

  // Khai báo state để lưu hàm getPlayableAudioUrl
  const [getPlayableAudioUrl, setGetPlayableAudioUrl] = useState<any>(null);

  // Import utility function
  useEffect(() => {
    const importUtils = async () => {
      try {
        const utils = await import('../../lib/utils');
        setGetPlayableAudioUrl(() => utils.getPlayableAudioUrl);
      } catch (error) {
        console.error('Error importing utils:', error);
        setError('Không thể tải các tiện ích cần thiết. Vui lòng tải lại trang.');
      }
    };

    importUtils();
  }, []);


  // Wrap setMarkers to track unsaved changes
  const updateMarkers = (newMarkers: Marker[]) => {
    setMarkers(newMarkers);
    setHasUnsavedChanges(true);
  };

  // Create markers from existing start times when component mounts and audio is loaded
  useEffect(() => {
    // Only proceed if we have existing start times and duration is loaded
    if (!existingStartTimes || existingStartTimes.length === 0 || duration === 0) return;

    try {
      // Create markers from start times
      const newMarkers: Marker[] = [];

      // Log thông tin các start times
      console.log('Creating markers from existing start times:', existingStartTimes);

      // For each start time except the first one (which is the start of the first question),
      // create a marker to mark the transition to the next question
      for (let i = 1; i < existingStartTimes.length; i++) {
        // Skip invalid start times
        if (existingStartTimes[i] < 0 || existingStartTimes[i] >= duration) {
          console.log(`Skipping invalid start time at index ${i}: ${existingStartTimes[i]}`);
          continue;
        }

        // Create a marker at this start time to mark the beginning of question i+1
        // (and the end of question i)
        newMarkers.push({
          id: `existing-marker-${i-1}`,
          time: existingStartTimes[i],
          label: `Câu ${i+1}`,
          color: '#4a9eff'
        });

        console.log(`Created marker for question ${i+1} at time ${existingStartTimes[i]}s`);
      }

      // Only set markers if we have any (avoid overwriting if empty)
      if (newMarkers.length > 0) {
        console.log(`Created ${newMarkers.length} markers from existing start times`);
        setMarkers(newMarkers); // Use setMarkers directly here, not updateMarkers, as this is initial load
        setHasUnsavedChanges(false); // No unsaved changes yet

        // Show a success notification to the user
        const successMessage = `Đã tải ${newMarkers.length} điểm đánh dấu từ dữ liệu có sẵn. Bạn có thể chỉnh sửa hoặc thêm điểm đánh dấu mới.`;
        // Use a custom data attribute to style this as a success message
        setError(successMessage);

        // Clear the notification after 5 seconds
        setTimeout(() => {
          setError(null);
        }, 5000);
      }
    } catch (error) {
      console.error('Error creating markers from existing durations:', error);
    }
  }, [existingStartTimes, duration]);

  // Initialize WaveSurfer
  useEffect(() => {
    if (!getPlayableAudioUrl || !waveformRef.current) return;

    const initWaveSurfer = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get playable URL
        let playableUrl;
        try {
          console.log('Getting playable URL for:', audioUrl);
          playableUrl = await getPlayableAudioUrl(audioUrl);
          console.log('Got playable URL:', playableUrl);

          // Kiểm tra URL có hợp lệ không
          if (!playableUrl) {
            throw new Error('URL trả về rỗng');
          }

          // Kiểm tra URL có thể truy cập được không
          try {
            const testResponse = await fetch(playableUrl, { method: 'HEAD' });
            if (!testResponse.ok) {
              throw new Error(`Không thể truy cập file: ${testResponse.status} ${testResponse.statusText}`);
            }
            console.log('URL is accessible:', testResponse.status, testResponse.statusText);
          } catch (fetchError) {
            console.error('Error testing URL accessibility:', fetchError);
            // Không throw lỗi ở đây, vẫn tiếp tục thử load audio
            console.warn('Continuing despite URL test failure');
          }
        } catch (urlError) {
          console.error('Error getting playable URL:', urlError);
          setError(`Không thể lấy URL phát trực tiếp: ${urlError.message || 'Vui lòng kiểm tra lại URL và quyền truy cập file.'}`);
          setIsLoading(false);
          return;
        }

        // Create WaveSurfer instance with error handling
        let wavesurfer;
        try {
          wavesurfer = WaveSurfer.create({
            container: waveformRef.current,
            waveColor: '#4a9eff',
            progressColor: '#00468C',
            cursorColor: '#f5f5f5',
            height: 40,
            normalize: true,
            pixelRatio: 1,
            barWidth: 1,
            barGap: 0,
            barRadius: 0,
            scrollParent: false,
            fillParent: true, // Fill the entire parent container
          });
        } catch (wavesurferError) {
          console.error('Error creating WaveSurfer instance:', wavesurferError);
          setError('Không thể khởi tạo trình phát âm thanh. Vui lòng thử lại sau.');
          setIsLoading(false);
          return;
        }

        // Save reference
        wavesurferRef.current = wavesurfer;

        // Event listeners for WaveSurfer v7+
        try {
          wavesurfer.on('ready', () => {
            console.log('WaveSurfer ready');
            setDuration(wavesurfer.getDuration());
            setIsLoading(false);
          });

          wavesurfer.on('timeupdate', (time) => {
            setCurrentTime(time);
          });

          wavesurfer.on('finish', () => {
            setIsPlaying(false);
          });

          wavesurfer.on('error', (e) => {
            console.error('WaveSurfer error:', e);
            setError('Không thể tải file âm thanh. Vui lòng kiểm tra lại URL và quyền truy cập file.');
            setIsLoading(false);
          });

          // Update isPlaying state when playback state changes
          wavesurfer.on('play', () => setIsPlaying(true));
          wavesurfer.on('pause', () => setIsPlaying(false));
        } catch (eventError) {
          console.error('Error setting up WaveSurfer event listeners:', eventError);
          setError('Không thể thiết lập trình phát âm thanh. Vui lòng thử lại sau.');
          setIsLoading(false);
          return;
        }

        // Load audio with error handling
        try {
          console.log('Loading audio into WaveSurfer:', playableUrl);

          // Thử sử dụng fetch để kiểm tra file trước khi load vào WaveSurfer
          try {
            const audioResponse = await fetch(playableUrl);
            if (!audioResponse.ok) {
              throw new Error(`Không thể tải file âm thanh: ${audioResponse.status} ${audioResponse.statusText}`);
            }

            // Kiểm tra content-type có phải là audio không
            const contentType = audioResponse.headers.get('content-type');
            console.log('File content type:', contentType);

            if (contentType && !contentType.includes('audio/') && !contentType.includes('video/') && !contentType.includes('application/octet-stream')) {
              console.warn('File might not be an audio file. Content-Type:', contentType);
            }
          } catch (fetchError) {
            console.error('Error pre-checking audio file:', fetchError);
            // Vẫn tiếp tục thử load vào WaveSurfer
          }

          // Thử load audio vào WaveSurfer
          await wavesurfer.load(playableUrl);
          console.log('Audio loaded successfully into WaveSurfer');
        } catch (loadError) {
          console.error('Error loading audio into WaveSurfer:', loadError);

          // Thử sử dụng proxy URL nếu URL trực tiếp không hoạt động
          try {
            console.log('Trying with proxy URL as fallback...');
            const fileId = extractGoogleDriveFileId(audioUrl);
            if (fileId) {
              const proxyUrl = `/api/proxy/gdrive?id=${fileId}`;
              console.log('Using proxy URL:', proxyUrl);
              await wavesurfer.load(proxyUrl);
              console.log('Audio loaded successfully with proxy URL');
            } else {
              throw new Error('Không thể trích xuất ID file');
            }
          } catch (proxyError) {
            console.error('Error loading audio with proxy URL:', proxyError);
            setError('Không thể tải file âm thanh. Vui lòng kiểm tra lại URL và quyền truy cập file.');
            setIsLoading(false);
            return;
          }
        }

        // Keyboard shortcuts removed
      } catch (error) {
        console.error('Error initializing WaveSurfer:', error);
        setError('Không thể khởi tạo trình phát âm thanh. Vui lòng thử lại sau.');
        setIsLoading(false);
      }
    };

    initWaveSurfer();

    return () => {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
    };
  }, [audioUrl, getPlayableAudioUrl]);

  // Handle play/pause
  const handlePlayPause = () => {
    if (!wavesurferRef.current) {
      console.error('WaveSurfer instance not available');
      setError('Trình phát âm thanh chưa sẵn sàng. Vui lòng tải lại trang.');
      return;
    }

    try {
      // Try to use playPause method
      try {
        wavesurferRef.current.playPause();
        // Toggle playing state
        setIsPlaying(!isPlaying);
      } catch (error) {
        console.error('Error using playPause method:', error);
        // Fallback to separate play/pause methods
        if (isPlaying) {
          try {
            wavesurferRef.current.pause();
            setIsPlaying(false);
          } catch (pauseError) {
            console.error('Error pausing audio:', pauseError);
            throw new Error('Không thể tạm dừng âm thanh');
          }
        } else {
          try {
            wavesurferRef.current.play();
            setIsPlaying(true);
          } catch (playError) {
            console.error('Error playing audio:', playError);
            throw new Error('Không thể phát âm thanh');
          }
        }
      }
    } catch (err) {
      console.error('Error playing/pausing audio:', err);
      setError('Không thể phát/tạm dừng âm thanh. Vui lòng thử lại sau.');
      setIsPlaying(false);

      // Try to reinitialize WaveSurfer if there's a persistent error
      if (wavesurferRef.current) {
        try {
          wavesurferRef.current.destroy();
          wavesurferRef.current = null;
          // Trigger re-initialization on next render
          setCurrentTime(0);
        } catch (destroyError) {
          console.error('Error destroying WaveSurfer instance:', destroyError);
        }
      }
    }
  };

  // Handle seek
  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!wavesurferRef.current) {
      console.error('WaveSurfer instance not available for seeking');
      return;
    }

    try {
      // Get the clicked element's bounding rectangle
      const rect = e.currentTarget.getBoundingClientRect();
      const pos = (e.clientX - rect.left) / rect.width;
      const newTime = pos * duration;

      // Update wavesurfer position
      wavesurferRef.current.seekTo(pos);

      // Update current time
      setCurrentTime(newTime);
    } catch (seekError) {
      console.error('Error seeking audio:', seekError);
      setError('Không thể di chuyển đến vị trí âm thanh. Vui lòng thử lại sau.');
    }
  };

  // Zoom functions removed

  // Handle marker creation
  const handleAddMarker = () => {
    if (!wavesurferRef.current) {
      console.error('WaveSurfer instance not available for adding marker');
      setError('Trình phát âm thanh chưa sẵn sàng. Vui lòng tải lại trang.');
      return;
    }

    try {
      // Get current playback time
      let currentTime;
      try {
        currentTime = wavesurferRef.current.getCurrentTime();
      } catch (timeError) {
        console.error('Error getting current time:', timeError);
        setError('Không thể lấy thời gian hiện tại. Vui lòng thử lại sau.');
        return;
      }

      // Create a unique ID for the marker
      const markerId = `marker-${Date.now()}`;

      // Create the new marker object
      // Label của marker là câu hỏi tiếp theo (số lượng markers hiện tại + 2)
      // Ví dụ: Nếu có 0 marker, thêm marker đầu tiên sẽ là câu 2
      // Làm tròn thời gian thành số nguyên (giây)
      const roundedTime = Math.round(currentTime);
      const newMarker: Marker = {
        id: markerId,
        time: roundedTime,
        label: `Câu ${markers.length + 2}`,
        color: '#ff4a4a'
      };

      // Add marker to state - this will trigger a re-render and update the UI
      updateMarkers([...markers, newMarker]);

      // Log for debugging
      console.log(`Added marker at ${roundedTime}s (rounded from ${currentTime.toFixed(2)}s)`);
    } catch (error) {
      console.error('Error adding marker:', error);
      setError('Không thể thêm điểm đánh dấu. Vui lòng thử lại sau.');
    }
  };

  // Handle marker removal
  const handleRemoveMarker = (markerId: string) => {
    try {
      // Remove from state only
      const newMarkers = markers.filter(m => m.id !== markerId);
      updateMarkers(newMarkers);
      console.log(`Removed marker ${markerId}`);
    } catch (error) {
      console.error('Error removing marker:', error);
      setError('Không thể xóa điểm đánh dấu. Vui lòng thử lại sau.');
    }
  };

  // Handle marker time update
  const handleMarkerTimeUpdate = (markerId: string, newTime: number) => {
    try {
      // Làm tròn thời gian thành số nguyên (giây)
      newTime = Math.round(newTime);

      // Validate time range
      if (newTime < 0) newTime = 0;
      if (newTime > duration) newTime = duration;

      // Get the current marker and its index
      const markerIndex = markers.findIndex(m => m.id === markerId);
      if (markerIndex === -1) return; // Marker not found

      // Sort markers by time to check constraints
      const sortedMarkers = [...markers].sort((a, b) => a.time - b.time);
      const sortedIndex = sortedMarkers.findIndex(m => m.id === markerId);

      // Check if this would violate the order constraint
      // A marker cannot be moved before the previous marker or after the next marker
      let isValid = true;
      let constraintMessage = '';

      // Check if moving before previous marker
      if (sortedIndex > 0 && newTime < sortedMarkers[sortedIndex - 1].time) {
        isValid = false;
        constraintMessage = `Thời điểm không thể trước câu ${sortedIndex} (${formatTime(sortedMarkers[sortedIndex - 1].time)})`;
      }

      // Check if moving after next marker
      if (sortedIndex < sortedMarkers.length - 1 && newTime > sortedMarkers[sortedIndex + 1].time) {
        isValid = false;
        constraintMessage = `Thời điểm không thể sau câu ${sortedIndex + 2} (${formatTime(sortedMarkers[sortedIndex + 1].time)})`;
      }

      // If validation fails, set error for this marker and return
      if (!isValid) {
        console.warn(constraintMessage);
        // Set error for this specific marker
        setMarkerErrors(prev => ({
          ...prev,
          [markerId]: constraintMessage
        }));
        // Clear error after 5 seconds
        setTimeout(() => {
          setMarkerErrors(prev => {
            const newErrors = {...prev};
            delete newErrors[markerId];
            return newErrors;
          });
        }, 5000);
        return false; // Return false to indicate validation failed
      }

      // Update marker time in state
      const updatedMarkers = markers.map(marker => {
        if (marker.id === markerId) {
          return { ...marker, time: newTime };
        }
        return marker;
      });

      // Update markers state
      updateMarkers(updatedMarkers);

      // If wavesurfer is available, seek to the new time to show the change
      if (wavesurferRef.current && markerId === draggedMarker) {
        wavesurferRef.current.seekTo(newTime / duration);
        setCurrentTime(newTime);
      }

      // Clear any previous error for this marker
      setMarkerErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[markerId];
        return newErrors;
      });

      // Log for debugging
      console.log(`Updated marker ${markerId} to ${newTime.toFixed(2)}s`);

      return true; // Return true to indicate success
    } catch (error) {
      console.error('Error updating marker time:', error);
    }
  };

  // Handle marker click (seek to marker position)
  const handleMarkerClick = (time: number, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation(); // Prevent the click from bubbling to the progress bar
    }
    if (!wavesurferRef.current) return;

    // Seek to the marker position
    wavesurferRef.current.seekTo(time / duration);

    // Update current time
    setCurrentTime(time);
  };

  // Simple drag start handler
  const handleMarkerDragStart = (markerId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Find the marker
    const marker = markers.find(m => m.id === markerId);
    if (!marker) return;

    // Set dragging state
    setDraggedMarker(markerId);

    // Initial position
    const containerRect = progressRef.current?.getBoundingClientRect();
    if (!containerRect) return;

    // Calculate initial position
    const startX = e.clientX;
    const startTime = marker.time;

    // Show tooltip
    setDragTooltip({
      id: markerId,
      time: startTime,
      x: e.clientX,
      y: e.clientY - 30
    });

    // Mouse move handler
    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (!containerRect) return;

      // Calculate time based on mouse movement
      const deltaX = moveEvent.clientX - startX;
      const deltaTime = (deltaX / containerRect.width) * duration;
      let newTime = startTime + deltaTime;

      // Constrain to valid range
      if (newTime < 0) newTime = 0;
      if (newTime > duration) newTime = duration;

      // Get sorted markers to find previous and next markers
      const sortedMarkers = [...markers].sort((a, b) => a.time - b.time);
      const sortedIndex = sortedMarkers.findIndex(m => m.id === markerId);

      // Determine min and max allowed positions
      let minTime = 0;
      let maxTime = duration;

      if (sortedIndex > 0) {
        minTime = sortedMarkers[sortedIndex - 1].time + 0.1; // Add small buffer
      }

      if (sortedIndex < sortedMarkers.length - 1) {
        maxTime = sortedMarkers[sortedIndex + 1].time - 0.1; // Add small buffer
      }

      // Constrain new time within allowed range
      if (newTime < minTime) newTime = minTime;
      if (newTime > maxTime) newTime = maxTime;

      // Làm tròn thời gian thành số nguyên khi kéo marker
      const roundedTime = Math.round(newTime);

      // Update marker position in state during drag for real-time feedback
      const updatedMarkers = markers.map(marker => {
        if (marker.id === markerId) {
          return { ...marker, time: roundedTime };
        }
        return marker;
      });

      updateMarkers(updatedMarkers);

      // Update tooltip - hiển thị thời gian đã làm tròn
      setDragTooltip({
        id: markerId,
        time: roundedTime,
        x: moveEvent.clientX,
        y: moveEvent.clientY - 30
      });

      // Update wavesurfer position to show current position
      if (wavesurferRef.current) {
        wavesurferRef.current.seekTo(newTime / duration);
        setCurrentTime(newTime);
      }
    };

    // Mouse up handler
    const handleMouseUp = (upEvent: MouseEvent) => {
      // Remove all event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      if (!containerRect) return;

      // Calculate final time
      const deltaX = upEvent.clientX - startX;
      const deltaTime = (deltaX / containerRect.width) * duration;
      let newTime = startTime + deltaTime;

      // Constrain to valid range
      if (newTime < 0) newTime = 0;
      if (newTime > duration) newTime = duration;

      // Làm tròn thời gian thành số nguyên khi kết thúc kéo
      newTime = Math.round(newTime);

      // Get sorted markers to find previous and next markers
      const sortedMarkers = [...markers].sort((a, b) => a.time - b.time);
      const sortedIndex = sortedMarkers.findIndex(m => m.id === markerId);

      // Determine min and max allowed positions
      let minTime = 0;
      let maxTime = duration;

      if (sortedIndex > 0) {
        minTime = sortedMarkers[sortedIndex - 1].time + 0.1; // Add small buffer
      }

      if (sortedIndex < sortedMarkers.length - 1) {
        maxTime = sortedMarkers[sortedIndex + 1].time - 0.1; // Add small buffer
      }

      // Constrain new time within allowed range
      if (newTime < minTime) newTime = minTime;
      if (newTime > maxTime) newTime = maxTime;

      // No need to update markers again since we already did it during drag
      // Just update the audio position one last time to ensure accuracy
      if (wavesurferRef.current) {
        wavesurferRef.current.seekTo(newTime / duration);
        setCurrentTime(newTime);
      }

      // Clear drag state
      setDraggedMarker(null);
      setDragTooltip(null);
    };

    // Touch move handler
    const handleTouchMove = (e: TouchEvent) => {
      const touch = e.touches[0];
      handleMouseMove({
        clientX: touch.clientX,
        clientY: touch.clientY
      } as MouseEvent);
      e.preventDefault(); // Prevent scrolling
    };

    // Touch end handler
    const handleTouchEnd = (e: TouchEvent) => {
      if (e.changedTouches.length > 0) {
        const touch = e.changedTouches[0];
        handleMouseUp({
          clientX: touch.clientX,
          clientY: touch.clientY
        } as MouseEvent);
      } else {
        // If no touch info, just use the last known position
        handleMouseUp({
          clientX: startX,
          clientY: 0
        } as MouseEvent);
      }
    };

    // Add event listeners for mouse and touch
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
  };

  // Handle save
  const handleSave = () => {
    if (markers.length === 0) {
      setSaveMessage({
        type: 'error',
        text: 'Không có điểm đánh dấu nào để lưu.'
      });
      // Just clear the message after 3 seconds, but don't close the popup
      // so the user can add markers
      setTimeout(() => setSaveMessage(null), 3000);
      return;
    }

    // Set saving state
    setIsSaving(true);
    setSaveMessage(null);

    try {
      // Sort markers by time
      const sortedMarkers = [...markers].sort((a, b) => a.time - b.time);

      // Get start times from markers
      // Các markers đánh dấu điểm chuyển tiếp giữa các câu hỏi
      // Ví dụ: Nếu có 3 markers ở thời điểm 30s, 60s, 90s
      // Thì có 4 câu hỏi: câu 1 (0-30s), câu 2 (30-60s), câu 3 (60-90s), câu 4 (90s-end)

      // Kiểm tra và loại bỏ các markers trùng lặp (có thời gian quá gần nhau)
      const uniqueMarkers: Marker[] = [];
      for (let i = 0; i < sortedMarkers.length; i++) {
        // Nếu là marker đầu tiên hoặc thời gian cách marker trước ít nhất 0.5s
        if (i === 0 || sortedMarkers[i].time - sortedMarkers[i-1].time >= 0.5) {
          uniqueMarkers.push(sortedMarkers[i]);
        } else {
          console.warn(`Skipping marker at ${sortedMarkers[i].time}s because it's too close to previous marker`);
        }
      }

      // Câu hỏi đầu tiên luôn có start_at là 0s
      // Các câu hỏi tiếp theo có start_at là thời điểm của các markers
      // Đảm bảo tất cả các giá trị thời gian đều là số nguyên
      const startTimes: number[] = [0, ...uniqueMarkers.map(marker => Math.round(marker.time))];

      // Log thông tin chi tiết
      console.log('Original markers:', markers.length);
      console.log('Sorted markers:', sortedMarkers.length);
      console.log('Unique markers after filtering:', uniqueMarkers.length);
      console.log('Markers:', uniqueMarkers.map(m => ({ id: m.id, time: m.time })));
      console.log('Start times (including 0 at the beginning):', startTimes);

      // Pass start times, marker count, and audio duration to parent component
      // The parent component will handle creating new questions if needed
      // Đảm bảo thời lượng file nghe là số nguyên
      const roundedDuration = Math.round(duration);
      onStartTimesRecorded(startTimes, startTimes.length, roundedDuration);

      // Reset unsaved changes flag
      setHasUnsavedChanges(false);

      // Show success message
      // Số lượng câu hỏi = số lượng điểm đánh dấu + 1 (câu đầu tiên)
      const totalQuestions = uniqueMarkers.length + 1;
      setSaveMessage({
        type: 'success',
        text: `Đã lưu ${uniqueMarkers.length} điểm đánh dấu và cập nhật thời điểm bắt đầu cho ${totalQuestions} câu hỏi nghe thành công.`
      });

      // Show success message and close after a short delay
      // Use a longer delay to ensure the parent component has time to process the durations
      setTimeout(() => {
        setSaveMessage(null);
        onClose(); // Close the popup after showing success message
      }, 2000);
    } catch (error) {
      console.error('Error saving markers:', error);
      setSaveMessage({
        type: 'error',
        text: 'Có lỗi xảy ra khi lưu điểm đánh dấu.'
      });
      // Just clear the message after 3 seconds, but don't close the popup
      // so the user can try again
      setTimeout(() => setSaveMessage(null), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle close
  const handleClose = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('Bạn có thay đổi chưa lưu. Bạn có chắc muốn thoát không?')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  // Format time
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Keyboard shortcuts removed

  return (
    <div className="audio-timeline-overlay">
      {/* Fixed position save message for better visibility */}
      {saveMessage && (
        <div className="save-message-overlay">
          <div className="save-message-popup" data-type={saveMessage.type}>
            {saveMessage.text}
          </div>
        </div>
      )}

      <div className="audio-timeline-modal">
        <div className="audio-timeline-header">
          <h2>Ghi nhận thời lượng cho từng câu hỏi</h2>
          <p className="instructions">
            Nhấn nút "Thêm điểm đánh dấu" tại vị trí kết thúc mỗi câu hỏi.
            Bạn có thể tua đi tua lại để nghe lại và điều chỉnh vị trí chính xác.
          </p>
          <p className="instructions important">
            <strong>Lưu ý:</strong> Câu hỏi đầu tiên luôn bắt đầu từ thời điểm 0s. Điểm đánh dấu đầu tiên sẽ là thời điểm bắt đầu của câu hỏi thứ 2.
          </p>
          {/* Keyboard shortcuts button removed */}
        </div>

        <div className="audio-timeline-content">
          {error && (
            <div className="error-message" data-type={error.includes('Đã tải') ? 'success' : 'error'}>
              {error}
            </div>
          )}

          {/* Save message moved to fixed overlay */}

          {/* First question marker */}
          <div className="first-question-marker">
            <div className="marker-line"></div>
            <div className="marker-label">Câu hỏi đầu tiên (0s)</div>
          </div>

          {/* Audio visualization section */}
          <div className="timeline-container-wrapper" ref={timelineContainerRef}>
            {/* Waveform visualization for detailed analysis */}
            <div className="waveform-container" ref={waveformRef} onClick={handleSeek}></div>

            {/* Simple progress bar for easier marker placement */}
            <div className="progress-container" onClick={handleSeek} ref={progressRef}>
              <div
                className="progress-bar"
                style={{ width: `${(currentTime / duration) * 100}%` }}
              />
              {/* Custom markers overlay */}
              <div className="markers-overlay" ref={markersOverlayRef}>
                {markers
                  .sort((a, b) => a.time - b.time)
                  .map((marker) => (
                    <div
                      key={`marker-${marker.id}-${marker.time}`}
                      className="timeline-marker"
                      style={{
                        left: `${(marker.time / duration) * 100}%`,
                        backgroundColor: marker.color
                      }}
                      data-active={marker.id === draggedMarker}
                      onClick={(e) => handleMarkerClick(marker.time, e)}
                      onMouseDown={(e) => handleMarkerDragStart(marker.id, e)}
                      onTouchStart={(e) => {
                        // Convert touch event to mouse-like event
                        const touch = e.touches[0];
                        const mouseEvent = {
                          clientX: touch.clientX,
                          clientY: touch.clientY,
                          preventDefault: () => e.preventDefault(),
                          stopPropagation: () => e.stopPropagation()
                        } as React.MouseEvent;
                        handleMarkerDragStart(marker.id, mouseEvent);
                      }}
                      title={`${marker.label} - ${formatTime(marker.time)}`}
                    />
                  ))}
              </div>
            </div>
          </div>

          {/* Drag tooltip - positioned at mouse cursor */}
          {dragTooltip && (
            <div
              className="drag-tooltip"
              style={{
                left: `${dragTooltip.x}px`,
                top: `${dragTooltip.y}px`,
                position: 'fixed'
              }}
            >
              {formatTime(dragTooltip.time)}
            </div>
          )}

          <div className="timeline-controls">
            <div className="control-buttons">
              <button
                className="control-button"
                onClick={handlePlayPause}
                disabled={isLoading}
                title="Phát/Tạm dừng (P)"
              >
                {isLoading ? (
                  <span className="spinner" />
                ) : isPlaying ? (
                  <FiPause size={20} />
                ) : (
                  <FiPlay size={20} />
                )}
                {isLoading ? 'Đang tải...' : isPlaying ? 'Tạm dừng' : 'Phát'}
              </button>

              <button
                className="control-button"
                onClick={handleAddMarker}
                disabled={isLoading}
                title="Thêm điểm đánh dấu"
              >
                <FiPlus size={16} /> Thêm điểm đánh dấu
              </button>



              {/* Zoom and skip buttons removed */}
            </div>

            <div className="time-display">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>



          <div className="markers-list">
            <h3>Danh sách các điểm đánh dấu:</h3>
            {markers.length === 0 ? (
              <p className="no-markers">Chưa có điểm đánh dấu nào. Hãy thêm điểm đánh dấu bằng cách nhấn nút "Thêm điểm đánh dấu".</p>
            ) : (
              <div className="markers-table">
                <div className="markers-header">
                  <div className="marker-col">Câu hỏi</div>
                  <div className="marker-col">Thời điểm</div>
                  <div className="marker-col">Thao tác</div>
                </div>
                {/* Câu hỏi đầu tiên luôn bắt đầu từ 0s và không thể chỉnh sửa */}
                <div className="marker-row first-question-row">
                  <div className="marker-col">
                    <span className="marker-label" style={{ color: '#4a9eff' }}>
                      Câu 1
                    </span>
                  </div>
                  <div className="marker-col">
                    <div className="marker-time-container">
                      <span className="marker-time" title="Câu hỏi đầu tiên luôn bắt đầu từ 0s">
                        {formatTime(0)}
                      </span>
                    </div>
                  </div>
                  <div className="marker-col">
                    <span className="disabled-action">Không thể chỉnh sửa</span>
                  </div>
                </div>
                {/* Các điểm đánh dấu cho các câu hỏi tiếp theo */}
                {markers
                  .sort((a, b) => a.time - b.time)
                  .map((marker, index) => (
                    <div key={marker.id} className="marker-row">
                      <div className="marker-col">
                        <span
                          className="marker-label"
                          style={{ color: marker.color }}
                        >
                          Câu {index + 2}
                        </span>
                      </div>
                      <div className="marker-col">
                        <div className="marker-time-container">
                          <span
                            className="marker-time"
                            onClick={(e) => handleMarkerClick(marker.time, e)}
                            title="Nhấn để chuyển đến vị trí này"
                          >
                            {formatTime(marker.time)}
                          </span>
                          <div className="marker-time-input-wrapper">
                            <input
                              type="number"
                              className={`marker-time-input ${markerErrors[marker.id] ? 'has-error' : ''}`}
                              value={marker.time.toFixed(2)}
                              key={`input-${marker.id}-${marker.time}`} // Force re-render when marker time changes
                              min="0"
                              max={duration.toFixed(2)}
                              step="0.01"
                            onFocus={() => {
                              // Set this marker as the active one being edited
                              setDraggedMarker(marker.id);
                              // Seek to this position
                              if (wavesurferRef.current) {
                                wavesurferRef.current.seekTo(marker.time / duration);
                                setCurrentTime(marker.time);
                              }
                            }}
                            onChange={(e) => {
                              // Since we're using a controlled input, we need to update the marker
                              // as the user types to keep the input responsive
                              const newTime = parseFloat(e.target.value);
                              if (!isNaN(newTime)) {
                                // We're not calling handleMarkerTimeUpdate here because we don't want
                                // to trigger validation or update the timeline yet
                                // Instead, we're just updating the marker in state temporarily
                                const updatedMarkers = markers.map(m => {
                                  if (m.id === marker.id) {
                                    return { ...m, time: newTime };
                                  }
                                  return m;
                                });
                                setMarkers(updatedMarkers); // Don't use updateMarkers here to avoid too many unsaved changes notifications
                              }
                            }}
                            onBlur={(e) => {
                              // Get the new time value
                              const newTime = parseFloat(e.target.value);

                              // Validate the new time
                              if (isNaN(newTime) || newTime < 0) {
                                // Reset to current marker time if invalid
                                // Set error for this specific marker
                                setMarkerErrors(prev => ({
                                  ...prev,
                                  [marker.id]: 'Thời gian không hợp lệ'
                                }));
                                // Clear error after 5 seconds
                                setTimeout(() => {
                                  setMarkerErrors(prev => {
                                    const newErrors = {...prev};
                                    delete newErrors[marker.id];
                                    return newErrors;
                                  });
                                }, 5000);

                                // Reset marker to its previous valid time
                                // This will update the input field as well since it's controlled
                                const originalMarker = markers.find(m => m.id === marker.id);
                                if (originalMarker) {
                                  const resetMarkers = markers.map(m => {
                                    if (m.id === marker.id) {
                                      return originalMarker;
                                    }
                                    return m;
                                  });
                                  setMarkers(resetMarkers);
                                }
                              } else if (newTime > duration) {
                                // Reset to current marker time if too large
                                // Set error for this specific marker
                                setMarkerErrors(prev => ({
                                  ...prev,
                                  [marker.id]: `Thời gian vượt quá độ dài file (${formatTime(duration)})`
                                }));
                                // Clear error after 5 seconds
                                setTimeout(() => {
                                  setMarkerErrors(prev => {
                                    const newErrors = {...prev};
                                    delete newErrors[marker.id];
                                    return newErrors;
                                  });
                                }, 5000);

                                // Reset marker to its previous valid time
                                const originalMarker = markers.find(m => m.id === marker.id);
                                if (originalMarker) {
                                  const resetMarkers = markers.map(m => {
                                    if (m.id === marker.id) {
                                      return originalMarker;
                                    }
                                    return m;
                                  });
                                  setMarkers(resetMarkers);
                                }
                              } else {
                                // Now that the user has finished editing, validate and update properly
                                // This will update the marker position on the timeline
                                // and also update the marker in the markers array with proper validation
                                // Đảm bảo thời gian là số nguyên
                                handleMarkerTimeUpdate(marker.id, Math.round(newTime));
                              }

                              // Clear dragged marker
                              setDraggedMarker(null);
                            }}
                            title="Chỉnh sửa thời gian (nhấn ra ngoài để cập nhật)"
                          />
                            {markerErrors[marker.id] && (
                              <div className="marker-error-message">{markerErrors[marker.id]}</div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="marker-col">
                        <button
                          className="remove-marker-button"
                          onClick={() => handleRemoveMarker(marker.id)}
                        >
                          Xóa
                        </button>
                      </div>
                    </div>
                  ))
                }
              </div>
            )}
          </div>

          <div className="timeline-actions">
            <button
              className="save-button"
              onClick={handleSave}
              disabled={markers.length === 0 || isLoading || isSaving}
            >
              {isSaving ? 'Đang lưu...' : hasUnsavedChanges ? 'Lưu thay đổi' : 'Lưu thời lượng'}
            </button>
            <button
              className="cancel-button"
              onClick={handleClose}
              disabled={isLoading || isSaving}
            >
              Hủy
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioTimelineRecorder;