import React, { useState, useEffect, useRef } from 'react';
import { Question } from '../QuestionArea';
import './ExamCreator.css';
import { FiPlus, FiEdit, FiTrash2, FiArrowUp, FiArrowDown, FiSave, FiX, FiCheckCircle, FiPlay, FiClock } from 'react-icons/fi';
import { getAudioDuration, calculateTotalListeningDuration } from '../../lib/utils';

interface ExamCreatorProps {
  onSaveExam: (exam: any) => void;
  examId?: number;
}

// Question type for our form
interface QuestionFormData {
  id: number;
  type: 'reading' | 'listening';
  text: string;
  subText: string;
  hasImageOptions: boolean;
  correctAnswer: number; // ID của đáp án đúng
  audioUrl?: string;    // URL to audio file for listening questions
  audioDuration?: number; // Thời lượng của file âm thanh tính bằng giây
  options: Array<{
    id: number;
    text: string;
    imageUrl: string;
  }>;
}

const ExamCreator: React.FC<ExamCreatorProps> = ({ onSaveExam, examId }) => {
  const [examTitle, setExamTitle] = useState('');
  const [examDescription, setExamDescription] = useState('');
  const [readingTime, setReadingTime] = useState(40);
  const [listeningTime, setListeningTime] = useState(30);
  const [isActive, setIsActive] = useState(true);
  const [questions, setQuestions] = useState<QuestionFormData[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionFormData | null>(null);
  const [isEditingQuestion, setIsEditingQuestion] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Fetch exam data if examId is provided
  useEffect(() => {
    if (examId) {
      const fetchExam = async () => {
        try {
          setLoading(true);
          const response = await fetch(`/api/exams/${examId}`);
          if (!response.ok) {
            throw new Error('Failed to fetch exam');
          }
          const examData = await response.json();
          
          // Set form values
          setExamTitle(examData.title);
          setExamDescription(examData.description || '');
          setReadingTime(examData.readingTime);
          setListeningTime(examData.listeningTime);
          setIsActive(examData.isActive === undefined ? true : examData.isActive);
          
          // Parse questions from JSON and format them for the form
          if (examData.questions) {
            const parsedQuestions = typeof examData.questions === 'string' 
              ? JSON.parse(examData.questions) 
              : examData.questions;
            
            // Convert to QuestionFormData format
            const formattedQuestions = parsedQuestions.map((q: any) => ({
              id: q.id,
              type: q.type,
              text: q.text,
              subText: q.subText || '',
              hasImageOptions: q.hasImageOptions || false,
              correctAnswer: q.correctAnswer || 1, // Mặc định đáp án 1 là đúng
              audioUrl: q.audioUrl || '',  // Fix: Lưu URL audio
              options: q.options.map((opt: any) => ({
                id: opt.id,
                text: opt.text || '',
                imageUrl: opt.imageUrl || '',
              })),
            }));
            
            // Thêm tất cả câu hỏi vào danh sách, không đặt câu hỏi hiện tại
            if (formattedQuestions.length > 0) {
              setQuestions(formattedQuestions);
              setIsEditingQuestion(false);
              setCurrentQuestion(null);
            }
          }
          
          setError(null);
        } catch (err) {
          console.error('Error fetching exam:', err);
          setError('Không thể tải thông tin đề thi. Vui lòng thử lại sau.');
        } finally {
          setLoading(false);
        }
      };
      
      fetchExam();
    }
  }, [examId]);

  // Khởi tạo câu hỏi mới
  const createNewQuestion = () => {
    setCurrentQuestion({
      id: questions.length > 0 ? Math.max(...questions.map(q => q.id)) + 1 : 1,
      type: 'reading',
      text: '',
      subText: '',
      hasImageOptions: false,
      correctAnswer: 1, // Mặc định đáp án 1 là đúng
      options: [
        { id: 1, text: '', imageUrl: '' },
        { id: 2, text: '', imageUrl: '' },
        { id: 3, text: '', imageUrl: '' },
        { id: 4, text: '', imageUrl: '' },
      ],
    });
    setIsEditingQuestion(true);
  };

  const handleQuestionChange = (field: string, value: any) => {
    if (!currentQuestion) return;
    
    // Ngăn chặn chỉnh sửa ID câu hỏi
    if (field === 'id') {
      console.log('ID câu hỏi không thể thay đổi');
      return;
    }
    
    setCurrentQuestion((prev) => ({
      ...prev!,
      [field]: value,
    }));
  };

  const handleOptionChange = (optionId: number, field: string, value: string) => {
    if (!currentQuestion) return;
    
    setCurrentQuestion((prev) => {
      const updatedOptions = prev!.options.map((option) =>
        option.id === optionId ? { ...option, [field]: value } : option
      );
      return { ...prev!, options: updatedOptions };
    });
  };

  const removeQuestion = (id: number) => {
    setQuestions((prev) => prev.filter((q) => q.id !== id));
  };

  const editQuestion = (id: number) => {
    const questionToEdit = questions.find((q) => q.id === id);
    if (questionToEdit) {
      // Tạo bản sao sâu của câu hỏi để tránh tham chiếu trực tiếp
      setCurrentQuestion(JSON.parse(JSON.stringify(questionToEdit)));
      setIsEditingQuestion(true);
      // Không xóa câu hỏi khỏi danh sách cho đến khi người dùng lưu thay đổi
    }
  };

  const saveQuestion = () => {
    if (!currentQuestion) return;
    
    if (!currentQuestion.text) {
      alert('Vui lòng nhập nội dung câu hỏi');
      return;
    }
    
    // Kiểm tra các đáp án
    if (currentQuestion.hasImageOptions) {
      const emptyImages = currentQuestion.options.filter(opt => !opt.imageUrl);
      if (emptyImages.length > 0) {
        alert('Vui lòng thêm URL hình ảnh cho tất cả các đáp án');
        return;
      }
    } else {
      const emptyTexts = currentQuestion.options.filter(opt => !opt.text);
      if (emptyTexts.length > 0) {
        alert('Vui lòng nhập nội dung cho tất cả các đáp án');
        return;
      }
    }
    
    // Kiểm tra ID câu hỏi có bị trùng không
    const existingQuestionIndex = questions.findIndex(q => q.id === currentQuestion.id);
    
    if (existingQuestionIndex !== -1) {
      if (!window.confirm(`Câu hỏi với ID ${currentQuestion.id} đã tồn tại. Bạn có muốn thay thế không?`)) {
        return;
      }
      // Thay thế câu hỏi hiện có
      const updatedQuestions = [...questions];
      updatedQuestions[existingQuestionIndex] = {...currentQuestion};
      setQuestions(updatedQuestions);
    } else {
      // Thêm câu hỏi mới vào danh sách
      setQuestions(prev => [...prev, {...currentQuestion}]);
    }
    
    // Reset form câu hỏi
    setCurrentQuestion(null);
    setIsEditingQuestion(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Kiểm tra nếu đang chỉnh sửa câu hỏi, nhắc người dùng lưu trước
    if (isEditingQuestion && currentQuestion) {
      if (!window.confirm('Bạn vẫn đang chỉnh sửa một câu hỏi. Bạn muốn tiếp tục lưu đề thi và bỏ qua thay đổi cho câu hỏi này?')) {
        return;
      }
    }
    
    // Calculate duration based on reading and listening time
    const calculatedDuration = readingTime + listeningTime;
    
    // Create the exam object
    const exam = {
      id: examId, // Include ID if editing an existing exam
      title: examTitle,
      description: examDescription,
      readingTime,
      listeningTime,
      duration: calculatedDuration,
      isActive,
      questions: questions.map(q => {
        // Transform the question data to match our application's format
        if (q.hasImageOptions) {
          return {
            id: q.id,
            type: q.type,
            text: q.text,
            subText: q.subText,
            hasImageOptions: true,
            correctAnswer: q.correctAnswer, // Thêm đáp án đúng
            audioUrl: q.audioUrl, // URL âm thanh cho câu hỏi listening
            options: q.options.map(opt => ({
              id: opt.id,
              imageUrl: opt.imageUrl
            }))
          };
        } else {
          return {
            id: q.id,
            type: q.type,
            text: q.text,
            subText: q.subText,
            correctAnswer: q.correctAnswer, // Thêm đáp án đúng
            audioUrl: q.audioUrl, // URL âm thanh cho câu hỏi listening
            options: q.options.map(opt => ({
              id: opt.id,
              text: opt.text
            }))
          };
        }
      }),
      createdBy: 1, // Assuming admin ID
    };
    
    try {
      // If editing an existing exam
      if (examId) {
        const response = await fetch(`/api/exams/${examId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exam),
        });
        
        if (!response.ok) {
          throw new Error('Failed to update exam');
        }
        
        const updatedExam = await response.json();
        console.log('Exam saved:', updatedExam);
        onSaveExam(updatedExam);
      } 
      // Creating a new exam
      else {
        const response = await fetch('/api/exams', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exam),
        });
        
        if (!response.ok) {
          throw new Error('Failed to create exam');
        }
        
        const newExam = await response.json();
        console.log('Exam saved:', newExam);
        onSaveExam(newExam);
      }
    } catch (err) {
      console.error('Error saving exam:', err);
      setError('Có lỗi xảy ra khi lưu đề thi. Vui lòng thử lại sau.');
    }
  };

  if (loading) {
    return (
      <div className="exam-list-loading">
        <div className="spinner"></div>
        <p>Đang tải thông tin đề thi...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="exam-list-error">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Thử lại</button>
      </div>
    );
  }

  return (
    <div className="admin-creator">
      <h2>{examId ? 'Chỉnh sửa đề thi' : 'Tạo đề thi EPS-TOPIK mới'}</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="creator-section">
          <h3>Thông tin đề thi</h3>
          <div className="form-group">
            <label>Tiêu đề</label>
            <input
              type="text"
              value={examTitle}
              onChange={(e) => setExamTitle(e.target.value)}
              required
              placeholder="Nhập tiêu đề đề thi"
            />
          </div>
          <div className="form-group">
            <label>Mô tả</label>
            <textarea
              value={examDescription}
              onChange={(e) => setExamDescription(e.target.value)}
              placeholder="Nhập mô tả đề thi"
            />
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Thời gian phần Reading (phút)</label>
              <input
                type="number"
                value={readingTime}
                onChange={(e) => setReadingTime(parseInt(e.target.value))}
                min="1"
                max="120"
                required
              />
            </div>
            <div className="form-group">
              <label>Thời gian phần Listening (phút)</label>
              <input
                type="number"
                value={listeningTime}
                onChange={(e) => setListeningTime(parseInt(e.target.value))}
                min="1"
                max="120"
                required
              />
            </div>
            <div className="form-group checkbox-group">
              <label>
                <input 
                  type="checkbox" 
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)} 
                />
                Đề thi đang hoạt động
              </label>
            </div>
          </div>
        </div>
        
        {/* Phần quản lý danh sách câu hỏi */}
        <div className="creator-section">
          <div className="section-header">
            <h3>Danh sách câu hỏi ({questions.length})</h3>
            <button 
              type="button" 
              className="button-with-text primary" 
              onClick={createNewQuestion}
            >
              <FiPlus size={18} /> Thêm câu hỏi mới
            </button>
          </div>
          
          {questions.length === 0 ? (
            <div className="empty-questions">
              <p>Chưa có câu hỏi nào. Nhấn "Thêm câu hỏi mới" để bắt đầu tạo câu hỏi.</p>
            </div>
          ) : (
            <div className="questions-table-container">
              <table className="questions-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Loại</th>
                    <th>Câu hỏi</th>
                    <th>Đáp án</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {questions.map((q, index) => (
                    <tr key={q.id}>
                      <td>{q.id}</td>
                      <td>
                        <span className={`question-type ${q.type}`}>
                          {q.type === 'reading' ? 'Reading' : 'Listening'}
                        </span>
                      </td>
                      <td className="question-text-cell">
                        <div className="question-text">{q.text}</div>
                        {q.subText && (
                          <div className="question-subtext">{q.subText}</div>
                        )}
                      </td>
                      <td className="options-count">
                        {q.options.length} đáp án
                        {q.hasImageOptions && <span className="image-icon"> (Hình ảnh)</span>}
                        <div>Đáp án đúng: {q.correctAnswer}</div>
                        {q.type === 'listening' && q.audioUrl && (
                          <div className="audio-info">
                            <span className="audio-icon">🎵</span> Có audio
                          </div>
                        )}
                      </td>
                      <td className="question-actions">
                        <button
                          type="button"
                          className="icon-button"
                          onClick={() => editQuestion(q.id)}
                        >
                          <FiEdit size={16} />
                          <span className="tooltip">Sửa</span>
                        </button>
                        <button
                          type="button"
                          className="icon-button danger"
                          onClick={() => removeQuestion(q.id)}
                        >
                          <FiTrash2 size={16} />
                          <span className="tooltip">Xóa</span>
                        </button>
                        {index > 0 && (
                          <button
                            type="button"
                            className="icon-button"
                            onClick={() => {
                              const newQuestions = [...questions];
                              [newQuestions[index], newQuestions[index - 1]] = 
                                [newQuestions[index - 1], newQuestions[index]];
                              setQuestions(newQuestions);
                            }}
                          >
                            <FiArrowUp size={16} />
                            <span className="tooltip">Di chuyển lên</span>
                          </button>
                        )}
                        {index < questions.length - 1 && (
                          <button
                            type="button"
                            className="icon-button"
                            onClick={() => {
                              const newQuestions = [...questions];
                              [newQuestions[index], newQuestions[index + 1]] = 
                                [newQuestions[index + 1], newQuestions[index]];
                              setQuestions(newQuestions);
                            }}
                          >
                            <FiArrowDown size={16} />
                            <span className="tooltip">Di chuyển xuống</span>
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
        
        <div className="form-actions">
          <button type="submit" className="button-with-text primary">
            <FiSave size={18} /> Lưu đề thi
          </button>
        </div>
      </form>

      {/* Form thêm/sửa câu hỏi - hiển thị dạng màn hình riêng */}
      {isEditingQuestion && currentQuestion && (
        <div className="question-editor-overlay">
          <div className="question-editor-modal">
            <div className="question-editor-header">
              <h2>Nội dung câu hỏi</h2>
              <button 
                type="button" 
                className="icon-button danger"
                onClick={() => {
                  if (window.confirm('Bạn có chắc muốn hủy thay đổi cho câu hỏi này không?')) {
                    setCurrentQuestion(null);
                    setIsEditingQuestion(false);
                  }
                }}
              >
                <FiX size={18} />
                <span className="tooltip">Đóng</span>
              </button>
            </div>
            
            <div className="question-editor-content">
              <div className="form-row">
                <div className="form-group full-width">
                  <label>Phần thi</label>
                  <select
                    value={currentQuestion.type}
                    onChange={(e) => handleQuestionChange('type', e.target.value as 'reading' | 'listening')}
                  >
                    <option value="reading">Reading</option>
                    <option value="listening">Listening</option>
                  </select>
                </div>
              </div>
              
              <div className="form-group">
                <label>Câu hỏi</label>
                <textarea
                  value={currentQuestion.text}
                  onChange={(e) => handleQuestionChange('text', e.target.value)}
                  required
                  placeholder="Nhập câu hỏi"
                />
              </div>
              
              <div className="form-group">
                <label>Văn bản phụ (nếu có)</label>
                <textarea
                  value={currentQuestion.subText}
                  onChange={(e) => handleQuestionChange('subText', e.target.value)}
                  placeholder="Nhập văn bản phụ (nếu có)"
                />
              </div>
              
              {/* Audio URL field for listening questions */}
              {currentQuestion.type === 'listening' && (
                <div className="form-group">
                  <label>URL âm thanh (Google Drive)</label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    <input
                      type="text"
                      value={currentQuestion.audioUrl || ''}
                      onChange={async (e) => {
                        const newAudioUrl = e.target.value;
                        handleQuestionChange('audioUrl', newAudioUrl);
                        
                        // Tự động phát hiện thời lượng nếu URL không trống và đã thay đổi
                        if (newAudioUrl && newAudioUrl !== currentQuestion.audioUrl) {
                          try {
                            // Hiển thị thông báo đang tính toán thời lượng
                            const durationStatus = document.getElementById('audio-duration-status');
                            if (durationStatus) {
                              durationStatus.textContent = 'Đang phát hiện thời lượng...';
                            }
                            
                            // Lấy thời lượng file âm thanh
                            const duration = await getAudioDuration(newAudioUrl);
                            
                            // Cập nhật thời lượng cho câu hỏi
                            if (duration > 0) {
                              handleQuestionChange('audioDuration', duration);
                              if (durationStatus) {
                                durationStatus.textContent = `Thời lượng: ${duration} giây`;
                              }
                            } else {
                              if (durationStatus) {
                                durationStatus.textContent = 'Không thể phát hiện thời lượng';
                              }
                            }
                          } catch (error) {
                            console.error('Lỗi khi phát hiện thời lượng:', error);
                          }
                        }
                      }}
                      placeholder="Nhập URL tệp âm thanh (ví dụ: https://drive.google.com/file/d/...)"
                      style={{ flex: 1 }}
                    />
                    <button
                      type="button"
                      className={`icon-button primary ${!currentQuestion.audioUrl ? 'disabled' : ''}`}
                      onClick={() => {
                        if (audioRef.current && currentQuestion.audioUrl) {
                          let audioUrl = currentQuestion.audioUrl;
                          
                          // Nếu là Google Drive URL, chuyển đổi sang proxy server
                          if (audioUrl.includes('drive.google.com/file/d/')) {
                            const fileId = audioUrl.match(/\/d\/([^\/]+)/)?.[1];
                            if (fileId) {
                              audioUrl = `/api/proxy/gdrive?id=${fileId}`;
                            }
                          }
                          
                          audioRef.current.src = audioUrl;
                          audioRef.current.play().catch(err => {
                            console.error('Không thể phát âm thanh:', err);
                            alert('Không thể phát file âm thanh. Vui lòng kiểm tra lại URL và đảm bảo đó là URL của Google Drive hợp lệ.');
                          });
                        }
                      }}
                      disabled={!currentQuestion.audioUrl}
                    >
                      <FiPlay size={18} />
                    </button>
                    <button
                      type="button"
                      className={`icon-button info ${!currentQuestion.audioUrl ? 'disabled' : ''}`}
                      onClick={async () => {
                        if (currentQuestion.audioUrl) {
                          try {
                            // Hiển thị thông báo đang tính toán thời lượng
                            const durationStatus = document.getElementById('audio-duration-status');
                            if (durationStatus) {
                              durationStatus.textContent = 'Đang phát hiện thời lượng...';
                            }
                            
                            // Lấy thời lượng file âm thanh
                            const duration = await getAudioDuration(currentQuestion.audioUrl);
                            
                            // Cập nhật thời lượng cho câu hỏi
                            if (duration > 0) {
                              handleQuestionChange('audioDuration', duration);
                              if (durationStatus) {
                                durationStatus.textContent = `Thời lượng: ${duration} giây`;
                              }
                            } else {
                              if (durationStatus) {
                                durationStatus.textContent = 'Không thể phát hiện thời lượng';
                              }
                            }
                          } catch (error) {
                            console.error('Lỗi khi phát hiện thời lượng:', error);
                          }
                        }
                      }}
                      disabled={!currentQuestion.audioUrl}
                      title="Phát hiện thời lượng file âm thanh"
                    >
                      <FiClock size={18} />
                    </button>
                  </div>
                  <div className="form-hint">
                    Nhập URL của tệp âm thanh từ Google Drive. URL có dạng: https://drive.google.com/file/d/FILE_ID/view
                  </div>
                  <div id="audio-duration-status" className="audio-duration-status">
                    {currentQuestion.audioDuration 
                      ? `Thời lượng: ${currentQuestion.audioDuration} giây` 
                      : 'Chưa phát hiện thời lượng'}
                  </div>
                  <audio ref={audioRef} style={{ display: 'none' }} />
                </div>
              )}
              
              <div className="form-group checkbox-group">
                <label>
                  <input 
                    type="checkbox" 
                    checked={currentQuestion.hasImageOptions}
                    onChange={(e) => handleQuestionChange('hasImageOptions', e.target.checked)} 
                  />
                  Sử dụng hình ảnh cho các đáp án
                </label>
              </div>

              <div className="options-section">
                <h4>Các đáp án</h4>
                {currentQuestion.options.map((option) => (
                  <div key={option.id} className="option-item">
                    <div className="option-header">
                      <div className="option-header-left">
                        <input 
                          type="radio" 
                          id={`correct-option-${option.id}`}
                          name="correctAnswer" 
                          value={option.id} 
                          checked={currentQuestion.correctAnswer === option.id}
                          onChange={() => handleQuestionChange('correctAnswer', option.id)}
                        />
                        <label htmlFor={`correct-option-${option.id}`} className="correct-option-label">
                          <strong>Đáp án {option.id}</strong>
                          {currentQuestion.correctAnswer === option.id && 
                            <span className="correct-label"> (Đáp án đúng)</span>
                          }
                        </label>
                      </div>
                    </div>
                    <div className="option-content">
                      {currentQuestion.hasImageOptions ? (
                        <div className="form-group">
                          <label>URL Hình ảnh</label>
                          <input
                            type="text"
                            value={option.imageUrl}
                            onChange={(e) =>
                              handleOptionChange(option.id, 'imageUrl', e.target.value)
                            }
                            placeholder="Nhập URL hình ảnh"
                            required={currentQuestion.hasImageOptions}
                          />
                          {option.imageUrl && (
                            <div className="image-preview">
                              <img src={option.imageUrl} alt={`Preview ${option.id}`} width="100" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="form-group">
                          <label>Nội dung</label>
                          <input
                            type="text"
                            value={option.text}
                            onChange={(e) =>
                              handleOptionChange(option.id, 'text', e.target.value)
                            }
                            placeholder="Nhập nội dung đáp án"
                            required={!currentQuestion.hasImageOptions}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="button-group">
                <button 
                  type="button" 
                  className="button-with-text primary" 
                  onClick={saveQuestion}
                >
                  <FiSave size={18} /> Lưu câu hỏi
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamCreator;