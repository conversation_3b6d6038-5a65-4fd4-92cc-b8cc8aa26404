.audio-timeline-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.audio-timeline-modal {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 95%;
  max-width: 1000px;
  max-height: 95vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.audio-timeline-header {
  margin-bottom: 24px;
  position: relative;
}

.audio-timeline-header h2 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.5rem;
}

.instructions {
  color: #666;
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.5;
}

.instructions.important {
  background-color: #fff8e1;
  border-left: 4px solid #ffc107;
  padding: 8px 12px;
  margin-bottom: 16px;
  color: #5d4037;
}

/* Keyboard shortcuts removed */

/* Timeline container */
/* First question marker */
.first-question-marker {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 8px;
  background-color: #e3f2fd;
  border-radius: 4px;
  border-left: 4px solid #2196f3;
}

.marker-line {
  width: 16px;
  height: 16px;
  background-color: #2196f3;
  border-radius: 50%;
  margin-right: 8px;
}

.marker-label {
  font-size: 14px;
  font-weight: 500;
  color: #0d47a1;
}

.timeline-container-wrapper {
  width: 100%;
  position: relative;
  margin-bottom: 16px;
}

/* Waveform container - more compact */
.waveform-container {
  width: 100%;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  margin-bottom: 4px;
  overflow: hidden; /* Ensure the waveform stays within container */
}

/* Progress bar - simpler visualization */
.progress-container {
  position: relative;
  width: 100%;
  height: 12px;
  background-color: #eee;
  border-radius: 4px;
  cursor: pointer;
  margin: 8px 0;
}

.progress-bar {
  position: absolute;
  height: 100%;
  background-color: #4a9eff;
  border-radius: 4px;
  transition: width 0.1s linear;
}

/* Custom markers overlay */
.markers-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 12px;
  pointer-events: none; /* Allow clicks to pass through to progress bar */
}

.timeline-marker {
  position: absolute;
  width: 4px;
  height: 16px;
  top: -2px;
  transform: translateX(-50%);
  pointer-events: auto; /* Make markers clickable */
  cursor: grab;
  transition: width 0.2s, height 0.2s, top 0.2s, box-shadow 0.2s, left 0.05s linear;
  z-index: 10;
  border-radius: 1px;
  will-change: left, width, height, top; /* Optimize for animations */
}

.timeline-marker:hover {
  width: 6px;
  height: 20px;
  top: -4px;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

.timeline-marker:active,
.timeline-marker[data-active="true"] {
  cursor: grabbing;
  width: 6px;
  height: 20px;
  top: -4px;
  z-index: 20;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.7);
  transition: left 0.05s linear; /* Smoother movement during drag */
}

/* Drag tooltip */
.drag-tooltip {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  pointer-events: none;
  z-index: 1100;
  transform: translate(-50%, -100%);
  white-space: nowrap;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.drag-tooltip::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.8);
}

.timeline-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 12px;
}

.control-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #4a9eff;
  color: white;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
  min-width: 36px;
}

.control-button:hover {
  background-color: #00468C;
}

.control-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.time-display {
  font-size: 14px;
  color: #666;
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 6px 10px;
  border-radius: 4px;
  min-width: 100px;
  text-align: center;
}

/* Detection controls container */
.detection-controls-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 24px 0;
}

/* Detection controls */
.detection-controls {
  flex: 1;
  min-width: 300px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eee;
}

.detection-controls h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.detection-description {
  font-size: 13px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.detection-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group label {
  font-size: 14px;
  color: #666;
}

.setting-group input[type="range"] {
  width: 100%;
  cursor: pointer;
}

.setting-group span {
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

/* Detection buttons */
.detect-button {
  width: 100%;
  padding: 10px;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.detect-button.silence {
  background-color: #4CAF50;
}

.detect-button.silence:hover {
  background-color: #388E3C;
}

.detect-button.silence:disabled {
  background-color: #A5D6A7;
  cursor: not-allowed;
}

/* Space mode toggle */
.space-mode-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #4a9eff;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}



/* Markers list */
.markers-list {
  margin-top: 24px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
}

.markers-list h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.no-markers {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px 0;
}

.markers-table {
  width: 100%;
  border-collapse: collapse;
}

.markers-header {
  display: flex;
  font-weight: bold;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
  margin-bottom: 8px;
  color: #333;
}

.marker-row {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.first-question-row {
  background-color: #f5f5f5;
  border-left: 4px solid #4a9eff;
  padding-left: 4px;
}

.disabled-action {
  color: #999;
  font-style: italic;
  font-size: 0.9em;
}

.marker-row:last-child {
  border-bottom: none;
}

.marker-col {
  flex: 1;
  padding: 0 8px;
}

.marker-col:first-child {
  flex: 0.8;
}

.marker-col:last-child {
  flex: 0.5;
  text-align: right;
}

.marker-label {
  font-size: 14px;
  font-weight: 500;
}

.marker-time-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.marker-time-input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.marker-time {
  font-size: 14px;
  color: #666;
  font-family: monospace;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.marker-time:hover {
  background-color: #f0f0f0;
  color: #333;
}

.marker-time-input {
  width: 80px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  font-family: monospace;
  color: #333;
  text-align: center;
  transition: all 0.2s;
  margin-top: 4px;
  position: relative;
}

.marker-time-input:hover {
  border-color: #bbb;
}

.marker-time-input:focus {
  border-color: #4a9eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
  background-color: #f0f7ff;
}

.marker-error-message {
  color: #d32f2f;
  font-size: 12px;
  margin-top: 4px;
  animation: fadeIn 0.3s ease-in-out;
  white-space: nowrap;
  font-weight: 500;
}

.marker-time-input.has-error {
  border-color: #d32f2f;
  background-color: #fff8f8;
}

.marker-time-input.has-error:focus {
  border-color: #d32f2f;
  box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.2);
}

/* Add a highlight effect for the marker being edited */
.timeline-marker[data-active="true"] {
  width: 4px !important;
  height: 18px !important;
  top: -3px !important;
  z-index: 20 !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
}

.remove-marker-button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background-color: #ff4a4a;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.remove-marker-button:hover {
  background-color: #cc0000;
}

.timeline-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.save-button {
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  background-color: #4a9eff;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  font-weight: 500;
  min-width: 140px;
}

.save-button:hover {
  background-color: #00468C;
}

.save-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Style for save button when there are unsaved changes */
.save-button:not(:disabled):not(:hover) {
  position: relative;
  overflow: hidden;
}

.save-button:not(:disabled):not(:hover)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.7);
  animation: pulse 1.5s infinite;
}

.cancel-button {
  padding: 10px 24px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #f5f5f5;
  border-color: #999;
}

.cancel-button:disabled {
  background-color: #f5f5f5;
  border-color: #ccc;
  color: #999;
  cursor: not-allowed;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  border-left: 4px solid #c62828;
  animation: fadeIn 0.3s ease-in-out;
  position: relative;
}

/* Success message style */
.error-message[data-type="success"] {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
}

/* Save message styles */
.save-message {
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 16px;
  animation: fadeIn 0.3s ease-in-out;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.save-message[data-type="success"] {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
  animation: successPulse 2s infinite;
}

@keyframes successPulse {
  0% { box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(46, 125, 50, 0); }
  100% { box-shadow: 0 0 0 0 rgba(46, 125, 50, 0); }
}

.save-message[data-type="error"] {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #c62828;
}

/* Fixed position save message overlay */
.save-message-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  pointer-events: none; /* Allow clicks to pass through */
}

.save-message-popup {
  padding: 20px 30px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: fadeInScale 0.3s ease-out;
  max-width: 80%;
  text-align: center;
  pointer-events: auto; /* Make the popup clickable */
}

.save-message-popup[data-type="success"] {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
  animation: successPulse 2s infinite;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-message-popup[data-type="success"]::before {
  content: '\2714'; /* Checkmark symbol */
  font-size: 24px;
  margin-right: 10px;
  font-weight: bold;
}

.save-message-popup[data-type="error"] {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #c62828;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-message-popup[data-type="error"]::before {
  content: '\26A0'; /* Warning symbol */
  font-size: 24px;
  margin-right: 10px;
  font-weight: bold;
}

@keyframes fadeInScale {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.save-message::before {
  content: '';
  margin-right: 8px;
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
}

.save-message[data-type="success"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232e7d32'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
}

.save-message[data-type="error"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23c62828'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}