import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Exam as SharedExam } from '@shared/schema';
import { FiEdit, FiPlayCircle, FiToggleLeft, FiToggleRight, FiChevronUp, FiChevronDown, FiStar } from 'react-icons/fi';
import ActionIconButton from '../common/ActionIconButton';

interface Exam extends SharedExam {
  isDefaultDemo?: boolean;
}

interface ExamListProps {
  onEditExam: (examId: number) => void;
}

type SortField = 'id' | 'title' | 'description' | 'duration' | 'isActive' | 'createdAt';
type SortDirection = 'asc' | 'desc';

const ExamList: React.FC<ExamListProps> = ({ onEditExam }) => {
  const navigate = useNavigate();
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  
  // Sorting
  const [sortField, setSortField] = useState<SortField>('id');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  useEffect(() => {
    const fetchExams = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/exams');
        if (!response.ok) {
          throw new Error('Failed to fetch exams');
        }
        const data = await response.json();
        setExams(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching exams:', err);
        setError('Không thể tải danh sách đề thi. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };

    fetchExams();
  }, []);

  const handleTakeExam = (examId: number) => {
    navigate(`/exam/${examId}`);
  };

  const handleEditExam = (examId: number) => {
    onEditExam(examId);
  };
  
  const toggleExamStatus = async (examId: number, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/exams/${examId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !currentStatus,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update exam status');
      }
      
      // Update local state after successful server update
      setExams(prevExams => prevExams.map(exam => 
        exam.id === examId ? { ...exam, isActive: !currentStatus } : exam
      ));
      
    } catch (err) {
      console.error('Error updating exam status:', err);
      alert('Có lỗi xảy ra khi cập nhật trạng thái đề thi. Vui lòng thử lại sau.');
    }
  };

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const filteredExams = exams
    .filter(exam => {
      // Filter by search term
      if (searchTerm && 
          !exam.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
          !exam.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      
      // Filter by status
      if (statusFilter === 'active' && !exam.isActive) return false;
      if (statusFilter === 'inactive' && exam.isActive) return false;
      
      return true;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'id':
          comparison = a.id - b.id;
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'description':
          comparison = (a.description || '').localeCompare(b.description || '');
          break;
        case 'duration':
          comparison = a.duration - b.duration;
          break;
        case 'isActive':
          comparison = (a.isActive ? 1 : 0) - (b.isActive ? 1 : 0);
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime();
          break;
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });

  if (loading) {
    return (
      <div className="exam-list-loading">
        <div className="spinner"></div>
        <p>Đang tải danh sách đề thi...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="exam-list-error">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Thử lại</button>
      </div>
    );
  }

  const renderSortIcon = (field: SortField) => {
    if (field !== sortField) return null;
    return sortDirection === 'asc' ? <FiChevronUp size={14} /> : <FiChevronDown size={14} />;
  };

  return (
    <div className="exam-list">
      {/* Header đã được di chuyển lên AdminPage */}
      
      <div className="exam-list-filters">
        <div className="search-container">
          <input 
            type="text" 
            placeholder="Tìm kiếm đề thi..." 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-container">
          <label htmlFor="status-filter">Trạng thái:</label>
          <select 
            id="status-filter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="status-filter"
          >
            <option value="all">Tất cả</option>
            <option value="active">Đang hoạt động</option>
            <option value="inactive">Không hoạt động</option>
          </select>
        </div>
      </div>
      
      {filteredExams.length === 0 ? (
        <div className="no-exams">
          <p>Không tìm thấy đề thi nào.</p>
        </div>
      ) : (
        <div className="exam-table-container">
          <table className="exam-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('id')} className="sortable">
                  ID {renderSortIcon('id')}
                </th>
                <th onClick={() => handleSort('title')} className="sortable">
                  Tiêu đề {renderSortIcon('title')}
                </th>
                <th onClick={() => handleSort('description')} className="sortable">
                  Mô tả {renderSortIcon('description')}
                </th>
                <th onClick={() => handleSort('duration')} className="sortable">
                  Thời gian {renderSortIcon('duration')}
                </th>
                <th onClick={() => handleSort('isActive')} className="sortable">
                  Trạng thái {renderSortIcon('isActive')}
                </th>
                <th onClick={() => handleSort('createdAt')} className="sortable">
                  Ngày tạo {renderSortIcon('createdAt')}
                </th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredExams.map(exam => (
                <tr key={exam.id} className={exam.isActive ? 'active-exam' : 'inactive-exam'}>
                  <td>{exam.id}</td>
                  <td>{exam.title} {exam.isDefaultDemo && <FiStar title="This is the public Try a Test exam" style={{ color: '#f39c12', marginLeft: 4, verticalAlign: 'middle' }} />}</td>
                  <td>{exam.description || '(Không có mô tả)'}</td>
                  <td>{exam.duration} phút</td>
                  <td>
                    <span className={`status-badge ${exam.isActive ? 'active' : 'inactive'}`}>
                      {exam.isActive ? 'Hoạt động' : 'Không hoạt động'}
                    </span>
                  </td>
                  <td>{new Date(exam.createdAt || '').toLocaleDateString('vi-VN')}</td>
                  <td>
                    <div className="action-buttons">
                      <ActionIconButton
                        icon={<FiEdit size={16} />}
                        tooltip="Chỉnh sửa"
                        onClick={() => handleEditExam(exam.id)}
                      />
                      <ActionIconButton
                        icon={<FiPlayCircle size={16} />}
                        tooltip="Thi thử"
                        onClick={() => handleTakeExam(exam.id)}
                        className="primary"
                      />
                      <ActionIconButton
                        icon={exam.isActive ? <FiToggleLeft size={16} /> : <FiToggleRight size={16} />}
                        tooltip={exam.isActive ? 'Vô hiệu hóa' : 'Kích hoạt'}
                        onClick={() => toggleExamStatus(exam.id, !!exam.isActive)}
                        className={exam.isActive ? 'warning' : 'success'}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ExamList;