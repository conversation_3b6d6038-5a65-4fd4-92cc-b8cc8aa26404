.audio-recorder-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.audio-recorder-modal {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.audio-recorder-header {
  margin-bottom: 20px;
  text-align: center;
}

.audio-recorder-header h2 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.5rem;
}

.instructions {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.audio-controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 24px;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #00468C;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.control-button:hover {
  background: #003366;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.duration-display {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 24px;
}

.current-duration {
  font-size: 1.2rem;
  font-weight: bold;
  color: #00468C;
  text-align: center;
  margin-bottom: 16px;
}

.recorded-durations {
  margin-top: 20px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
}

.recorded-durations::-webkit-scrollbar {
  width: 8px;
}

.recorded-durations::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.recorded-durations::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.recorded-durations::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.recorded-durations h3 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: #333;
}

.duration-item {
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.duration-item:last-child {
  border-bottom: none;
}

.recorder-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.save-button,
.cancel-button {
  padding: 8px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.save-button {
  background: #00468C;
  color: white;
}

.save-button:hover {
  background: #003366;
}

.save-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-button {
  background: #f5f5f5;
  color: #333;
}

.cancel-button:hover {
  background: #e0e0e0;
}

.spinner {
}

.seek-bar-container {
  width: 100%;
  margin: 20px 0;
  padding: 0 10px;
  position: relative;
}

.seek-bar {
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
  margin: 10px 0;
}

.seek-bar-progress {
  position: absolute;
  height: 100%;
  background: #00468C;
  border-radius: 3px;
  width: 0%;
  transition: width 0.1s linear;
}

.seek-bar-handle {
  position: absolute;
  width: 16px;
  height: 16px;
  background: #00468C;
  border-radius: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.1s ease;
}

.seek-bar-handle:hover {
  transform: translate(-50%, -50%) scale(1.2);
  background: #003366;
}

.seek-time {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  font-family: monospace;
} 