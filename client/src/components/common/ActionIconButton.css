.action-icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #3498db;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  position: relative;
  transition: background 0.2s, box-shadow 0.2s, filter 0.2s;
  margin-bottom: 0.25rem;
  outline: none;
}
.action-icon-button:disabled {
  background: #cbd5e0;
  color: #fff;
  cursor: not-allowed;
  filter: grayscale(0.5);
}
.action-icon-button:hover:not(:disabled),
.action-icon-button:focus-visible:not(:disabled) {
  background: #f39c12;
  color: #fff;
  filter: brightness(1.08) drop-shadow(0 2px 6px rgba(243,156,18,0.10));
  z-index: 2;
}

.action-icon-button .tooltip {
  visibility: hidden;
  opacity: 0;
  min-width: 80px;
  max-width: 220px;
  background: #222;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 6px 14px;
  position: absolute;
  left: 50%;
  bottom: 120%;
  transform: translateX(-50%);
  z-index: 99;
  font-size: 0.97rem;
  pointer-events: none;
  transition: opacity 0.2s;
  white-space: pre-line;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
.action-icon-button .tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: #222 transparent transparent transparent;
}
.action-icon-button:hover .tooltip,
.action-icon-button:focus .tooltip {
  visibility: visible;
  opacity: 1;
} 