import React from 'react';
import './ActionIconButton.css';

interface ActionIconButtonProps {
  icon: React.ReactNode;
  tooltip: string;
  onClick?: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

const ActionIconButton: React.FC<ActionIconButtonProps> = ({
  icon,
  tooltip,
  onClick,
  disabled = false,
  className = '',
  type = 'button',
}) => (
  <button
    type={type}
    className={`action-icon-button ${className}`}
    onClick={onClick}
    disabled={disabled}
    tabIndex={0}
    aria-label={tooltip}
  >
    {icon}
    <span className="tooltip">{tooltip}</span>
  </button>
);

export default ActionIconButton; 