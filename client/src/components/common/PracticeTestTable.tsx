import React, { useState } from 'react';
import { FiChevronUp, FiChevronDown, FiSearch, FiPlayCircle, FiFileText } from 'react-icons/fi';
import ActionIconButton from './ActionIconButton';
import './PracticeTestTable.css';

export interface PracticeTest {
  id: number;
  title: string;
  description?: string;
  duration: number;
  isActive: boolean;
  createdAt?: string;
}

interface PracticeTestTableProps {
  tests: PracticeTest[];
  onTakeTest?: (id: number) => void;
  onHistory?: (id: number) => void;
  showActions?: boolean; // for admin
  onEdit?: (id: number) => void;
  onDelete?: (id: number) => void;
}

type SortField = 'id' | 'title' | 'description' | 'duration' | 'isActive' | 'createdAt';
type SortDirection = 'asc' | 'desc';

const PracticeTestTable: React.FC<PracticeTestTableProps> = ({
  tests,
  onTakeTest,
  onHistory,
  showActions = false,
  onEdit,
  onDelete,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [sortField, setSortField] = useState<SortField>('id');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const renderSortIcon = (field: SortField) => {
    if (field !== sortField) return null;
    return sortDirection === 'asc' ? <FiChevronUp size={14} /> : <FiChevronDown size={14} />;
  };

  const filteredTests = tests
    .filter(test => {
      // Filter by search term
      if (searchTerm && 
          !test.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
          !test.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      
      // Filter by status
      if (statusFilter === 'active' && !test.isActive) return false;
      if (statusFilter === 'inactive' && test.isActive) return false;
      
      return true;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'id':
          comparison = a.id - b.id;
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'description':
          comparison = (a.description || '').localeCompare(b.description || '');
          break;
        case 'duration':
          comparison = a.duration - b.duration;
          break;
        case 'isActive':
          comparison = (a.isActive ? 1 : 0) - (b.isActive ? 1 : 0);
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime();
          break;
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });

  return (
    <div className="practice-tests-container">
      <div className="practice-tests-filters">
        <div className="search-container">
          <FiSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search tests..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-container">
          <label htmlFor="status-filter">Status:</label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="status-filter"
          >
            <option value="all">All</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      <div className="exam-table-container">
        <table className="exam-table">
          <thead>
            <tr>
              <th onClick={() => handleSort('id')} className="sortable">
                ID {renderSortIcon('id')}
              </th>
              <th onClick={() => handleSort('title')} className="sortable">
                Test Name {renderSortIcon('title')}
              </th>
              <th onClick={() => handleSort('description')} className="sortable">
                Description {renderSortIcon('description')}
              </th>
              <th onClick={() => handleSort('duration')} className="sortable">
                Duration {renderSortIcon('duration')}
              </th>
              <th onClick={() => handleSort('isActive')} className="sortable">
                Status {renderSortIcon('isActive')}
              </th>
              <th onClick={() => handleSort('createdAt')} className="sortable">
                Created {renderSortIcon('createdAt')}
              </th>
              <th>Action</th>
              {showActions && <th>Manage</th>}
            </tr>
          </thead>
          <tbody>
            {filteredTests.map((test) => (
              <tr key={test.id}>
                <td>{test.id}</td>
                <td>{test.title}</td>
                <td>{test.description || '(No description)'}</td>
                <td>{test.duration} min</td>
                <td>
                  <span className={`status-badge ${test.isActive ? 'active' : 'inactive'}`}>
                    {test.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{test.createdAt ? new Date(test.createdAt).toLocaleDateString() : '-'}</td>
                <td>
                  <div className="action-buttons">
                    <ActionIconButton
                      icon={<FiPlayCircle size={18} />}
                      tooltip="Take Test"
                      onClick={() => onTakeTest && onTakeTest(test.id)}
                      disabled={!test.isActive}
                    />
                    {onHistory && (
                      <ActionIconButton
                        icon={<FiFileText size={18} />}
                        tooltip="History"
                        onClick={() => onHistory(test.id)}
                      />
                    )}
                  </div>
                </td>
                {showActions && (
                  <td>
                    <button onClick={() => onEdit && onEdit(test.id)}>Edit</button>
                    <button onClick={() => onDelete && onDelete(test.id)}>Delete</button>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PracticeTestTable; 