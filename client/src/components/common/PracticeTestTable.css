.practice-tests-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.practice-tests-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.filter-container label {
  color: #4a5568;
  font-weight: 500;
}

.status-filter {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.2s;
}

.status-filter:focus {
  outline: none;
  border-color: #3498db;
}

.exam-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
  overflow: hidden;
  position: relative;
  overflow: visible;
}

.exam-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.exam-table th,
.exam-table td {
  padding: 1.25rem 1rem;
  text-align: left;
  vertical-align: top;
  border-bottom: 1px solid #e2e8f0;
  background: none;
  word-break: break-word;
}

.exam-table th {
  background-color: #f8fafc;
  color: #374151;
  font-weight: 700;
  white-space: nowrap;
  font-size: 1.05rem;
  border-bottom: 2px solid #e2e8f0;
}

.exam-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-right: 24px;
}

.exam-table th.sortable:hover {
  background-color: #edf2f7;
}

.exam-table th.sortable svg {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
}

.exam-table tbody tr {
  background: #fcfcfd;
  transition: background 0.2s, box-shadow 0.2s;
  border-radius: 8px;
}

.exam-table tbody tr:not(:last-child) {
  box-shadow: 0 1px 0 #e2e8f0;
}

.exam-table tbody tr:hover td {
  background-color: #f1f5f9;
}

.exam-table td {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  vertical-align: top;
}

.exam-table td .status-badge {
  margin-top: 0.25rem;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.9rem;
  border-radius: 9999px;
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fee2e2;
  color: #b91c1c;
}

.take-exam-button, .history-button {
  display: none !important;
}

.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #3498db;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  position: relative;
  transition: background 0.2s, box-shadow 0.2s, filter 0.2s;
  margin-bottom: 0.25rem;
  outline: none;
}
.icon-button:disabled {
  background: #cbd5e0;
  color: #fff;
  cursor: not-allowed;
  filter: grayscale(0.5);
}
.icon-button:hover:not(:disabled),
.icon-button:focus-visible:not(:disabled) {
  background: #217dbb;
  filter: brightness(1.15) drop-shadow(0 2px 6px rgba(52,152,219,0.10));
  z-index: 2;
}

.icon-button .tooltip {
  visibility: hidden;
  opacity: 0;
  min-width: 80px;
  max-width: 220px;
  background: #222;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 6px 14px;
  position: absolute;
  left: 50%;
  bottom: 120%;
  transform: translateX(-50%);
  z-index: 99;
  font-size: 0.97rem;
  pointer-events: none;
  transition: opacity 0.2s;
  white-space: pre-line;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
.icon-button .tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: #222 transparent transparent transparent;
}
.icon-button:hover .tooltip,
.icon-button:focus .tooltip {
  visibility: visible;
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .exam-table th,
  .exam-table td {
    padding: 1rem 0.5rem;
    font-size: 0.97rem;
  }
}

@media (max-width: 768px) {
  .practice-tests-container {
    padding: 1rem;
  }
  .practice-tests-filters {
    flex-direction: column;
  }
  .search-container,
  .filter-container {
    width: 100%;
  }
  .exam-table-container {
    overflow-x: auto;
  }
  .exam-table {
    min-width: 800px;
  }
} 