import React from 'react';

interface ExamHeaderProps {
  applicationNo: string;
  seatNo: string;
}

const ExamHeader: React.FC<ExamHeaderProps> = ({
  applicationNo,
  seatNo
}) => {
  return (
    <div className="exam-header">
      <div className="header-left">
        <div className="header-title">EPS-TOPIK</div>
      </div>
      
      <div className="header-center">
        <div className="header-subtitle">Test of proficiency in Korean</div>
      </div>
      
      <div className="header-right">
        <div className="application-box">
          <span className="application-label">Application No.</span>
          <span className="application-value">{applicationNo}</span>
        </div>
        
        <div className="application-box">
          <span className="application-label">Seat no.</span>
          <span className="application-value">{seatNo}</span>
        </div>
      </div>
    </div>
  );
};

export default ExamHeader;