import React, { useEffect, ReactNode } from 'react';
import { Navigate, useLocation, useParams } from 'react-router-dom';
import { useAuth } from '../hooks/use-auth';

interface ProtectedRouteProps {
  children: ReactNode;
  adminOnly?: boolean;
  allowAnonymousForDemo?: boolean;
  publicRoute?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  adminOnly = false,
  allowAnonymousForDemo = false,
  publicRoute = false
}) => {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();

  console.log('ProtectedRoute debug:', {
    id,
    allowAnonymousForDemo,
    isLoading,
    user,
    path: location.pathname
  });

  // Hiển thị loading khi đang kiểm tra trạng thái đăng nhập
  if (isLoading) {
    console.log('ProtectedRoute: Loading state');
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Đang tải...</p>
      </div>
    );
  }

  // Handle public routes (like landing page)
  if (publicRoute) {
    // If user is logged in, redirect based on role
    if (user) {
      if (user.role === 'admin') {
        return <Navigate to="/admin" replace />;
      } else {
        return <Navigate to="/dashboard" replace />;
      }
    }
    // If not logged in, allow access to public route
    return <>{children}</>;
  }

  // Allow anonymous access for demo exam if allowAnonymousForDemo is true
  if (allowAnonymousForDemo && id === '8') {
    console.log('ProtectedRoute: Allowing anonymous access for demo exam');
    return <>{children}</>;
  }

  // Nếu không có người dùng, chuyển hướng đến trang đăng nhập
  if (!user) {
    console.log('ProtectedRoute: No user, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Nếu yêu cầu quyền admin và người dùng không phải admin, chuyển về dashboard
  if (adminOnly && user.role !== 'admin') {
    console.log('ProtectedRoute: Admin required, redirecting to dashboard');
    return <Navigate to="/dashboard" replace />;
  }

  // Nếu đã đăng nhập và có quyền thích hợp, hiển thị component
  console.log('ProtectedRoute: User authenticated, showing content');
  return <>{children}</>;
};

interface AdminRouteProps {
  children: ReactNode;
}

export const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  return <ProtectedRoute adminOnly>{children}</ProtectedRoute>;
};

export default ProtectedRoute;