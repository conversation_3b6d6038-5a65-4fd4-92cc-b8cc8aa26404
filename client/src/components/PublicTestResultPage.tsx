import { useParams, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { ExamResultScreen } from '../App';

export default function PublicTestResultPage() {
  const { attemptId } = useParams();
  const navigate = useNavigate();
  const [attempt, setAttempt] = useState<any>(null);
  const [exam, setExam] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const res = await fetch(`/api/exam-attempts/${attemptId}`);
        if (!res.ok) throw new Error('Failed to fetch attempt');
        const attemptData = await res.json();
        setAttempt(attemptData);
        const examRes = await fetch(`/api/exams/${attemptData.examId}`);
        if (!examRes.ok) throw new Error('Failed to fetch exam');
        setExam(await examRes.json());
      } catch (err: any) {
        setError(err.message || 'Error loading result');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [attemptId]);

  if (loading) return <div style={{padding: 40, textAlign: 'center'}}>Loading...</div>;
  if (error || !attempt || !exam) return <div style={{padding: 40, textAlign: 'center', color: 'red'}}>Failed to load result: {error}</div>;

  // result_json là mảng, cần chuyển thành answers dạng { [questionId]: userAnswer }
  const answers: Record<number, number> = {};
  (attempt.result_json || []).forEach((q: any) => {
    answers[q.questionId] = q.userAnswer;
  });

  // Tính score nếu cần
  const correctCount = (attempt.result_json || []).filter((q: any) => q.isCorrect).length;
  const score = correctCount * 5;
  const totalScore = 200;

  return (
    <ExamResultScreen
      exam={exam}
      answers={answers}
      score={score}
      onReturn={() => navigate('/')}
    />
  );
} 