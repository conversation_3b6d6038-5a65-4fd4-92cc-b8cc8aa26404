import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ExamAttempt, Exam, ExamResultDetails } from "@shared/schema";
import { Loader2, BarChart2, FileText } from "lucide-react";
import PracticeTestTable, { PracticeTest } from "../common/PracticeTestTable";
import TestResultModal from "./TestResultModal";

const UserDashboard = () => {
  const { user, isLoading, logout } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'overview' | 'exams'>('overview');
  const [selectedAttempt, setSelectedAttempt] = useState<ExamAttempt | null>(null);
  const [resultDetails, setResultDetails] = useState<ExamResultDetails[]>([]);
  const [isResultModalOpen, setIsResultModalOpen] = useState(false);
  
  // Redirect to login page if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      console.log('User not authenticated, redirecting to login page');
      navigate('/login');
    }
  }, [user, isLoading, navigate]);
  
  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="user-dashboard-container">
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }
  
  // If no user after loading is complete, don't render the dashboard
  if (!user) {
    return null;
  }
  
  // Fetch user's exam attempts
  const { data: userAttempts, isLoading: attemptsLoading } = useQuery<ExamAttempt[]>({
    queryKey: ["/api/exam-attempts"],
    queryFn: async () => {
      const res = await fetch('/api/exam-attempts');
      if (!res.ok) throw new Error('Failed to fetch exam attempts');
      return res.json();
    },
  });

  // Fetch available exams
  const { data: availableExams, isLoading: examsLoading } = useQuery<Exam[]>({
    queryKey: ["/api/exams/active"],
    queryFn: async () => {
      const res = await fetch('/api/exams/active');
      if (!res.ok) throw new Error('Failed to fetch exams');
      return res.json();
    },
  });

  const handleLogout = () => {
    logout();
  };

  const handleStartExam = (examId: number) => {
    navigate(`/exam/${examId}`);
  };

  const handleHistory = (examId: number) => {
    navigate(`/test-history/${examId}`);
  };

  const handleViewResults = async (attempt: ExamAttempt) => {
    navigate(`/test-result/${attempt.id}`);
  };

  const renderExamAttemptsList = () => {
    if (attemptsLoading) {
      return <div className="flex justify-center p-6"><Loader2 className="h-6 w-6 animate-spin" /></div>;
    }

    if (!userAttempts || userAttempts.length === 0) {
      return <p className="text-center p-6">You haven't taken any exams yet. Start practicing!</p>;
    }

    // Sort all attempts by completedAt (or startedAt) descending
    const sortedAttempts = [...userAttempts].sort((a, b) => {
      const aTime = new Date(a.completedAt || a.startedAt || 0).getTime();
      const bTime = new Date(b.completedAt || b.startedAt || 0).getTime();
      return bTime - aTime;
    });

    // Take only 5 most recent
    const recentAttempts = sortedAttempts.slice(0, 5);

    return (
      <div className="space-y-4">
        {recentAttempts.map(attempt => {
          const exam = availableExams?.find(e => e.id === attempt.examId);
          const examTitle = exam?.title || `Exam #${attempt.examId}`;
          const finishedAt = attempt.completedAt ? new Date(attempt.completedAt) : (attempt.startedAt ? new Date(attempt.startedAt) : null);
          const score = typeof attempt.score === 'number' ? attempt.score : 0;
          // Màu điểm: >=160 xanh lá, >=100 cam, <100 đỏ
          let scoreColor = '#4caf50';
          if (score < 100) scoreColor = '#f44336';
          else if (score < 160) scoreColor = '#ffa726';
          return (
            <div key={attempt.id} className="attempt-item flex items-center justify-between p-4 bg-white rounded shadow-sm border">
              <div>
                <div className="font-semibold text-base" style={{ fontWeight: 700 }}>{examTitle}</div>
                <div className="text-sm text-gray-600">{finishedAt ? finishedAt.toLocaleString() : 'Unknown time'}</div>
                <div className="text-sm font-medium">
                  <span style={{ color: scoreColor, fontWeight: 700, fontSize: '1.1rem' }}>Score: {score} / 200</span>
                </div>
              </div>
              <button 
                className="view-results-button"
                onClick={() => handleViewResults(attempt)}
              >
                View Results
              </button>
            </div>
          );
        })}
      </div>
    );
  };

  const renderAvailableExams = () => {
    if (examsLoading) {
      return <div className="flex justify-center p-6"><Loader2 className="h-6 w-6 animate-spin" /></div>;
    }

    if (!availableExams || availableExams.length === 0) {
      return <p className="text-center p-6">No exams are currently available. Please check back later.</p>;
    }

    // Map availableExams to PracticeTest type if needed
    const tests: PracticeTest[] = availableExams.map(e => ({
      id: e.id,
      title: e.title,
      description: e.description ?? '',
      duration: e.duration,
      isActive: e.isActive ?? true,
      createdAt: e.createdAt ? String(e.createdAt) : undefined,
    }));

    return (
      <PracticeTestTable
        tests={tests}
        onTakeTest={handleStartExam}
        onHistory={handleHistory}
      />
    );
  };

  return (
    <div className="user-dashboard-container">
      <div className="header">
        <div className="logo">
          <h1>EPS-TOPIK</h1>
          <span>User Dashboard</span>
        </div>
        <div className="user-info">
          <span className="user-name">{user?.fullName || user?.username}</span>
          <button className="logout-button" onClick={handleLogout}>Đăng xuất</button>
        </div>
      </div>
      
      <div className="dashboard-content">
        <div className="sidebar">
          <nav>
            <ul>
              <li className={activeTab === 'overview' ? 'active' : ''}>
                <button onClick={() => setActiveTab('overview')}>
                  <span className="icon"><BarChart2 size={18} /></span>
                  <span className="label">My Overview</span>
                </button>
              </li>
              <li className={activeTab === 'exams' ? 'active' : ''}>
                <button onClick={() => setActiveTab('exams')}>
                  <span className="icon"><FileText size={18} /></span>
                  <span className="label">Practice Tests</span>
                </button>
              </li>
            </ul>
          </nav>
        </div>
        
        <div className="content-area">
          {activeTab === 'overview' ? (
            <div className="overview-tab">
              <div className="welcome-message">
                <h2>Welcome, {user?.fullName || user?.username}!</h2>
                <p>Track your EPS-TOPIK exam practice progress and performance.</p>
              </div>
              
              <div className="dashboard-stats">
                <div className="stat-card">
                  <h3>Exams Completed</h3>
                  <div className="stat-value">
                    {attemptsLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      userAttempts?.filter(a => a.completed).length || 0
                    )}
                  </div>
                </div>
                <div className="stat-card">
                  <h3>Average Score</h3>
                  <div className="stat-value">
                    {attemptsLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      (() => {
                        const completedAttempts = userAttempts?.filter(a => a.completed) || [];
                        if (completedAttempts.length === 0) return 'N/A';
                        const totalScore = completedAttempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0);
                        return Math.round(totalScore / completedAttempts.length);
                      })()
                    )}
                  </div>
                </div>
                <div className="stat-card">
                  <h3>Available Exams</h3>
                  <div className="stat-value">
                    {examsLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      availableExams?.length || 0
                    )}
                  </div>
                </div>
              </div>
              
              <div className="recent-exams">
                <h3>Recent Attempts</h3>
                {renderExamAttemptsList()}
              </div>
            </div>
          ) : (
            <div className="exams-tab">
              <h2>Available Practice Tests</h2>
              <p className="text-gray-600 mb-6">Choose a test to start practicing for your EPS-TOPIK exam.</p>
              {renderAvailableExams()}
            </div>
          )}
        </div>
      </div>
      
      {selectedAttempt && (
        <TestResultModal
          isOpen={isResultModalOpen}
          onClose={() => {
            setIsResultModalOpen(false);
            setSelectedAttempt(null);
            setResultDetails([]);
          }}
          attempt={selectedAttempt}
          exam={availableExams?.find(e => e.id === selectedAttempt.examId)}
          resultDetails={resultDetails}
        />
      )}
    </div>
  );
};

export default UserDashboard;