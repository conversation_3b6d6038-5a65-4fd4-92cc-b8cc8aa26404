import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Exam as SharedExam } from '@shared/schema';
import { FiPlayCircle, FiChevronUp, FiChevronDown, FiStar } from 'react-icons/fi';
import './PracticeTests.css';

interface Exam extends SharedExam {
  isDefaultDemo?: boolean;
}

type SortField = 'id' | 'title' | 'description' | 'duration' | 'isActive' | 'createdAt';
type SortDirection = 'asc' | 'desc';

const PracticeTests = () => {
  const navigate = useNavigate();
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  
  // Sorting
  const [sortField, setSortField] = useState<SortField>('id');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  useEffect(() => {
    const fetchExams = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/exams');
        if (!response.ok) {
          throw new Error('Failed to fetch exams');
        }
        const data = await response.json();
        setExams(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching exams:', err);
        setError('Failed to load practice tests. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchExams();
  }, []);

  const handleTakeExam = (examId: number) => {
    navigate(`/exam/${examId}`);
  };

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const filteredExams = exams
    .filter(exam => {
      // Filter by search term
      if (searchTerm && 
          !exam.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
          !exam.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      
      // Filter by status
      if (statusFilter === 'active' && !exam.isActive) return false;
      if (statusFilter === 'inactive' && exam.isActive) return false;
      
      return true;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'id':
          comparison = a.id - b.id;
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'description':
          comparison = (a.description || '').localeCompare(b.description || '');
          break;
        case 'duration':
          comparison = a.duration - b.duration;
          break;
        case 'isActive':
          comparison = (a.isActive ? 1 : 0) - (b.isActive ? 1 : 0);
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime();
          break;
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });

  const renderSortIcon = (field: SortField) => {
    if (field !== sortField) return null;
    return sortDirection === 'asc' ? <FiChevronUp size={14} /> : <FiChevronDown size={14} />;
  };

  if (loading) {
    return (
      <div className="exam-list-loading">
        <div className="spinner"></div>
        <p>Loading practice tests...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="exam-list-error">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Try Again</button>
      </div>
    );
  }

  return (
    <div className="practice-tests-container">
      <div className="practice-tests-header">
        <h2>Available Practice Tests</h2>
      </div>
      
      <div className="exam-list-filters">
        <div className="search-container">
          <input 
            type="text" 
            placeholder="Search tests..." 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-container">
          <label htmlFor="status-filter">Status:</label>
          <select 
            id="status-filter"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="status-filter"
          >
            <option value="all">All</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>
      
      {filteredExams.length === 0 ? (
        <div className="no-exams">
          <p>No practice tests found.</p>
        </div>
      ) : (
        <div className="exam-table-container">
          <table className="exam-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('id')} className="sortable">
                  ID {renderSortIcon('id')}
                </th>
                <th onClick={() => handleSort('title')} className="sortable">
                  Title {renderSortIcon('title')}
                </th>
                <th onClick={() => handleSort('description')} className="sortable">
                  Description {renderSortIcon('description')}
                </th>
                <th onClick={() => handleSort('duration')} className="sortable">
                  Duration {renderSortIcon('duration')}
                </th>
                <th onClick={() => handleSort('isActive')} className="sortable">
                  Status {renderSortIcon('isActive')}
                </th>
                <th onClick={() => handleSort('createdAt')} className="sortable">
                  Created {renderSortIcon('createdAt')}
                </th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {filteredExams.map(exam => (
                <tr key={exam.id} className={exam.isActive ? 'active-exam' : 'inactive-exam'}>
                  <td>{exam.id}</td>
                  <td>{exam.title} {exam.isDefaultDemo && <FiStar title="This is the public Try a Test exam" style={{ color: '#f39c12', marginLeft: 4, verticalAlign: 'middle' }} />}</td>
                  <td>{exam.description || '(No description)'}</td>
                  <td>{exam.duration} minutes</td>
                  <td>
                    <span className={`status-badge ${exam.isActive ? 'active' : 'inactive'}`}>
                      {exam.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>{new Date(exam.createdAt || '').toLocaleDateString()}</td>
                  <td>
                    <button 
                      onClick={() => handleTakeExam(exam.id)}
                      className="take-exam-button"
                      disabled={!exam.isActive}
                    >
                      <FiPlayCircle size={16} style={{ marginRight: 4 }} />
                      Take Test
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default PracticeTests; 