.practice-tests-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.practice-tests-header {
  margin-bottom: 2rem;
}

.practice-tests-header h2 {
  color: #2c3e50;
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
}

.exam-list-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.filter-container label {
  color: #4a5568;
  font-weight: 500;
}

.status-filter {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.2s;
}

.status-filter:focus {
  outline: none;
  border-color: #3498db;
}

.exam-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.exam-table {
  width: 100%;
  border-collapse: collapse;
}

.exam-table th,
.exam-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.exam-table th {
  background-color: #f8fafc;
  color: #4a5568;
  font-weight: 600;
  white-space: nowrap;
}

.exam-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-right: 24px;
}

.exam-table th.sortable:hover {
  background-color: #edf2f7;
}

.exam-table th.sortable svg {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
}

.exam-table tr:hover td {
  background-color: #f8fafc;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fee2e2;
  color: #b91c1c;
}

.take-exam-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.take-exam-button:hover:not(:disabled) {
  background-color: #2980b9;
}

.take-exam-button:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
}

.no-exams {
  padding: 3rem;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-exams p {
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
}

.exam-list-loading,
.exam-list-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #e53e3e;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .practice-tests-container {
    padding: 1rem;
  }
  
  .exam-list-filters {
    flex-direction: column;
  }
  
  .search-container,
  .filter-container {
    width: 100%;
  }
  
  .exam-table-container {
    overflow-x: auto;
  }
  
  .exam-table {
    min-width: 800px;
  }
} 