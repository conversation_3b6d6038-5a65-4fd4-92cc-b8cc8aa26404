import React from 'react';
import { Question, ExamAttempt, Exam, ExamResultDetails } from '@shared/schema';

interface TestResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  attempt: ExamAttempt;
  exam?: Exam;
  resultDetails?: ExamResultDetails[];
}

const TestResultModal: React.FC<TestResultModalProps> = ({ 
  isOpen, 
  onClose, 
  attempt, 
  exam,
  resultDetails
}) => {
  if (!isOpen) return null;

  // Calculate total number of questions
  const questions = exam?.questions ? (Array.isArray(exam.questions) ? exam.questions : JSON.parse(exam.questions as string)) : [];
  const totalQuestions = questions.length || 0;
  
  // Calculate number of correct answers
  const correctCount = resultDetails ? resultDetails.filter((d: any) => d.isCorrect).length : 0;
  const score = correctCount * 5;
  const totalScore = 200;
  
  const renderQuestionResults = () => {
    if (!resultDetails || resultDetails.length === 0) return <p>No detailed results available</p>;

    return (
      <div className="question-results">
        {resultDetails.map((detail, idx) => {
          const questionNumber = detail.questionId;
          const isCorrect = detail.isCorrect;
          return (
            <div key={detail.questionId || idx} className="preview-question">
              <div className="question-number">Question {questionNumber}</div>
              <div className="question-text">{detail.questionText}</div>
              {detail.questionType === 'listening' && (
                <div className="question-subtext">Listening Question</div>
              )}
              <div className="options-container">
                {(detail.options as any[]).map((option: any) => {
                  const isUserChoice = option.id === detail.userAnswer;
                  const isCorrectOption = option.id === detail.correctAnswer;
                  return (
                    <div
                      key={option.id}
                      className={`answer-option${isUserChoice ? ' user-choice' : ''}${isCorrectOption ? ' correct-option' : ''}`}
                      style={{width: '100%', boxSizing: 'border-box'}}>
                      <span className="option-number">{option.id}.</span>
                      <span className="option-text">{option.text}
                        {isCorrectOption && <span className="correct-mark">✓</span>}
                        {isUserChoice && !isCorrectOption && <span className="user-mark">✗</span>}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="test-result-modal-overlay">
      <div className="test-result-modal">
        <div className="test-result-header">
          <h2>Test Results</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="test-result-summary">
          <div className="summary-item">
            <span className="label">Exam:</span>
            <span className="value">{exam?.title || 'Unknown'}</span>
          </div>
          <div className="summary-item">
            <span className="label">Date:</span>
            <span className="value">
              {new Date(attempt.completedAt || attempt.startedAt || new Date()).toLocaleDateString()}
            </span>
          </div>
          <div className="summary-item">
            <span className="label">Score:</span>
            <span className="value score">{score} / {totalScore}</span>
          </div>
          <div className="summary-item">
            <span className="label">Percentage:</span>
            <span className="value percentage">
              {totalQuestions > 0 ? Math.round((score / totalScore) * 100) : 0}%
            </span>
          </div>
        </div>
        
        <div className="test-result-details">
          <h3>Detailed Results</h3>
          {renderQuestionResults()}
        </div>
      </div>
    </div>
  );
};

export default TestResultModal;