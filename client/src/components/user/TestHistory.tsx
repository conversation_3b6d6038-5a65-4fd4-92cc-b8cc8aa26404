import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Loader2, ArrowUpRight, ArrowDownRight, ArrowRight, FileText } from 'lucide-react';
import { ExamAttempt } from '@shared/schema';
import './TestHistory.css';
import { useAuth } from '../../hooks/use-auth';

export const TestHistoryContent = () => {
  const { user } = useAuth();
  const { testId } = useParams<{ testId: string }>();
  const navigate = useNavigate();

  const { data: attempts, isLoading } = useQuery<ExamAttempt[]>({
    queryKey: ["/api/exam-attempts", testId],
    queryFn: async () => {
      const res = await fetch(`/api/exam-attempts?examId=${testId}`);
      if (!res.ok) throw new Error('Failed to fetch attempts');
      return res.json();
    },
  });

  // Fetch exam info for display
  const { data: examInfo } = useQuery<any>({
    queryKey: ["/api/exams", testId],
    queryFn: async () => {
      const res = await fetch(`/api/exams/${testId}`);
      if (!res.ok) throw new Error('Failed to fetch exam info');
      return res.json();
    },
    enabled: !!testId
  });

  if (isLoading) {
    return (
      <div className="loading-container">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  if (!attempts || attempts.length === 0) {
    return (
      <div className="history-container empty">
        <h2 className="history-title">Test History</h2>
        <p>No attempts found for this test.</p>
        <button className="primary-button mt-4" onClick={() => {
          if (user?.role === 'admin') {
            navigate('/admin');
          } else {
            navigate('/dashboard');
          }
        }}>Back</button>
      </div>
    );
  }

  // Calculate statistics
  const scores = attempts.filter(a => a.completed && typeof a.score === 'number').map(a => a.score!);
  const average = scores.length ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;
  const totalScore = 200;
  let trendIcon = <ArrowRight className="inline" />;
  if (scores.length > 1) {
    if (scores[scores.length - 1] > scores[scores.length - 2]) trendIcon = <ArrowUpRight className="inline text-green-600" />;
    else if (scores[scores.length - 1] < scores[scores.length - 2]) trendIcon = <ArrowDownRight className="inline text-red-600" />;
  }

  return (
    <div className="test-history-detail-container">
      <div className="test-history-header">
        <button className="back-button" onClick={() => {
          if (user?.role === 'admin') {
            navigate('/admin');
          } else {
            navigate('/dashboard');
          }
        }}>
          &larr; Back
        </button>
        <div className="test-history-title-group">
          <FileText size={32} className="history-icon" />
          <div>
            <h2 className="test-history-title">Test History</h2>
            <div className="test-history-exam-title">{examInfo?.title || 'Exam'}</div>
          </div>
        </div>
      </div>
      <div className="test-history-card">
        <div className="history-stats">
          <div>
            <div className="stat-label">Attempts</div>
            <div className="stat-value">{scores.length}</div>
          </div>
          <div>
            <div className="stat-label">Average Score</div>
            <div className="stat-value">{average} / {totalScore}</div>
          </div>
          <div>
            <div className="stat-label">Trend</div>
            <div className="stat-value">{trendIcon}</div>
          </div>
        </div>
        <div className="history-list">
          {attempts.map(a => {
            // Lấy thời gian hoàn thành theo giờ Việt Nam
            const finishedAt = a.completedAt ? new Date(new Date(a.completedAt).getTime() + 7 * 60 * 60 * 1000) : null;
            return (
              <div className={`history-card ${a.completed ? 'completed' : 'incomplete'}`} key={a.id}>
                <div className="history-card-row">
                  <span className="history-label">Score:</span>
                  <span className="history-score">{typeof a.score === 'number' ? `${a.score} / ${totalScore}` : '-'}</span>
                </div>
                <div className="history-card-row">
                  <span className="history-label">Status:</span>
                  <span className={`history-status ${a.completed ? 'completed' : 'incomplete'}`}>{a.completed ? 'Completed' : 'Incomplete'}</span>
                </div>
                <div className="history-card-row">
                  <span className="history-label">Finished At:</span>
                  <span className="history-date">{finishedAt ? finishedAt.toLocaleString('vi-VN', { hour12: false }) : '-'}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}; 