.test-result-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.test-result-modal {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.test-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.test-result-header h2 {
  margin: 0;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.test-result-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-item .label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.summary-item .value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.summary-item .value.score {
  color: #28a745;
  font-weight: 600;
}

.summary-item .value.percentage {
  color: #007bff;
  font-weight: 600;
}

.test-result-details {
  margin-top: 20px;
}

.test-result-details h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.question-results {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-result-item {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 15px;
  background-color: white;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.question-number {
  font-weight: 500;
  color: #333;
}

.result-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.result-indicator.correct {
  background-color: #d4edda;
  color: #155724;
}

.result-indicator.incorrect {
  background-color: #f8d7da;
  color: #721c24;
}

.question-content {
  margin-bottom: 15px;
}

.question-text {
  margin: 0 0 10px 0;
  color: #333;
  line-height: 1.5;
}

.question-type {
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.answer-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.option.user-choice {
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.option.correct-option {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.option-number {
  font-weight: 500;
  margin-right: 10px;
  color: #666;
}

.option-text {
  flex: 1;
  color: #333;
}

.correct-mark {
  color: #28a745;
  margin-left: 8px;
}

.user-mark {
  color: #dc3545;
  margin-left: 8px;
}

@media (max-width: 768px) {
  .test-result-modal {
    width: 95%;
    padding: 15px;
  }

  .test-result-summary {
    grid-template-columns: 1fr;
  }
} 