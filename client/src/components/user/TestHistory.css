/* TestHistory.css */

.history-container {
  max-width: 600px;
  margin: 32px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 8px;
}

.history-icon {
  color: #00468C;
}

.history-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2px;
  color: #00468C;
}

.history-exam-title {
  font-size: 1.1rem;
  color: #555;
  font-weight: 500;
}

.history-stats {
  display: flex;
  gap: 32px;
  justify-content: flex-start;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.95rem;
  color: #888;
}

.stat-value {
  font-size: 1.3rem;
  font-weight: 600;
  color: #00468C;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.history-card {
  background: #f7fbff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  padding: 18px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-left: 5px solid #aac8e7;
  transition: border-color 0.2s;
}
.history-card.completed {
  border-left-color: #4caf50;
}
.history-card.incomplete {
  border-left-color: #f44336;
}

.history-card-row {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
}

.history-label {
  color: #888;
  min-width: 90px;
  font-size: 0.98rem;
}

.history-score {
  font-weight: 700;
  color: #00468C;
  font-size: 1.1rem;
}

.history-status.completed {
  color: #4caf50;
  font-weight: 600;
}
.history-status.incomplete {
  color: #f44336;
  font-weight: 600;
}

.history-date {
  color: #555;
  font-size: 0.98rem;
}

.primary-button {
  background: linear-gradient(to bottom, #62a0e1, #3d88d5);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.13);
  transition: background 0.2s;
}
.primary-button:hover {
  background: #ff7f27;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40vh;
  font-size: 1.2rem;
  color: #00468C;
}

@media (max-width: 700px) {
  .history-container {
    padding: 16px 4px;
    max-width: 98vw;
  }
  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .history-stats {
    gap: 12px;
  }
  .history-card {
    padding: 12px 8px;
  }
} 