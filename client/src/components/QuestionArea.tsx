import React, { useEffect, useRef, useState } from 'react';

interface QuestionOption {
  id: number;
  text: string;
}

interface QuestionOptionImage {
  id: number;
  imageUrl: string;
}

export interface Question {
  id: number;
  display_index: number; // Make display_index required
  type: 'reading' | 'listening' | 'transition';
  text: string;
  subText: string;
  options: QuestionOption[] | QuestionOptionImage[];
  hasImageOptions?: boolean;
  audioUrl?: string; // URL tới file audio từ Google Drive
  correctAnswer?: number; // ID của đáp án đúng
  isTransitionScreen?: boolean; // Biểu thị đây là màn hình chuyển tiếp
  listeningCount?: number; // Số lượng câu hỏi listening
  start_at?: number; // Thời điểm bắt đầu của câu hỏi (giây)
  audioDuration?: number; // Thời lượng audio tính bằng giây (đư<PERSON><PERSON> tính từ start_at, không lưu trong DB)
  examListeningAudioUrl?: string; // Add this field to store the exam-level audio URL
}

interface QuestionAreaProps {
  question: Question;
  currentQuestionNumber: number;
  selectedOption?: number;
  onSelectOption: (optionId: number) => void;
  onAudioEnded?: () => void; // Callback khi audio kết thúc
  onNavigate?: (displayIndex: number) => void; // Callback để chuyển câu hỏi
  allQuestions?: Question[]; // Danh sách tất cả câu hỏi để tính toán thời điểm chuyển
}

const QuestionArea: React.FC<QuestionAreaProps> = ({
  question,
  currentQuestionNumber,
  selectedOption,
  onSelectOption,
  onAudioEnded,
  onNavigate,
  allQuestions
}) => {
  // Ref for audio element
  const audioRef = useRef<HTMLAudioElement>(null);
  const [showPlayButton, setShowPlayButton] = useState(false);
  const timeUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Hàm tính toán câu hỏi tiếp theo dựa trên thời gian audio
  const getNextQuestionByTime = (currentTime: number): Question | null => {
    if (!allQuestions || !onNavigate) return null;

    // Lọc các câu hỏi nghe và sắp xếp theo start_at
    const listeningQuestions = allQuestions
      .filter(q => q.type === 'listening' && q.start_at !== undefined)
      .sort((a, b) => (a.start_at || 0) - (b.start_at || 0));

    // Tìm câu hỏi có start_at gần nhất với thời gian hiện tại
    for (let i = 0; i < listeningQuestions.length; i++) {
      const q = listeningQuestions[i];
      const nextQ = listeningQuestions[i + 1];

      // Nếu là câu cuối cùng hoặc thời gian hiện tại chưa đến câu tiếp theo
      if (!nextQ || currentTime < (nextQ.start_at || 0)) {
        // Kiểm tra xem thời gian hiện tại có ở trong khoảng của câu hỏi này không
        if (currentTime >= (q.start_at || 0)) {
          return q;
        }
      }
    }

    return null;
  };

  // Hàm theo dõi thời gian audio và tự động chuyển câu hỏi
  const startTimeTracking = (audioElement: HTMLAudioElement) => {
    // Xóa interval cũ nếu có
    if (timeUpdateIntervalRef.current) {
      clearInterval(timeUpdateIntervalRef.current);
    }

    timeUpdateIntervalRef.current = setInterval(() => {
      const currentTime = audioElement.currentTime;
      const nextQuestion = getNextQuestionByTime(currentTime);

      if (nextQuestion && nextQuestion.display_index !== question.display_index) {
        console.log(`[AUDIO] Auto-switching to question ${nextQuestion.display_index} at time ${currentTime}s`);
        onNavigate?.(nextQuestion.display_index);
      }
    }, 500); // Kiểm tra mỗi 500ms
  };

  // Hàm dừng theo dõi thời gian
  const stopTimeTracking = () => {
    if (timeUpdateIntervalRef.current) {
      clearInterval(timeUpdateIntervalRef.current);
      timeUpdateIntervalRef.current = null;
    }
  };

  // Section icon or label based on question type
  const sectionLabel = question.type === 'reading'
    ? 'READING'
    : 'LISTENING';

  // Function to determine if option has image
  const hasImageOption = (option: any): option is QuestionOptionImage => {
    return 'imageUrl' in option;
  };

  // Reset audio khi chuyển câu hỏi
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current.src = '';
    }
    setShowPlayButton(false);
    console.log(`[AUDIO] Question changed to ${question.id}, reset audio`);
  }, [question.id]);

  // Tự động chuyển câu hỏi sau khi hết thời gian
  useEffect(() => {
    if (question.type === 'listening' && question.audioDuration && onAudioEnded) {
      const timer = setTimeout(() => {
        console.log(`[AUDIO] Question ${question.id} duration (${question.audioDuration}s) ended, moving to next question`);
        onAudioEnded();
      }, question.audioDuration * 1000);

      return () => clearTimeout(timer);
    }
  }, [question.id, question.type, question.audioDuration, onAudioEnded]);

  // Cleanup khi component unmount
  useEffect(() => {
    return () => {
      stopTimeTracking();
    };
  }, []);

  // Phát âm thanh khi câu hỏi listening hiển thị
  useEffect(() => {
    if (question.isTransitionScreen) {
      console.log(`[AUDIO] Question ${question.id} is transition screen, skipping audio`);
      return;
    }

    const playAudio = async () => {
      // Kiểm tra xem có phải là câu hỏi nghe đầu tiên không
      if (question.type === 'listening' && allQuestions) {
        const listeningQuestions = allQuestions
          .filter(q => q.type === 'listening')
          .sort((a, b) => (a.display_index || 0) - (b.display_index || 0));

        const isFirstListeningQuestion = listeningQuestions.length > 0 &&
          listeningQuestions[0].display_index === question.display_index;

        // Chỉ play audio cho câu hỏi nghe đầu tiên
        if (!isFirstListeningQuestion) {
          console.log(`[AUDIO] Question ${question.id} is not the first listening question, skipping audio play`);

          // Nếu có audio đang phát, chỉ cần theo dõi thời gian
          if (window.currentPlayingAudio && !window.currentPlayingAudio.paused) {
            console.log(`[AUDIO] Continuing to track existing audio for question ${question.id}`);
            startTimeTracking(window.currentPlayingAudio);
          }

          return;
        }
      }
      if (question.type === 'listening' && (question.audioUrl || question.examListeningAudioUrl) && audioRef.current) {
        try {
          // Kiểm tra xem có audio element đã tải trước không
          if (window.preloadedMainAudioElement && question.examListeningAudioUrl) {
            console.log(`[AUDIO] Using preloaded main audio element for question ${question.id}`);

            const preloadedAudio = window.preloadedMainAudioElement;

            // Đặt thời điểm bắt đầu phát
            if (question.start_at !== undefined && question.start_at > 0) {
              preloadedAudio.currentTime = question.start_at;
              console.log(`[AUDIO] Setting start time to ${question.start_at}s for question ${question.id}`);
            } else {
              preloadedAudio.currentTime = 0;
            }

            // Phát ngay lập tức
            await preloadedAudio.play();
            setShowPlayButton(false);
            console.log(`[AUDIO] Playing preloaded audio for question ${question.id} from ${question.start_at || 0}s`);

            // Bắt đầu theo dõi thời gian để tự động chuyển câu hỏi
            startTimeTracking(preloadedAudio);

            // Lưu audio element vào window để các câu hỏi khác có thể truy cập
            window.currentPlayingAudio = preloadedAudio;

            // Đồng bộ với audioRef để các control khác hoạt động
            if (audioRef.current) {
              audioRef.current.src = preloadedAudio.src;
              audioRef.current.currentTime = preloadedAudio.currentTime;
            }

            return;
          }

          // Nếu không có URL đã tải trước, sử dụng phương pháp thông thường
          const audioUrl = question.audioUrl || question.examListeningAudioUrl;
          if (!audioUrl) return;

          // Import getPlayableAudioUrl function
          const utils = await import('../lib/utils');
          const getPlayableAudioUrl = utils.getPlayableAudioUrl;

          // Get direct playable URL
          const playableUrl = await getPlayableAudioUrl(audioUrl);
          console.log(`[AUDIO] Got playable URL for question ${question.id}`);

          if (playableUrl) {
            audioRef.current.src = playableUrl;
            audioRef.current.load();

            // Nếu có start_at, đặt thời điểm bắt đầu phát
            if (question.start_at !== undefined && question.start_at > 0) {
              audioRef.current.currentTime = question.start_at;
              console.log(`[AUDIO] Setting start time to ${question.start_at}s for question ${question.id}`);
            }

            await audioRef.current.play();
            setShowPlayButton(false);
            console.log(`[AUDIO] Playing audio for question ${question.id}...`);
          }
        } catch (err) {
          setShowPlayButton(true);
          console.error('[AUDIO] Không thể phát âm thanh tự động:', err);
        }
      }
    };

    playAudio();

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, [question.id, question.type, question.audioUrl, question.examListeningAudioUrl, question.isTransitionScreen]);

  // Nếu là màn hình chuyển tiếp, hiển thị giao diện đặc biệt
  if (question.isTransitionScreen) {
    return (
      <div className="question-area">
        <div className="transition-screen">
          <h1>{question.text}</h1>
          <div className="divider-line"></div>
          <p className="transition-note">{question.subText}</p>
        </div>
      </div>
    );
  }

  // Màn hình câu hỏi bình thường
  return (
    <div className="question-area">
      {/* Audio element for question */}
      <audio
        ref={audioRef}
        controls={false}
        className="hidden-audio"
      >
        <source src="" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
      {showPlayButton && question.type === 'listening' && allQuestions && (() => {
        const listeningQuestions = allQuestions
          .filter(q => q.type === 'listening')
          .sort((a, b) => (a.display_index || 0) - (b.display_index || 0));

        const isFirstListeningQuestion = listeningQuestions.length > 0 &&
          listeningQuestions[0].display_index === question.display_index;

        return isFirstListeningQuestion;
      })() && (
        <button
          style={{ margin: '10px 0', padding: '8px 16px', background: '#00468C', color: 'white', borderRadius: 6, border: 'none', fontWeight: 600, cursor: 'pointer' }}
          onClick={async () => {
            try {
              if (audioRef.current) {
                // Kiểm tra xem có phải là câu hỏi nghe đầu tiên không
                if (question.type === 'listening' && allQuestions) {
                  const listeningQuestions = allQuestions
                    .filter(q => q.type === 'listening')
                    .sort((a, b) => (a.display_index || 0) - (b.display_index || 0));

                  const isFirstListeningQuestion = listeningQuestions.length > 0 &&
                    listeningQuestions[0].display_index === question.display_index;

                  // Nếu không phải câu hỏi nghe đầu tiên, không cho phép play thủ công
                  if (!isFirstListeningQuestion) {
                    console.log(`[AUDIO] Manual play not allowed for non-first listening question ${question.id}`);
                    return;
                  }
                }

                // Kiểm tra xem có audio element đã tải trước không
                if (window.preloadedMainAudioElement && question.examListeningAudioUrl) {
                  console.log(`[AUDIO] Using preloaded main audio element for manual play of question ${question.id}`);

                  const preloadedAudio = window.preloadedMainAudioElement;

                  // Đặt thời điểm bắt đầu phát
                  if (question.start_at !== undefined && question.start_at > 0) {
                    preloadedAudio.currentTime = question.start_at;
                    console.log(`[AUDIO] Setting start time to ${question.start_at}s for manual play of question ${question.id}`);
                  } else {
                    preloadedAudio.currentTime = 0;
                  }

                  await preloadedAudio.play();
                  setShowPlayButton(false);

                  // Bắt đầu theo dõi thời gian để tự động chuyển câu hỏi
                  startTimeTracking(preloadedAudio);

                  // Lưu audio element vào window để các câu hỏi khác có thể truy cập
                  window.currentPlayingAudio = preloadedAudio;

                  return;
                }

                // Nếu không có URL đã tải trước, sử dụng phương pháp thông thường
                const audioUrl = question.audioUrl || question.examListeningAudioUrl;
                if (!audioUrl) return;

                // Import getPlayableAudioUrl function
                const utils = await import('../lib/utils');
                const getPlayableAudioUrl = utils.getPlayableAudioUrl;

                // Get direct playable URL
                const playableUrl = await getPlayableAudioUrl(audioUrl);
                console.log(`[AUDIO] Got playable URL for manual play of question ${question.id}`);

                if (playableUrl && audioRef.current.src !== playableUrl) {
                  audioRef.current.src = playableUrl;
                  audioRef.current.load();
                }

                // Nếu có start_at, đặt thời điểm bắt đầu phát
                if (question.start_at !== undefined && question.start_at > 0) {
                  audioRef.current.currentTime = question.start_at;
                  console.log(`[AUDIO] Setting start time to ${question.start_at}s for manual play of question ${question.id}`);
                }

                await audioRef.current.play();
                setShowPlayButton(false);
                console.log(`[AUDIO] User manually played audio for question ${question.id}`);
              }
            } catch (err) {
              setShowPlayButton(true);
              alert('Không thể phát file âm thanh. Vui lòng kiểm tra lại URL hoặc thử lại.');
              console.error('[AUDIO] Không thể phát âm thanh khi user click:', err);
            }
          }}
        >
          Phát lại audio
        </button>
      )}

      <div className="question-content">
        <div className="question-main-text">
          {currentQuestionNumber}. {question.text}
        </div>

        {question.subText && (
          <div className="question-subtext">
            {question.subText}
          </div>
        )}
      </div>

      {question.hasImageOptions ? (
        <div className="image-options-container">
          <div className="image-options-grid" style={{ textAlign: 'center' }}>
            {question.options.map((option: any) => (
              <div
                key={option.id}
                className={`image-option ${selectedOption === option.id ? 'selected' : ''}`}
                onClick={() => {
                  if (question.type === 'reading') {
                    onSelectOption(option.id);
                  } else if (question.type === 'listening') {
                    onSelectOption(option.id);
                  }
                }}
                style={{
                  cursor: 'pointer',
                  width: '66.7%',
                  height: '66.7%',
                  margin: '2px',
                  display: 'inline-block'
                }}
              >
                <img
                  src={option.imageUrl}
                  alt={`Option ${option.id}`}
                  className="option-image"
                  style={{ width: '100%', height: 'auto' }}
                />
                <div className={`image-option-circle ${selectedOption === option.id ? 'selected' : ''}`}>
                  <span className="option-number">{option.id}</span>
                  {selectedOption === option.id && (
                    <span className="option-check" style={{ color: '#00468C' }}>✓</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="options-container">
          <ul className="options-list">
            {question.options.map((option: any) => (
              <li
                key={option.id}
                className={`option-item ${selectedOption === option.id ? 'selected' : ''}`}
                onClick={() => {
                  if (question.type === 'reading') {
                    onSelectOption(option.id);
                  } else if (question.type === 'listening') {
                    onSelectOption(option.id);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                <div className={`option-circle ${selectedOption === option.id ? 'selected' : ''}`}>
                  <span className="option-number">{option.id}</span>
                  {selectedOption === option.id && (
                    <span className="option-check" style={{ color: '#00468C' }}>✓</span>
                  )}
                </div>
                <div className="option-text" style={{
                  fontSize: '0.95rem',
                  marginLeft: '10px',
                  fontWeight: selectedOption === option.id ? 'bold' : 'normal'
                }}>{hasImageOption(option) ? '' : option.text}</div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default QuestionArea;