import React, { useEffect, useRef, useState } from 'react';

interface QuestionOption {
  id: number;
  text: string;
}

interface QuestionOptionImage {
  id: number;
  imageUrl: string;
}

export interface Question {
  id: number;
  display_index: number; // Make display_index required
  type: 'reading' | 'listening' | 'transition';
  text: string;
  subText: string;
  options: QuestionOption[] | QuestionOptionImage[];
  hasImageOptions?: boolean;
  audioUrl?: string; // URL tới file audio từ Google Drive
  correctAnswer?: number; // ID của đáp án đúng
  isTransitionScreen?: boolean; // Biểu thị đây là màn hình chuyển tiếp
  listeningCount?: number; // Số lượng câu hỏi listening
  start_at?: number; // Thời điểm bắt đầu của câu hỏi (giây)
  audioDuration?: number; // Thời lượng audio tính bằng giây (đư<PERSON><PERSON> tính từ start_at, không lưu trong DB)
  examListeningAudioUrl?: string; // Add this field to store the exam-level audio URL
}

interface QuestionAreaProps {
  question: Question;
  currentQuestionNumber: number;
  selectedOption?: number;
  onSelectOption: (optionId: number) => void;
  onAudioEnded?: () => void; // Callback khi audio kết thúc
  onNavigate?: (displayIndex: number) => void; // Callback để chuyển câu hỏi
  allQuestions?: Question[]; // Danh sách tất cả câu hỏi để tính toán thời điểm chuyển
  onAudioInfoUpdate?: (info: {
    fileName: string;
    fileSize: string;
    currentTime: number;
    duration: number;
    isPlaying: boolean;
    loadState: string;
    error?: string;
    url: string;
    readyState: number;
    networkState: number;
    bufferedRanges: string;
    bufferedPercent: number;
  } | null) => void; // Callback để cập nhật thông tin audio
}

const QuestionArea: React.FC<QuestionAreaProps> = ({
  question,
  currentQuestionNumber,
  selectedOption,
  onSelectOption,
  onAudioEnded,
  onNavigate,
  allQuestions,
  onAudioInfoUpdate
}) => {
  // Ref for audio element
  const audioRef = useRef<HTMLAudioElement>(null);
  const [showPlayButton, setShowPlayButton] = useState(false);
  const timeUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Hàm lấy thông tin file từ URL
  const getFileInfo = async (url: string) => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      const fileSize = contentLength ? formatFileSize(parseInt(contentLength)) : 'Unknown';
      const fileName = url.split('/').pop()?.split('?')[0] || 'audio.mp3';
      return { fileName, fileSize };
    } catch (error) {
      console.error('Error getting file info:', error);
      return { fileName: 'audio.mp3', fileSize: 'Unknown' };
    }
  };

  // Hàm format kích thước file
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Hàm cập nhật thông tin audio
  const updateAudioInfo = async (audioElement: HTMLAudioElement, isPlaying: boolean, error?: string) => {
    if (!onAudioInfoUpdate) return;

    const fileInfo = await getFileInfo(audioElement.src);

    // Xác định trạng thái tải
    let loadState = 'Unknown';
    if (error) {
      loadState = 'Error';
    } else if (audioElement.readyState === 4) {
      loadState = 'Fully Loaded';
    } else if (audioElement.readyState >= 2) {
      loadState = 'Loading...';
    } else if (audioElement.readyState === 1) {
      loadState = 'Metadata Loaded';
    } else {
      loadState = 'Not Loaded';
    }

    // Tính toán thông tin buffering
    let bufferedRanges = 'None';
    let bufferedPercent = 0;

    if (audioElement.buffered.length > 0) {
      const ranges = [];
      let totalBuffered = 0;

      for (let i = 0; i < audioElement.buffered.length; i++) {
        const start = audioElement.buffered.start(i);
        const end = audioElement.buffered.end(i);
        ranges.push(`${start.toFixed(1)}-${end.toFixed(1)}s`);
        totalBuffered += (end - start);
      }

      bufferedRanges = ranges.join(', ');

      if (audioElement.duration > 0) {
        bufferedPercent = (totalBuffered / audioElement.duration) * 100;
      }
    }

    console.log('[AUDIO DEBUG] Audio info update:', {
      fileName: fileInfo.fileName,
      fileSize: fileInfo.fileSize,
      currentTime: audioElement.currentTime,
      duration: audioElement.duration,
      isPlaying,
      loadState,
      readyState: audioElement.readyState,
      networkState: audioElement.networkState,
      bufferedRanges,
      bufferedPercent,
      error,
      url: audioElement.src
    });

    onAudioInfoUpdate({
      fileName: fileInfo.fileName,
      fileSize: fileInfo.fileSize,
      currentTime: audioElement.currentTime,
      duration: audioElement.duration || 0,
      isPlaying,
      loadState,
      error,
      url: audioElement.src,
      readyState: audioElement.readyState,
      networkState: audioElement.networkState,
      bufferedRanges,
      bufferedPercent
    });
  };

  // Hàm tính toán câu hỏi tiếp theo dựa trên thời gian audio
  const getNextQuestionByTime = (currentTime: number): Question | null => {
    if (!allQuestions || !onNavigate) return null;

    // Lọc các câu hỏi nghe và sắp xếp theo start_at
    const listeningQuestions = allQuestions
      .filter(q => q.type === 'listening' && q.start_at !== undefined)
      .sort((a, b) => (a.start_at || 0) - (b.start_at || 0));

    // Tìm câu hỏi có start_at gần nhất với thời gian hiện tại
    for (let i = 0; i < listeningQuestions.length; i++) {
      const q = listeningQuestions[i];
      const nextQ = listeningQuestions[i + 1];

      // Nếu là câu cuối cùng hoặc thời gian hiện tại chưa đến câu tiếp theo
      if (!nextQ || currentTime < (nextQ.start_at || 0)) {
        // Kiểm tra xem thời gian hiện tại có ở trong khoảng của câu hỏi này không
        if (currentTime >= (q.start_at || 0)) {
          return q;
        }
      }
    }

    return null;
  };

  // Hàm theo dõi thời gian audio và tự động chuyển câu hỏi
  const startTimeTracking = (audioElement: HTMLAudioElement) => {
    // Xóa interval cũ nếu có
    if (timeUpdateIntervalRef.current) {
      clearInterval(timeUpdateIntervalRef.current);
    }

    // Thêm các event listeners để monitor audio streaming
    const addStreamingMonitors = () => {
      audioElement.addEventListener('progress', () => {
        const buffered = audioElement.buffered;
        if (buffered.length > 0) {
          const bufferedEnd = buffered.end(buffered.length - 1);
          const bufferedStart = buffered.start(0);
          console.log(`[AUDIO STREAM] Buffered: ${bufferedStart.toFixed(2)}s - ${bufferedEnd.toFixed(2)}s (${bufferedEnd - bufferedStart}s total)`);
        }
      });

      audioElement.addEventListener('waiting', () => {
        console.log(`[AUDIO STREAM] Waiting for data at ${audioElement.currentTime.toFixed(2)}s`);
      });

      audioElement.addEventListener('canplay', () => {
        console.log(`[AUDIO STREAM] Can play at ${audioElement.currentTime.toFixed(2)}s`);
      });

      audioElement.addEventListener('canplaythrough', () => {
        console.log(`[AUDIO STREAM] Can play through at ${audioElement.currentTime.toFixed(2)}s`);
      });

      audioElement.addEventListener('stalled', () => {
        console.log(`[AUDIO STREAM] Stalled at ${audioElement.currentTime.toFixed(2)}s`);
        // Thử reload nếu bị stall quá lâu
        setTimeout(() => {
          if (audioElement.readyState < 3) {
            console.log(`[AUDIO STREAM] Still stalled after 5s, attempting reload`);
            const currentTime = audioElement.currentTime;
            const wasPlaying = !audioElement.paused;

            audioElement.load();

            audioElement.addEventListener('canplay', () => {
              audioElement.currentTime = currentTime;
              if (wasPlaying) {
                audioElement.play().catch(e => {
                  console.error('[AUDIO STREAM] Error resuming after reload:', e);
                });
              }
            }, { once: true });
          }
        }, 5000);
      });

      audioElement.addEventListener('suspend', () => {
        console.log(`[AUDIO STREAM] Suspended at ${audioElement.currentTime.toFixed(2)}s`);
      });

      audioElement.addEventListener('abort', () => {
        console.log(`[AUDIO STREAM] Aborted at ${audioElement.currentTime.toFixed(2)}s`);
      });

      audioElement.addEventListener('error', (e) => {
        console.error(`[AUDIO STREAM] Error at ${audioElement.currentTime.toFixed(2)}s:`, e, audioElement.error);
        if (audioElement.error) {
          console.error(`[AUDIO STREAM] Error details:`, {
            code: audioElement.error.code,
            message: audioElement.error.message,
            MEDIA_ERR_ABORTED: audioElement.error.MEDIA_ERR_ABORTED,
            MEDIA_ERR_NETWORK: audioElement.error.MEDIA_ERR_NETWORK,
            MEDIA_ERR_DECODE: audioElement.error.MEDIA_ERR_DECODE,
            MEDIA_ERR_SRC_NOT_SUPPORTED: audioElement.error.MEDIA_ERR_SRC_NOT_SUPPORTED
          });
        }
      });
    };

    // Chỉ thêm monitors một lần
    if (!audioElement.dataset.monitorsAdded) {
      addStreamingMonitors();
      audioElement.dataset.monitorsAdded = 'true';
    }

    timeUpdateIntervalRef.current = setInterval(() => {
      const currentTime = audioElement.currentTime;
      const nextQuestion = getNextQuestionByTime(currentTime);

      // Log chi tiết hơn mỗi 10 giây
      if (Math.floor(currentTime) % 10 === 0 && Math.floor(currentTime * 10) % 10 === 0) {
        console.log(`[AUDIO STREAM] Status at ${currentTime.toFixed(2)}s:`, {
          readyState: audioElement.readyState,
          networkState: audioElement.networkState,
          paused: audioElement.paused,
          ended: audioElement.ended,
          duration: audioElement.duration,
          bufferedRanges: audioElement.buffered.length,
          src: audioElement.src.substring(0, 100) + '...'
        });
      }

      // Cập nhật thông tin audio
      updateAudioInfo(audioElement, !audioElement.paused);

      if (nextQuestion && nextQuestion.display_index !== question.display_index) {
        console.log(`[AUDIO] Auto-switching to question ${nextQuestion.display_index} at time ${currentTime}s`);
        onNavigate?.(nextQuestion.display_index);
      }
    }, 500); // Kiểm tra mỗi 500ms
  };

  // Hàm dừng theo dõi thời gian
  const stopTimeTracking = () => {
    if (timeUpdateIntervalRef.current) {
      clearInterval(timeUpdateIntervalRef.current);
      timeUpdateIntervalRef.current = null;
    }
  };

  // Section icon or label based on question type
  const sectionLabel = question.type === 'reading'
    ? 'READING'
    : 'LISTENING';

  // Function to determine if option has image
  const hasImageOption = (option: any): option is QuestionOptionImage => {
    return 'imageUrl' in option;
  };

  // Reset audio khi chuyển câu hỏi
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current.src = '';
    }
    setShowPlayButton(false);
    console.log(`[AUDIO] Question changed to ${question.id}, reset audio`);
  }, [question.id]);

  // Tự động chuyển câu hỏi sau khi hết thời gian
  useEffect(() => {
    if (question.type === 'listening' && question.audioDuration && onAudioEnded) {
      const timer = setTimeout(() => {
        console.log(`[AUDIO] Question ${question.id} duration (${question.audioDuration}s) ended, moving to next question`);
        onAudioEnded();
      }, question.audioDuration * 1000);

      return () => clearTimeout(timer);
    }
  }, [question.id, question.type, question.audioDuration, onAudioEnded]);

  // Cleanup khi component unmount
  useEffect(() => {
    return () => {
      stopTimeTracking();
      // Xóa thông tin audio khi component unmount
      if (onAudioInfoUpdate) {
        onAudioInfoUpdate(null);
      }
    };
  }, []);

  // Xóa thông tin audio khi chuyển sang câu hỏi không phải listening
  useEffect(() => {
    if (question.type !== 'listening' && onAudioInfoUpdate) {
      onAudioInfoUpdate(null);
    }
  }, [question.type]);

  // Phát âm thanh khi câu hỏi listening hiển thị
  useEffect(() => {
    if (question.isTransitionScreen) {
      console.log(`[AUDIO] Question ${question.id} is transition screen, skipping audio`);
      return;
    }

    const playAudio = async () => {
      // Kiểm tra xem có phải là câu hỏi nghe đầu tiên không
      if (question.type === 'listening' && allQuestions) {
        const listeningQuestions = allQuestions
          .filter(q => q.type === 'listening')
          .sort((a, b) => (a.display_index || 0) - (b.display_index || 0));

        const isFirstListeningQuestion = listeningQuestions.length > 0 &&
          listeningQuestions[0].display_index === question.display_index;

        // Chỉ play audio cho câu hỏi nghe đầu tiên
        if (!isFirstListeningQuestion) {
          console.log(`[AUDIO] Question ${question.id} is not the first listening question, skipping audio play`);

          // Nếu có audio đang phát, chỉ cần theo dõi thời gian
          if (window.currentPlayingAudio && !window.currentPlayingAudio.paused) {
            console.log(`[AUDIO] Continuing to track existing audio for question ${question.id}`);
            startTimeTracking(window.currentPlayingAudio);
          }

          return;
        }
      }
      if (question.type === 'listening' && (question.audioUrl || question.examListeningAudioUrl) && audioRef.current) {
        try {
          // Kiểm tra xem có audio element đã tải trước không
          if (window.preloadedMainAudioElement && question.examListeningAudioUrl) {
            console.log(`[AUDIO DEBUG] Using preloaded main audio element for question ${question.id}`);

            const preloadedAudio = window.preloadedMainAudioElement;

            console.log(`[AUDIO DEBUG] Preloaded audio state:`, {
              src: preloadedAudio.src,
              readyState: preloadedAudio.readyState,
              networkState: preloadedAudio.networkState,
              duration: preloadedAudio.duration,
              currentTime: preloadedAudio.currentTime,
              paused: preloadedAudio.paused,
              ended: preloadedAudio.ended,
              error: preloadedAudio.error
            });

            // Đặt thời điểm bắt đầu phát
            if (question.start_at !== undefined && question.start_at > 0) {
              preloadedAudio.currentTime = question.start_at;
              console.log(`[AUDIO DEBUG] Setting start time to ${question.start_at}s for question ${question.id}`);
            } else {
              preloadedAudio.currentTime = 0;
            }

            try {
              // Phát ngay lập tức
              await preloadedAudio.play();
              setShowPlayButton(false);
              console.log(`[AUDIO DEBUG] Successfully playing preloaded audio for question ${question.id} from ${question.start_at || 0}s`);

              // Cập nhật thông tin audio ban đầu
              await updateAudioInfo(preloadedAudio, true);
            } catch (playError) {
              console.error(`[AUDIO DEBUG] Error playing preloaded audio:`, playError);
              await updateAudioInfo(preloadedAudio, false, playError.message);
              setShowPlayButton(true);
            }

            // Bắt đầu theo dõi thời gian để tự động chuyển câu hỏi
            startTimeTracking(preloadedAudio);

            // Lưu audio element vào window để các câu hỏi khác có thể truy cập
            window.currentPlayingAudio = preloadedAudio;

            // Đồng bộ với audioRef để các control khác hoạt động
            if (audioRef.current) {
              audioRef.current.src = preloadedAudio.src;
              audioRef.current.currentTime = preloadedAudio.currentTime;
            }

            return;
          }

          // Nếu không có URL đã tải trước, sử dụng phương pháp thông thường
          const audioUrl = question.audioUrl || question.examListeningAudioUrl;
          if (!audioUrl) return;

          console.log(`[AUDIO DEBUG] Fallback to normal audio loading for question ${question.id}`);

          try {
            // Import getPlayableAudioUrl function
            const utils = await import('../lib/utils');
            const getPlayableAudioUrl = utils.getPlayableAudioUrl;
            const extractGoogleDriveFileId = utils.extractGoogleDriveFileId;

            let playableUrl;

            try {
              // Get direct playable URL
              playableUrl = await getPlayableAudioUrl(audioUrl);
              console.log(`[AUDIO DEBUG] Got playable URL for question ${question.id}:`, playableUrl);
            } catch (urlError) {
              console.error(`[AUDIO DEBUG] Error getting playable URL, trying proxy:`, urlError);

              // Thử sử dụng proxy URL
              const fileId = extractGoogleDriveFileId(audioUrl);
              if (fileId) {
                playableUrl = `/api/proxy/gdrive?id=${fileId}`;
                console.log(`[AUDIO DEBUG] Using proxy URL for question ${question.id}:`, playableUrl);
              } else {
                throw new Error('Không thể trích xuất ID file và không thể sử dụng proxy');
              }
            }

            if (playableUrl && audioRef.current) {
              audioRef.current.src = playableUrl;
              audioRef.current.load();

              // Đợi cho audio sẵn sàng
              await new Promise((resolve, reject) => {
                const onCanPlay = () => {
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  resolve(void 0);
                };

                const onError = (e) => {
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  reject(new Error('Audio loading failed'));
                };

                audioRef.current?.addEventListener('canplay', onCanPlay);
                audioRef.current?.addEventListener('error', onError);

                // Timeout 5 giây
                setTimeout(() => {
                  audioRef.current?.removeEventListener('canplay', onCanPlay);
                  audioRef.current?.removeEventListener('error', onError);
                  resolve(void 0); // Tiếp tục dù chưa sẵn sàng hoàn toàn
                }, 5000);
              });

              // Nếu có start_at, đặt thời điểm bắt đầu phát
              if (question.start_at !== undefined && question.start_at > 0) {
                audioRef.current.currentTime = question.start_at;
                console.log(`[AUDIO DEBUG] Setting start time to ${question.start_at}s for question ${question.id}`);
              }

              await audioRef.current.play();
              setShowPlayButton(false);
              console.log(`[AUDIO DEBUG] Successfully playing fallback audio for question ${question.id}`);

              // Cập nhật thông tin audio
              await updateAudioInfo(audioRef.current, true);
            }
          } catch (fallbackError) {
            console.error(`[AUDIO DEBUG] Fallback audio loading failed for question ${question.id}:`, fallbackError);
            await updateAudioInfo(audioRef.current, false, fallbackError.message);
            setShowPlayButton(true);
          }
        } catch (err) {
          setShowPlayButton(true);
          console.error('[AUDIO] Không thể phát âm thanh tự động:', err);
        }
      }
    };

    playAudio();

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, [question.id, question.type, question.audioUrl, question.examListeningAudioUrl, question.isTransitionScreen]);

  // Nếu là màn hình chuyển tiếp, hiển thị giao diện đặc biệt
  if (question.isTransitionScreen) {
    return (
      <div className="question-area">
        <div className="transition-screen">
          <h1>{question.text}</h1>
          <div className="divider-line"></div>
          <p className="transition-note">{question.subText}</p>
        </div>
      </div>
    );
  }

  // Màn hình câu hỏi bình thường
  return (
    <div className="question-area">
      {/* Audio element for question */}
      <audio
        ref={audioRef}
        controls={false}
        className="hidden-audio"
      >
        <source src="" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
      {showPlayButton && question.type === 'listening' && allQuestions && (() => {
        const listeningQuestions = allQuestions
          .filter(q => q.type === 'listening')
          .sort((a, b) => (a.display_index || 0) - (b.display_index || 0));

        const isFirstListeningQuestion = listeningQuestions.length > 0 &&
          listeningQuestions[0].display_index === question.display_index;

        return isFirstListeningQuestion;
      })() && (
        <button
          style={{ margin: '10px 0', padding: '8px 16px', background: '#00468C', color: 'white', borderRadius: 6, border: 'none', fontWeight: 600, cursor: 'pointer' }}
          onClick={async () => {
            try {
              if (audioRef.current) {
                // Kiểm tra xem có phải là câu hỏi nghe đầu tiên không
                if (question.type === 'listening' && allQuestions) {
                  const listeningQuestions = allQuestions
                    .filter(q => q.type === 'listening')
                    .sort((a, b) => (a.display_index || 0) - (b.display_index || 0));

                  const isFirstListeningQuestion = listeningQuestions.length > 0 &&
                    listeningQuestions[0].display_index === question.display_index;

                  // Nếu không phải câu hỏi nghe đầu tiên, không cho phép play thủ công
                  if (!isFirstListeningQuestion) {
                    console.log(`[AUDIO] Manual play not allowed for non-first listening question ${question.id}`);
                    return;
                  }
                }

                // Kiểm tra xem có audio element đã tải trước không
                if (window.preloadedMainAudioElement && question.examListeningAudioUrl) {
                  console.log(`[AUDIO] Using preloaded main audio element for manual play of question ${question.id}`);

                  const preloadedAudio = window.preloadedMainAudioElement;

                  // Đặt thời điểm bắt đầu phát
                  if (question.start_at !== undefined && question.start_at > 0) {
                    preloadedAudio.currentTime = question.start_at;
                    console.log(`[AUDIO] Setting start time to ${question.start_at}s for manual play of question ${question.id}`);
                  } else {
                    preloadedAudio.currentTime = 0;
                  }

                  await preloadedAudio.play();
                  setShowPlayButton(false);

                  // Cập nhật thông tin audio ban đầu
                  await updateAudioInfo(preloadedAudio, true);

                  // Bắt đầu theo dõi thời gian để tự động chuyển câu hỏi
                  startTimeTracking(preloadedAudio);

                  // Lưu audio element vào window để các câu hỏi khác có thể truy cập
                  window.currentPlayingAudio = preloadedAudio;

                  return;
                }

                // Nếu không có URL đã tải trước, sử dụng phương pháp thông thường
                const audioUrl = question.audioUrl || question.examListeningAudioUrl;
                if (!audioUrl) return;

                // Import getPlayableAudioUrl function
                const utils = await import('../lib/utils');
                const getPlayableAudioUrl = utils.getPlayableAudioUrl;

                // Get direct playable URL
                const playableUrl = await getPlayableAudioUrl(audioUrl);
                console.log(`[AUDIO] Got playable URL for manual play of question ${question.id}`);

                if (playableUrl && audioRef.current.src !== playableUrl) {
                  audioRef.current.src = playableUrl;
                  audioRef.current.load();
                }

                // Nếu có start_at, đặt thời điểm bắt đầu phát
                if (question.start_at !== undefined && question.start_at > 0) {
                  audioRef.current.currentTime = question.start_at;
                  console.log(`[AUDIO] Setting start time to ${question.start_at}s for manual play of question ${question.id}`);
                }

                await audioRef.current.play();
                setShowPlayButton(false);
                console.log(`[AUDIO] User manually played audio for question ${question.id}`);
              }
            } catch (err) {
              setShowPlayButton(true);
              alert('Không thể phát file âm thanh. Vui lòng kiểm tra lại URL hoặc thử lại.');
              console.error('[AUDIO] Không thể phát âm thanh khi user click:', err);
            }
          }}
        >
          Phát lại audio
        </button>
      )}

      <div className="question-content">
        <div className="question-main-text">
          {currentQuestionNumber}. {question.text}
        </div>

        {question.subText && (
          <div className="question-subtext">
            {question.subText}
          </div>
        )}
      </div>

      {question.hasImageOptions ? (
        <div className="image-options-container">
          <div className="image-options-grid" style={{ textAlign: 'center' }}>
            {question.options.map((option: any) => (
              <div
                key={option.id}
                className={`image-option ${selectedOption === option.id ? 'selected' : ''}`}
                onClick={() => {
                  if (question.type === 'reading') {
                    onSelectOption(option.id);
                  } else if (question.type === 'listening') {
                    onSelectOption(option.id);
                  }
                }}
                style={{
                  cursor: 'pointer',
                  width: '66.7%',
                  height: '66.7%',
                  margin: '2px',
                  display: 'inline-block'
                }}
              >
                <img
                  src={option.imageUrl}
                  alt={`Option ${option.id}`}
                  className="option-image"
                  style={{ width: '100%', height: 'auto' }}
                />
                <div className={`image-option-circle ${selectedOption === option.id ? 'selected' : ''}`}>
                  <span className="option-number">{option.id}</span>
                  {selectedOption === option.id && (
                    <span className="option-check" style={{ color: '#00468C' }}>✓</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="options-container">
          <ul className="options-list">
            {question.options.map((option: any) => (
              <li
                key={option.id}
                className={`option-item ${selectedOption === option.id ? 'selected' : ''}`}
                onClick={() => {
                  if (question.type === 'reading') {
                    onSelectOption(option.id);
                  } else if (question.type === 'listening') {
                    onSelectOption(option.id);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                <div className={`option-circle ${selectedOption === option.id ? 'selected' : ''}`}>
                  <span className="option-number">{option.id}</span>
                  {selectedOption === option.id && (
                    <span className="option-check" style={{ color: '#00468C' }}>✓</span>
                  )}
                </div>
                <div className="option-text" style={{
                  fontSize: '0.95rem',
                  marginLeft: '10px',
                  fontWeight: selectedOption === option.id ? 'bold' : 'normal'
                }}>{hasImageOption(option) ? '' : option.text}</div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default QuestionArea;