import React from 'react';
import testEndImage from '../assets/test-end-image.png';
import '../assets/test-end.css';

interface TestEndScreenProps {
  applicationNo: string;
  seatNo: string;
  onClose: () => void;
}

const TestEndScreen: React.FC<TestEndScreenProps> = ({
  applicationNo,
  seatNo,
  onClose
}) => {
  return (
    <div className="test-end-container">
      <div className="test-end-header">
        <div className="test-end-title">
          <h1>EPS-TOPIK</h1>
          <p>Test of proficiency in Korean</p>
        </div>
        <div className="test-end-info">
          <div className="application-info">
            <span>Application No. </span>
            <span className="info-value">{applicationNo}</span>
          </div>
          <div className="seat-info">
            <span>Seat no. </span>
            <span className="info-value">{seatNo}</span>
          </div>
        </div>
      </div>
      
      <div className="test-end-content">
        <div className="test-end-image-frame">
          <div className="test-end-blue-header">
            Thank you for your efforts!
          </div>
          <div className="test-end-white-content">
            <div className="papers-icon"></div>
            <div className="test-end-instructions">
              <p className="instruction-item">✓ All test is done.</p>
              <p className="instruction-item">✓ Please wait quietly until supervisor give any direction.</p>
              <p className="instruction-item">✓ Please leave the room, after checking you have not left any personal belongings behind.</p>
            </div>
          </div>
        </div>
        
        <div className="test-end-actions">
          <button onClick={onClose} className="close-button">닫기</button>
        </div>
      </div>
    </div>
  );
};

export default TestEndScreen;