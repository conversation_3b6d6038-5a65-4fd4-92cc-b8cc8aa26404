import React, { useState, useEffect } from 'react';

interface TimerSectionProps {
  readingMinutes: number; // Thời gian phần Reading tính theo phút (cho hiển thị)
  listeningMinutes: number; // Thời gian phần Listening tính theo phút (cho hiển thị)
  readingSeconds?: number; // Thời gian phần Reading tính theo giây (chính xác hơn)
  listeningSeconds?: number; // Thời gian phần Listening tính theo giây (chính xác hơn)
  onTimeUp: () => void;
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: Record<number, number>;
  onNavigate: (index: number) => void;
  isReadingActive?: boolean; // Thêm prop này để component cha có thể kiểm soát
  questions?: Array<{ id: number, type: string, display_index?: number }>; // Danh sách câu hỏi với type và display_index
}

const TimerSection: React.FC<TimerSectionProps> = ({
  readingMinutes,
  listeningMinutes,
  readingSeconds,
  listeningSeconds,
  onTimeUp,
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  onNavigate,
  isReadingActive: externalIsReadingActive,
  questions = []
}) => {
  // Ưu tiên sử dụng giá trị theo giây nếu được cung cấp, ngược lại tính từ phút
  const [readingTimeRemaining, setReadingTimeRemaining] = useState(readingSeconds || readingMinutes * 60);
  const [listeningTimeRemaining, setListeningTimeRemaining] = useState(listeningSeconds || listeningMinutes * 60);
  const [internalIsReadingActive, setInternalIsReadingActive] = useState(true);

  // Sử dụng isReadingActive từ props nếu được cung cấp, ngược lại dùng state nội bộ
  const isReadingActive = externalIsReadingActive !== undefined ? externalIsReadingActive : internalIsReadingActive;

  // Không còn nút skip to listening nữa

  // Không cần xác định câu hỏi hiện tại thuộc nhóm nào nữa, vì isReadingActive đã được truyền vào từ component cha

  // Xử lý đồng hồ đếm thời gian
  useEffect(() => {
    const interval = setInterval(() => {
      if (isReadingActive) {
        // Nếu đang ở phần Reading và còn thời gian thì giảm thời gian
        if (readingTimeRemaining > 0) {
          setReadingTimeRemaining(prev => prev - 1);
        } else {
          // Nếu hết thời gian Reading, chuyển sang Listening
          setInternalIsReadingActive(false);
          console.log("Hết thời gian Reading, chuyển sang Listening");
        }
      } else {
        // Nếu đang ở phần Listening và còn thời gian thì giảm thời gian
        if (listeningTimeRemaining > 0) {
          setListeningTimeRemaining(prev => prev - 1);
        } else {
          // Hết thời gian Listening, dừng đồng hồ nhưng không tự động nộp bài
          clearInterval(interval);
          console.log("Hết thời gian Listening, cho phép nộp bài");
        }
      }
    }, 1000);

    // Cleanup interval khi component unmount
    return () => clearInterval(interval);
  }, [readingTimeRemaining, listeningTimeRemaining, isReadingActive, onTimeUp]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePrevious = () => {
    if (currentQuestion > 1 && isReadingActive) {
      onNavigate(currentQuestion - 1);
    }
  };

  const handleNext = () => {
    if (currentQuestion < totalQuestions && isReadingActive) {
      onNavigate(currentQuestion + 1);
    }
  };

  // Chuẩn hóa: lấy danh sách câu hỏi theo đúng thứ tự từ props questions
  const readingQuestions = questions.filter(q => q.type === 'reading');
  const listeningQuestions = questions.filter(q => q.type === 'listening');

  // Số lượng tối đa câu hỏi hiển thị trên mỗi cột - tùy chỉnh theo kích thước màn hình
  const MAX_CELLS_PER_COLUMN = 20; // Có thể hiển thị tối đa 20 câu hỏi

  // Tạo mảng cells cho hiển thị - chỉ hiển thị các câu hỏi thực tế, tối đa MAX_CELLS_PER_COLUMN câu
  const readingCells = readingQuestions
    .slice(0, MAX_CELLS_PER_COLUMN)
    .map(q => ({
      display_index: q.display_index,
      hasQuestion: true
    }));

  const listeningCells = listeningQuestions
    .slice(0, MAX_CELLS_PER_COLUMN)
    .map(q => ({
      display_index: q.display_index,
      hasQuestion: true
    }));

  return (
    <div className="timer-container">
      <div className="questions-table">
        <div className="questions-header">
          <div className="reading-column-header">
            <div>READING({readingMinutes} Min)</div>
            <div style={{ fontSize: '0.55rem', marginTop: '1px' }}>
              {readingQuestions.length > MAX_CELLS_PER_COLUMN ? MAX_CELLS_PER_COLUMN : readingQuestions.length}/{readingQuestions.length}Q
            </div>
          </div>
          <div className="listening-column-header">
            <div>LISTENING({listeningMinutes} Min)</div>
            <div style={{ fontSize: '0.55rem', marginTop: '1px' }}>
              {listeningQuestions.length > MAX_CELLS_PER_COLUMN ? MAX_CELLS_PER_COLUMN : listeningQuestions.length}/{listeningQuestions.length}Q
            </div>
          </div>
        </div>

        <div className="questions-time-displays">
          <div className="time-display reading-time-display">
            <div className="time-display-label">REMAIN TIME</div>
            <div className="time-display-value">{formatTime(readingTimeRemaining)}</div>
          </div>
          <div className="time-display listening-time-display">
            <div className="time-display-label">REMAIN TIME</div>
            <div className="time-display-value">{formatTime(listeningTimeRemaining)}</div>
          </div>
        </div>

        <div className="questions-grid">
          <div className="questions-column">
            {readingCells.map((cell) => {
              const displayIndex = cell.display_index;
              // Tìm id của câu hỏi theo display_index
              const question = questions.find(q => q.display_index === displayIndex);
              const id = question ? question.id : undefined;
              const isAnswered = typeof id === 'number' ? !!answeredQuestions[id] : false;
              const isCurrent = currentQuestion === displayIndex;
              return (
                <div
                  key={displayIndex}
                  className={`question-cell ${isCurrent ? 'current' : ''}`}
                  onClick={() => onNavigate(displayIndex as number)}
                >
                  <span>{displayIndex}</span>
                  {isAnswered && <span className="answer-dot"></span>}
                </div>
              );
            })}
          </div>
          <div className="questions-column">
            {listeningCells.map((cell) => {
              const displayIndex = cell.display_index;
              // Tìm id của câu hỏi theo display_index
              const question = questions.find(q => q.display_index === displayIndex);
              const id = question ? question.id : undefined;
              const isAnswered = typeof id === 'number' ? !!answeredQuestions[id] : false;
              const isCurrent = currentQuestion === displayIndex;
              return (
                <div
                  key={displayIndex}
                  className={`question-cell ${isCurrent ? 'current' : ''} ${!isReadingActive ? 'listening-active' : 'listening-inactive'}`}
                  onClick={() => !isReadingActive && onNavigate(displayIndex as number)}
                >
                  <span>{displayIndex}</span>
                  {isAnswered && <span className="answer-dot"></span>}
                </div>
              );
            })}
          </div>
        </div>

        {isReadingActive && (
          <div className="navigation-buttons">
            <button
              className="nav-button prev-button"
              onClick={handlePrevious}
              disabled={currentQuestion <= 1}
            >
              &laquo; PREVIOUS
            </button>
            <button
              className="nav-button next-button"
              onClick={handleNext}
              disabled={currentQuestion > readingQuestions.length} // Chỉ cho phép nhấn Next ở phần Reading
            >
              NEXT &raquo;
            </button>
          </div>
        )}


      </div>
    </div>
  );
};

export default TimerSection;