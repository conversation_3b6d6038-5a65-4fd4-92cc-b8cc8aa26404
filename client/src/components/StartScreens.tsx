import React, { useState, useEffect } from 'react';
import '../assets/start-screens.css';
import InformationScreen from './start_screens/InformationScreen';
import NoticeScreen from './start_screens/NoticeScreen';
import PracticeScreen from './start_screens/PracticeScreen';

interface StartScreensProps {
  applicationNo: string;
  seatNo: string;
  onComplete: () => void; // Callback khi hoàn thành toàn bộ luồng màn hình bắt đầu
}

// Các màn hình khác nhau trong luồng bắt đầu
enum ScreenType {
  INFORMATION_CHECK = 1,
  NOTICE_APPLICANT = 2, 
  PRACTICE_TEST = 3,
  FINAL_SCREEN = 4
}

const StartScreens: React.FC<StartScreensProps> = ({
  applicationNo,
  seatNo,
  onComplete
}) => {
  const [currentScreen, setCurrentScreen] = useState<ScreenType>(ScreenType.INFORMATION_CHECK);
  
  // Xử lý hiển thị màn hình cuối và tự động chuyển vào bài test
  useEffect(() => {
    if (currentScreen === ScreenType.FINAL_SCREEN) {
      // Đợi 3 giây rồi tự động chuyển vào bài test
      const timer = setTimeout(() => {
        onComplete();
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [currentScreen, onComplete]);
  
  // Xử lý chuyển màn hình
  const handleNext = () => {
    if (currentScreen < ScreenType.FINAL_SCREEN) {
      setCurrentScreen(prev => (prev + 1) as ScreenType);
    }
  };
  
  // Render màn hình dựa trên trạng thái hiện tại
  const renderScreen = () => {
    switch(currentScreen) {
      case ScreenType.INFORMATION_CHECK:
        return (
          <div className="start-screen-container">
            <InformationScreen applicationNo={applicationNo} seatNo={seatNo} />
            <div className="start-screen-footer">
              <button className="confirm-button" onClick={handleNext}>
                Confirm
              </button>
            </div>
          </div>
        );
      
      case ScreenType.NOTICE_APPLICANT:
        return (
          <div className="start-screen-container">
            <NoticeScreen />
            <div className="start-screen-footer">
              <button className="confirm-button" onClick={handleNext}>
                Confirm
              </button>
            </div>
          </div>
        );
      
      case ScreenType.PRACTICE_TEST:
        return (
          <div className="start-screen-container">
            <PracticeScreen />
            <div className="start-screen-footer">
              <button className="ready-button" onClick={handleNext}>
                Ready
              </button>
            </div>
          </div>
        );
      
      case ScreenType.FINAL_SCREEN:
        return (
          <div className="start-screen-container">
            <InformationScreen applicationNo={applicationNo} seatNo={seatNo} />
            {/* Không hiển thị nút ở màn hình cuối */}
          </div>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <div className="start-screens-container">
      {renderScreen()}
    </div>
  );
};

export default StartScreens;