import React, { useState, useEffect } from 'react';

interface SubmitButtonProps {
  disabled: boolean;
  onSubmit: () => void;
  timeRemaining?: number; // Thời gian còn lại cho phần thi
  isReadingActive?: boolean; // Đang ở phần Reading hay Listening
  lastListeningDone?: boolean; // Đánh dấu audio của câu hỏi listening cuối cùng đã phát xong
  onTestEnd?: () => void; // Callback khi cần hiển thị màn hình Test End
}

const SubmitButton: React.FC<SubmitButtonProps> = ({
  disabled,
  onSubmit,
  timeRemaining = 0,
  isReadingActive = true,
  lastListeningDone = false,
  onTestEnd
}) => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showTestShutdown, setShowTestShutdown] = useState(false);
  const [showTestEndScreen, setShowTestEndScreen] = useState(false);
  const [canSubmit, setCanSubmit] = useState(false);
  
  // Cập nhật trạng thái nút Submit dựa vào thời gian, phần thi và trạng thái audio cuối cùng
  useEffect(() => {
    // Chỉ cho phép submit khi đang ở câu listening cuối cùng
    if (!isReadingActive && lastListeningDone) {
      setCanSubmit(true);
    } else {
      setCanSubmit(false);
    }
  }, [isReadingActive, lastListeningDone]);
  
  const handleClick = () => {
    if (!canSubmit) return;
    
    // Bỏ popup xác nhận, chuyển thẳng sang thông báo "Try testing has been shut down"
    setShowTestShutdown(true);
  };
  
  // State để hiển thị thông báo khi chưa hoàn thành
  const [showNotFinished, setShowNotFinished] = useState(false);
  
  // Khi chưa hoàn thành hết các câu hỏi
  const handleNotFinishClick = () => {
    // Hiển thị thông báo Not Finish
    setShowNotFinished(true);
  };
  
  const handleConfirm = () => {
    setShowConfirmation(false);
    setShowTestShutdown(true); // Hiển thị "Try testing has been shut down"
  };
  
  const handleCancel = () => {
    setShowConfirmation(false);
  };
  
  // Xử lý khi nhấn OK trên thông báo "Try testing has been shut down"
  const handleTestShutdownOK = () => {
    setShowTestShutdown(false);
    // Nếu có callback onTestEnd từ parent component, gọi nó
    if (onTestEnd) {
      onTestEnd();
    } else {
      // Nếu không có callback, sử dụng popup cũ
      setShowTestEndScreen(true); 
    }
  };
  
  // Xử lý khi nhấn Cancel trên thông báo "Try testing has been shut down"
  const handleTestShutdownCancel = () => {
    setShowTestShutdown(false);
  };
  
  // Xử lý khi nhấn nút "닫기" (Đóng) trên màn hình "Test end"
  const handleTestEndClose = () => {
    setShowTestEndScreen(false);
    onSubmit(); // Chuyển sang màn hình kết quả
  };
  
  // Cập nhật tooltip message tùy theo trạng thái
  const getTooltipMessage = () => {
    if (isReadingActive) {
      return "You cannot submit while in the Reading section";
    } else if (!isReadingActive && !lastListeningDone && timeRemaining > 0) {
      return "Please wait until the last audio finishes playing";
    } else if (!isReadingActive && (lastListeningDone || timeRemaining <= 0)) {
      return "Click to submit your answers";
    }
    return "";
  };
  
  // Xử lý đóng thông báo chưa hoàn thành
  const handleCloseNotFinished = () => {
    setShowNotFinished(false);
  };

  return (
    <>
      <div className="submit-button-container" title={getTooltipMessage()}>
        <button 
          className={`submit-button ${canSubmit ? 'submit-active' : 'submit-inactive'}`}
          onClick={!canSubmit ? handleNotFinishClick : handleClick}
        >
          Submit Answers &nbsp;&raquo;
        </button>
      </div>
      
      {/* Màn hình xác nhận nộp bài - style theo popup noti_end*/}
      {showConfirmation && (
        <div className="confirmation-overlay">
          <div className="confirmation-dialog test-shutdown-dialog">
            <p>Are you sure you want to submit your test?</p>
            <div className="test-shutdown-buttons">
              <button 
                onClick={handleCancel}
              >
                Hủy
              </button>
              <button 
                onClick={handleConfirm}
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Màn hình thông báo chưa hoàn thành - style theo popup noti_end */}
      {showNotFinished && (
        <div className="confirmation-overlay">
          <div className="confirmation-dialog test-shutdown-dialog">
            <p>Solved all the problems, the answers can be submitted.</p>
            <div className="test-shutdown-buttons">
              <button 
                onClick={handleCloseNotFinished}
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Màn hình "Try testing has been shut down" theo hình ảnh đính kèm */}
      {showTestShutdown && (
        <div className="confirmation-overlay">
          <div className="confirmation-dialog test-shutdown-dialog">
            <p>Try testing has been shut down.</p>
            <div className="test-shutdown-buttons">
              <button 
                onClick={handleTestShutdownCancel}
              >
                Hủy
              </button>
              <button 
                onClick={handleTestShutdownOK}
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Màn hình "Test end" theo hình ảnh đính kèm */}
      {showTestEndScreen && (
        <div className="confirmation-overlay">
          <div className="test-end-screen">
            <div className="test-end-header">
              <div className="test-end-title">
                <h1>EPS-TOPIK</h1>
                <p>Test of proficiency in Korean</p>
              </div>
              <div className="test-end-info">
                <div className="application-info">
                  <span>Application No. </span>
                  <span className="info-value">00120135040001</span>
                </div>
                <div className="seat-info">
                  <span>Seat no. </span>
                  <span className="info-value">A-7</span>
                </div>
              </div>
            </div>
            
            <div className="test-end-content">
              <h2 className="thank-you-message">Thank you for your efforts!</h2>
              
              <div className="test-end-image">
                {/* Hình ảnh tờ giấy */}
              </div>
              
              <div className="test-end-instructions">
                <p>✓ All test is done.</p>
                <p>✓ Please wait quietly until supervisor give any direction.</p>
                <p>✓ Please leave the room, after checking you have not left any personal belongings behind.</p>
              </div>
              
              <div className="test-end-actions">
                <button onClick={handleTestEndClose}>닫기</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SubmitButton;