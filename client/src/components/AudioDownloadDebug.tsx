import React, { useState, useEffect } from 'react';
import { audioDownloadService, AudioDownloadProgress } from '../services/AudioDownloadService';

interface AudioDownloadDebugProps {
  downloadId: string;
  show: boolean;
}

const AudioDownloadDebug: React.FC<AudioDownloadDebugProps> = ({ downloadId, show }) => {
  const [progress, setProgress] = useState<AudioDownloadProgress>({
    status: 'idle',
    progress: 0,
    bytesLoaded: 0,
    bytesTotal: 0
  });

  useEffect(() => {
    if (!show) return;

    // Đăng ký callback để nhận updates
    audioDownloadService.onProgress(downloadId, setProgress);

    // Cleanup khi component unmount
    return () => {
      audioDownloadService.offProgress(downloadId);
    };
  }, [downloadId, show]);

  if (!show) return null;

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'idle': return '#6c757d';
      case 'fetching-url': return '#ffc107';
      case 'downloading': return '#007bff';
      case 'processing': return '#17a2b8';
      case 'completed': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'idle': return 'Idle';
      case 'fetching-url': return 'Getting URL...';
      case 'downloading': return 'Downloading...';
      case 'processing': return 'Processing...';
      case 'completed': return 'Completed';
      case 'error': return 'Error';
      default: return status;
    }
  };

  return (
    <div className="audio-download-debug">
      <div className="debug-header">
        <span className="debug-icon">🎵</span>
        <span className="debug-title">Audio Download</span>
      </div>
      
      <div className="debug-content">
        <div className="debug-row">
          <span className="debug-label">Status:</span>
          <span 
            className="debug-value status" 
            style={{ color: getStatusColor(progress.status) }}
          >
            {getStatusText(progress.status)}
          </span>
        </div>
        
        {progress.fileName && (
          <div className="debug-row">
            <span className="debug-label">File:</span>
            <span className="debug-value" title={progress.fileName}>
              {progress.fileName.length > 20 ? progress.fileName.substring(0, 20) + '...' : progress.fileName}
            </span>
          </div>
        )}
        
        <div className="debug-row">
          <span className="debug-label">Progress:</span>
          <span className="debug-value">{progress.progress.toFixed(1)}%</span>
        </div>
        
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ 
              width: `${progress.progress}%`,
              backgroundColor: getStatusColor(progress.status)
            }}
          />
        </div>
        
        {progress.bytesTotal > 0 && (
          <div className="debug-row">
            <span className="debug-label">Size:</span>
            <span className="debug-value">
              {formatBytes(progress.bytesLoaded)} / {formatBytes(progress.bytesTotal)}
            </span>
          </div>
        )}
        
        {progress.error && (
          <div className="debug-row">
            <span className="debug-label">Error:</span>
            <span className="debug-value error" title={progress.error}>
              {progress.error.length > 30 ? progress.error.substring(0, 30) + '...' : progress.error}
            </span>
          </div>
        )}
        
        {progress.url && (
          <div className="debug-row">
            <span className="debug-label">URL:</span>
            <span className="debug-value" title={progress.url}>
              {progress.url.length > 25 ? progress.url.substring(0, 25) + '...' : progress.url}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default AudioDownloadDebug;
