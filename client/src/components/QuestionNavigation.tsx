import React from 'react';

interface QuestionNavigationProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: Record<number, number>;
  onNavigate: (index: number) => void;
}

const QuestionNavigation: React.FC<QuestionNavigationProps> = ({
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  onNavigate
}) => {
  // Generate an array of question numbers (1 to totalQuestions)
  const questionNumbers = Array.from({ length: totalQuestions }, (_, i) => i + 1);
  
  // Split the questions into two columns
  const firstColumnQuestions = questionNumbers.slice(0, Math.ceil(totalQuestions / 2));
  const secondColumnQuestions = questionNumbers.slice(Math.ceil(totalQuestions / 2));
  
  const handlePrevious = () => {
    if (currentQuestion > 1) {
      onNavigate(currentQuestion - 1);
    }
  };
  
  const handleNext = () => {
    if (currentQuestion < totalQuestions) {
      onNavigate(currentQuestion + 1);
    }
  };
  
  const renderQuestionButton = (num: number) => {
    const isAnswered = !!answeredQuestions[num];
    const isCurrent = currentQuestion === num;
    
    return (
      <div 
        key={num}
        className={`question-button ${isAnswered ? 'answered' : ''} ${isCurrent ? 'current' : ''}`}
        onClick={() => onNavigate(num)}
      >
        {num}
      </div>
    );
  };
  
  return (
    <div className="question-navigation">
      <div className="navigation-header">
        <div className="navigation-title">Questions List</div>
      </div>
      
      <div className="two-column-grid">
        <div className="question-column">
          {firstColumnQuestions.map(num => renderQuestionButton(num))}
        </div>
        <div className="question-column">
          {secondColumnQuestions.map(num => renderQuestionButton(num))}
        </div>
      </div>
      
      <div className="navigation-buttons">
        <button 
          className="nav-button prev-button"
          onClick={handlePrevious}
          disabled={currentQuestion === 1}
        >
          ◀ Back
        </button>
        <button 
          className="nav-button next-button"
          onClick={handleNext}
          disabled={currentQuestion === totalQuestions}
        >
          Next ▶
        </button>
      </div>
    </div>
  );
};

export default QuestionNavigation;