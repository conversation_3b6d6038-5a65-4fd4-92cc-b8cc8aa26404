import React from 'react';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Card({ className = '', ...props }: CardProps) {
  return (
    <div
      className={`bg-white rounded-lg shadow-md p-6 ${className}`}
      {...props}
    />
  );
}

export function CardHeader({ className = '', ...props }: CardProps) {
  return (
    <div
      className={`pb-4 ${className}`}
      {...props}
    />
  );
}

export function CardTitle({ className = '', ...props }: CardProps) {
  return (
    <h3
      className={`text-xl font-semibold ${className}`}
      {...props}
    />
  );
}

export function CardDescription({ className = '', ...props }: CardProps) {
  return (
    <p
      className={`text-sm text-gray-500 ${className}`}
      {...props}
    />
  );
}

export function CardContent({ className = '', ...props }: CardProps) {
  return (
    <div
      className={`py-4 ${className}`}
      {...props}
    />
  );
}

export function CardFooter({ className = '', ...props }: CardProps) {
  return (
    <div
      className={`pt-4 ${className}`}
      {...props}
    />
  );
}
