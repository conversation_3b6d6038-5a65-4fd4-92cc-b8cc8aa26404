import React, { useEffect, useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FiBookOpen, FiSettings, FiUser } from 'react-icons/fi';
import { useAuth } from '@/hooks/use-auth';
import { BarChart2, FileText, Users, ChevronLeft, ChevronRight } from 'lucide-react';

const slidesData = [
  {
    title: "EPS-TOPIK Mock Test",
    description: "Prepare for your EPS-TOPIK exam with our authentic practice tests.",
    features: [
      {
        icon: <BarChart2 size={24} />,
        title: "Realistic Exam Simulation",
        description: "Experience the actual exam format and timing"
      },
      {
        icon: <FileText size={24} />,
        title: "Comprehensive Tests",
        description: "Practice both Reading and Listening sections"
      },
      {
        icon: <Users size={24} />,
        title: "Detailed Feedback",
        description: "Get instant scoring and performance analysis"
      }
    ]
  },
  {
    title: "Ready to Start Practicing?",
    description: "Join thousands of students preparing for their EPS-TOPIK exam",
    cta: true
  }
];

const LandingPage = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [availableExams, setAvailableExams] = useState<any[]>([]);
  const slideCount = slidesData.length;
  const autoPlayRef = useRef<number | null>(null);
  
  // Fetch available exams for Try a Test
  useEffect(() => {
    fetch('/api/exams/active')
      .then(res => res.json())
      .then(data => setAvailableExams(data || []));
  }, []);
  
  // Auto-play slider every 3.5 seconds
  useEffect(() => {
    if (autoPlayRef.current) clearInterval(autoPlayRef.current);
    autoPlayRef.current = window.setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slideCount);
    }, 3500);
    return () => {
      if (autoPlayRef.current) clearInterval(autoPlayRef.current);
    };
  }, [currentSlide, slideCount]);
  
  // Redirect to dashboard if already logged in
  useEffect(() => {
    if (user && !isLoading) {
      console.log('User already logged in, redirecting to dashboard');
      navigate('/dashboard');
    }
  }, [user, isLoading, navigate]);
  
  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slideCount);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slideCount) % slideCount);
  };
  
  // Handler for Try a Test
  const handleTryTest = () => {
    console.log('Try a Test clicked, navigating to exam/8');
    navigate('/exam/8');
  };

  return (
    <div className="landing-page">
      <div className="landing-header">
        <div className="logo">
          <h1>EPS-TOPIK</h1>
          <span>Test of proficiency in Korean</span>
        </div>
        <div className="auth-buttons">
          {user ? (
            <Link to="/dashboard" className="dashboard-button">
              <span>Go to Dashboard</span>
            </Link>
          ) : (
            <>
              <Link to="/login" className="login-button">
                <span>Log in</span>
              </Link>
              <Link to="/register" className="register-button">
                <span>Register</span>
              </Link>
            </>
          )}
        </div>
      </div>

      <div className="landing-content">
        <div className="slider-container">
          <button className="slider-button prev" onClick={prevSlide}>
            <ChevronLeft size={24} />
          </button>
          <div className="slider-track" style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
            {slidesData.map((slide, index) => (
              <div className="slide" key={index}>
                <div className="slide-content">
                  <h2>{slide.title}</h2>
                  <p>{slide.description}</p>
                  
                  {!slide.cta && slide.features ? (
                    <div className="features-grid">
                      {slide.features.map((feature, idx) => (
                        <div key={idx} className="feature-card">
                          <div className="feature-icon">
                            {feature.icon}
                          </div>
                          <h3>{feature.title}</h3>
                          <p>{feature.description}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="cta-buttons">
                      {user ? (
                        <Link to="/dashboard" className="primary-button">
                          <span>Go to Dashboard</span>
                        </Link>
                      ) : (
                        <>
                          <Link to="/login" className="secondary-button">
                            <span>Log in</span>
                          </Link>
                          <Link to="/register" className="primary-button">
                            <span>Register Now</span>
                          </Link>
                          {/* Try a Test button for anonymous users */}
                          {availableExams.length > 0 && (
                            <button className="primary-button" onClick={handleTryTest}>
                              Try a Test
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <button className="slider-button next" onClick={nextSlide}>
            <ChevronRight size={24} />
          </button>
        </div>
        
        <div className="slider-dots">
          {slidesData.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentSlide ? 'active' : ''}`}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default LandingPage;