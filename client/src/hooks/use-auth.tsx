import React, { create<PERSON>ontext, ReactNode, useContext } from "react";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { User } from "@shared/schema";
import { getQueryFn, apiRequest, queryClient } from "../lib/queryClient";
// import { useToast } from "../hooks/use-toast";

// Mock toast hook for now
const useToast = () => ({
  toast: (options: any) => {
    console.log('Toast:', options.title, options.description);
    // You can replace this with alert or any other notification method
    if (options.variant === 'destructive') {
      alert(`Error: ${options.title}\n${options.description || ''}`);
    } else {
      console.log(`Success: ${options.title}\n${options.description || ''}`);
    }
  }
});

interface LoginData {
  username: string;
  password: string;
}

interface RegisterData {
  username: string;
  password: string;
  email: string;
  fullName: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  googleLogin: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: user,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["/api/user"],
    queryFn: async () => {
      try {
        console.log("Checking authentication status...");

        // Check for JWT tokens first
        const accessToken = localStorage.getItem('accessToken');
        const refreshToken = localStorage.getItem('refreshToken');

        if (accessToken) {
          console.log("Found JWT access token, verifying...");
          const res = await fetch("/api/user", {
            headers: {
              'Authorization': `Bearer ${accessToken}`
            },
            credentials: "include",
          });

          if (res.ok) {
            const userData = await res.json() as User;
            console.log("Authenticated with JWT:", userData);
            return userData;
          } else if (res.status === 401 && refreshToken) {
            console.log("Access token expired, trying refresh...");
            // Try to refresh token
            try {
              const refreshRes = await fetch("/api/refresh-token", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ refreshToken }),
                credentials: "include",
              });

              if (refreshRes.ok) {
                const data = await refreshRes.json();
                localStorage.setItem("accessToken", data.accessToken);
                localStorage.setItem("refreshToken", data.refreshToken);

                // Retry with new token
                const retryRes = await fetch("/api/user", {
                  headers: {
                    'Authorization': `Bearer ${data.accessToken}`
                  },
                  credentials: "include",
                });

                if (retryRes.ok) {
                  const userData = await retryRes.json() as User;
                  console.log("Authenticated with refreshed JWT:", userData);
                  return userData;
                }
              }
            } catch (refreshError) {
              console.error("Token refresh failed:", refreshError);
              localStorage.removeItem('accessToken');
              localStorage.removeItem('refreshToken');
            }
          }
        }

        // Fallback to session-based auth
        console.log("Trying session-based authentication...");
        const res = await fetch("/api/user", {
          credentials: "include",
        });

        if (res.status === 401) {
          console.log("Not authenticated (401)");
          return null;
        }

        if (!res.ok) {
          const errorText = await res.text();
          console.error("Auth error:", res.status, errorText);
          throw new Error(errorText);
        }

        const userData = await res.json() as User;
        console.log("Authenticated with session:", userData);
        return userData;
      } catch (err) {
        console.error("Auth query error:", err);
        return null;
      }
    },
    retry: false,
    refetchOnWindowFocus: true
  });

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      console.log("Login attempt:", credentials.username);
      try {
        const res = await fetch("/api/login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(credentials),
          credentials: "include",
        });

        if (!res.ok) {
          const errorText = await res.text();
          console.error("Login error response:", res.status, errorText);
          throw new Error(errorText || "Login failed");
        }

        const userData = await res.json();
        console.log("Login success:", userData);
        return userData;
      } catch (err) {
        console.error("Login error:", err);
        throw err;
      }
    },
    onSuccess: (user: User) => {
      console.log("Login onSuccess:", user);
      // Update the query cache with the user data
      queryClient.setQueryData(["/api/user"], user);

      // Force a refetch of the user data to ensure we have the latest from the session
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });

      // Redirect based on user role
      const redirectPath = user.role === 'admin' ? '/admin' : '/dashboard';
      window.location.href = `${redirectPath}?t=${Date.now()}`; // Use direct navigation with cache busting

      toast({
        title: "Login successful",
        description: `Welcome back, ${user.fullName || user.username}!`,
      });
    },
    onError: (error: Error) => {
      console.error("Login onError:", error);
      toast({
        title: "Login failed",
        description: error.message || "Login failed",
        variant: "destructive",
      });
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (data: RegisterData) => {
      console.log("Registration attempt:", data.username);
      try {
        const res = await fetch("/api/register", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        });

        if (!res.ok) {
          const errorText = await res.text();
          console.error("Registration error response:", res.status, errorText);
          throw new Error(errorText || "Registration failed");
        }

        const userData = await res.json();
        console.log("Registration success:", userData);
        return userData;
      } catch (err) {
        console.error("Registration error:", err);
        throw err;
      }
    },
    onSuccess: (user: User) => {
      console.log("Registration onSuccess:", user);
      // Update the query cache with the user data
      queryClient.setQueryData(["/api/user"], user);

      // Force a refetch of the user data
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });

      // Redirect to dashboard
      window.location.href = "/dashboard";

      toast({
        title: "Registration successful",
        description: `Welcome, ${user.fullName || user.username}!`,
      });
    },
    onError: (error: Error) => {
      console.error("Registration onError:", error);
      toast({
        title: "Registration failed",
        description: error.message || "Registration failed",
        variant: "destructive",
      });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      try {
        const res = await fetch("/api/logout", {
          method: "POST",
          credentials: "include",
        });

        if (!res.ok) {
          throw new Error("Logout failed");
        }
      } catch (err) {
        console.error("Logout error:", err);
        throw err;
      }
    },
    onSuccess: () => {
      // Clear the user from the cache
      queryClient.setQueryData(["/api/user"], null);

      // Redirect to login page
      window.location.href = "/login";

      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
    },
    onError: (error: Error) => {
      console.error("Logout error:", error);
      toast({
        title: "Logout failed",
        description: error.message || "Logout failed",
        variant: "destructive",
      });
    },
  });

  const refreshTokenMutation = useMutation({
    mutationFn: async () => {
      try {
        const res = await fetch("/api/refresh-token", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ refreshToken: localStorage.getItem("refreshToken") }),
          credentials: "include",
        });

        if (!res.ok) {
          throw new Error("Token refresh failed");
        }

        const data = await res.json();
        localStorage.setItem("accessToken", data.accessToken);
        localStorage.setItem("refreshToken", data.refreshToken);
        return data;
      } catch (err) {
        console.error("Token refresh error:", err);
        throw err;
      }
    },
    onSuccess: () => {
      // Force a refetch of the user data
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });
    },
    onError: (error: Error) => {
      console.error("Token refresh error:", error);
      // Clear tokens and redirect to login on refresh failure
      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
      window.location.href = "/login";
    },
  });

  const googleLogin = () => {
    window.location.href = "/api/auth/google";
  };

  const value: AuthContextType = {
    user: user || null,
    isLoading,
    error,
    login: async (data) => {
      await loginMutation.mutateAsync(data);
    },
    register: async (data) => {
      await registerMutation.mutateAsync(data);
    },
    logout: async () => {
      await logoutMutation.mutateAsync();
    },
    refreshToken: async () => {
      await refreshTokenMutation.mutateAsync();
    },
    googleLogin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}