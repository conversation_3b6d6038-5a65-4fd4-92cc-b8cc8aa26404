-- EPS-TOPIK Mock Test System Database Backup
-- Created: May 02, 2025
-- This file contains schema and data for the EPS-TOPIK Mock Test System

-- Start by dropping existing tables if they exist
DROP TABLE IF EXISTS exam_attempts;
DROP TABLE IF EXISTS exams;
DROP TABLE IF EXISTS refresh_tokens;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS exam_result_details;

-- Drop existing types
DROP TYPE IF EXISTS role;
DROP TYPE IF EXISTS status;
DROP TYPE IF EXISTS auth_provider;

-- <PERSON>reate custom types
CREATE TYPE role AS ENUM ('user', 'admin');
CREATE TYPE status AS ENUM ('active', 'inactive', 'pending');
CREATE TYPE auth_provider AS ENUM ('local', 'google');

-- Create users table with OAuth and JWT support
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password TEXT, -- Can be NULL for OAuth users
    full_name TEXT,
    role role NOT NULL DEFAULT 'user',
    status status NOT NULL DEFAULT 'active',
    auth_provider auth_provider NOT NULL DEFAULT 'local',
    google_id TEXT UNIQUE, -- Google OAuth ID
    profile_picture TEXT, -- URL to profile picture
    last_login TIMESTAMP,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create refresh tokens table for JWT
CREATE TABLE refresh_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    revoked BOOLEAN NOT NULL DEFAULT FALSE,
    device_info TEXT -- Store device/browser information
);

-- Create exams table
CREATE TABLE exams (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    duration INTEGER NOT NULL,
    reading_time INTEGER NOT NULL,
    listening_time INTEGER NOT NULL,
    questions JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_by INTEGER REFERENCES users(id)
);

-- Create exam_attempts table
CREATE TABLE exam_attempts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) NOT NULL,
    exam_id INTEGER REFERENCES exams(id) NOT NULL,
    answers JSONB,
    score INTEGER,
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    started_at TIMESTAMP NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Create exam_result_details table
CREATE TABLE exam_result_details (
    id SERIAL PRIMARY KEY,
    attempt_id INTEGER NOT NULL REFERENCES exam_attempts(id) ON DELETE CASCADE,
    question_id INTEGER NOT NULL,
    question_text TEXT NOT NULL,
    question_type TEXT NOT NULL,
    user_answer INTEGER,
    correct_answer INTEGER NOT NULL,
    is_correct BOOLEAN NOT NULL,
    options JSONB NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Insert mock users (including both local and Google OAuth users)
INSERT INTO users (username, email, password, full_name, role, status, auth_provider, google_id, email_verified) VALUES
-- Local admin user
('admin', '<EMAIL>', '713bfda78870bf9d1b261f565286f85e97ee614efe5f0faf7c34e7ca4f65baca', 'Admin User', 'admin', 'active', 'local', NULL, true),
-- Local regular user
('user1', '<EMAIL>', '0b14d501a594442a01c6859541bcb3e8164d183d32937b851835442f69d5c94e', 'Regular User 1', 'user', 'active', 'local', NULL, true),
-- Google OAuth user
('user2', '<EMAIL>', NULL, 'Google User', 'user', 'active', 'google', '116275262277591123069', true);

-- Insert mock exam with questions
INSERT INTO exams (title, description, duration, reading_time, listening_time, questions, created_by) VALUES
('EPS-TOPIK 기출 모의고사 1회', 
 '2023년 EPS-TOPIK 기출문제 기반 모의고사입니다. 읽기와 듣기 섹션을 포함합니다.',
 47, 40, 7,
 '[
    {
        "id": 1,
        "text": "다음 중 ''안전벨트''를 나타내는 표지는 무엇입니까?",
        "type": "reading",
        "options": [
            {"id": 1, "text": "안전조끼"},
            {"id": 2, "text": "안전모"},
            {"id": 3, "text": "안전벨트"},
            {"id": 4, "text": "소화기"}
        ],
        "correctAnswer": 3
    },
    {
        "id": 2,
        "text": "다음 문장의 뜻으로 가장 알맞은 것을 고르십시오.",
        "type": "reading",
        "options": [
            {"id": 1, "text": "작업장에서는 안전모를 벗어도 됩니다."},
            {"id": 2, "text": "작업장에 들어가기 전에 안전모를 벗어야 합니다."},
            {"id": 3, "text": "작업장에서는 반드시 안전모를 써야 합니다."},
            {"id": 4, "text": "작업장에서는 안전모를 쓰지 않아도 됩니다."}
        ],
        "correctAnswer": 3
    }
]',
 1);

-- Insert mock exam attempts
INSERT INTO exam_attempts (user_id, exam_id, answers, score, completed, completed_at) VALUES
(2, 1, '{"1": 3, "2": 3}', 100, true, NOW()),
(3, 1, '{"1": 1, "2": 2}', 50, true, NOW());

-- Add indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_google_id ON users(google_id);
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX idx_exam_attempts_user_id ON exam_attempts(user_id);
CREATE INDEX idx_exam_attempts_exam_id ON exam_attempts(exam_id);

-- Add comments to tables
COMMENT ON TABLE users IS 'Stores user information including authentication details and OAuth data';
COMMENT ON TABLE refresh_tokens IS 'Stores JWT refresh tokens for persistent sessions';
COMMENT ON TABLE exams IS 'Contains exam information and questions';
COMMENT ON TABLE exam_attempts IS 'Tracks user exam attempts and results';
COMMENT ON TABLE exam_result_details IS 'Stores detailed information about each question in an exam attempt';

-- Successfully completed database backup