#!/bin/bash

# Kill existing servers if running
echo "Checking for existing servers..."

# Kill backend server if running
if [ -f backend.pid ]; then
  BACKEND_PID=$(cat backend.pid)
  if ps -p $BACKEND_PID > /dev/null; then
    echo "Killing existing backend server (PID: $BACKEND_PID)..."
    kill $BACKEND_PID
    sleep 1
    # Force kill if still running
    if ps -p $BACKEND_PID > /dev/null; then
      echo "Force killing backend server..."
      kill -9 $BACKEND_PID
    fi
  else
    echo "No running backend server found with PID: $BACKEND_PID"
  fi
  rm backend.pid
fi

# Kill frontend server if running
if [ -f frontend.pid ]; then
  FRONTEND_PID=$(cat frontend.pid)
  if ps -p $FRONTEND_PID > /dev/null; then
    echo "Killing existing frontend server (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID
    sleep 1
    # Force kill if still running
    if ps -p $FRONTEND_PID > /dev/null; then
      echo "Force killing frontend server..."
      kill -9 $FRONTEND_PID
    fi
  else
    echo "No running frontend server found with PID: $FRONTEND_PID"
  fi
  rm frontend.pid
fi

# Kill any other Node.js processes that might be running on ports 3000 (backend) and 5173 (frontend)
echo "Checking for processes on ports 3000 and 5173..."
BACKEND_PORT_PID=$(lsof -ti:3000)
if [ ! -z "$BACKEND_PORT_PID" ]; then
  echo "Killing process on port 3000 (PID: $BACKEND_PORT_PID)..."
  kill -9 $BACKEND_PORT_PID
fi

FRONTEND_PORT_PID=$(lsof -ti:5173)
if [ ! -z "$FRONTEND_PORT_PID" ]; then
  echo "Killing process on port 5173 (PID: $FRONTEND_PORT_PID)..."
  kill -9 $FRONTEND_PORT_PID
fi

# Clean up Vite cache
echo "Cleaning Vite cache..."
rm -rf node_modules/.vite

# Start backend server
echo "Starting backend server..."
export DATABASE_URL="postgres://postgres@localhost:5432/eps_simulator"
export NODE_ENV=development
nohup npx tsx server/index.ts > backend.log 2>&1 &
echo $! > backend.pid

# Start frontend server
echo "Starting frontend server..."
export NODE_ENV=development
nohup npx vite > frontend.log 2>&1 &
echo $! > frontend.pid

echo "Servers started. Check backend.log and frontend.log for output."